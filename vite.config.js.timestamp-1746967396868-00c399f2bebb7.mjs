// vite.config.js
import { fileURLToPath, URL } from "node:url";
import { defineConfig } from "file:///home/<USER>/Documents/workbench/NEWALERT/node_modules/vite/dist/node/index.js";
import vue from "file:///home/<USER>/Documents/workbench/NEWALERT/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///home/<USER>/Documents/workbench/NEWALERT/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import vueDevTools from "file:///home/<USER>/Documents/workbench/NEWALERT/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
var __vite_injected_original_import_meta_url = "file:///home/<USER>/Documents/workbench/NEWALERT/vite.config.js";
var vite_config_default = defineConfig({
  plugins: [vue(), vueJsx(), vueDevTools()],
  // server:{
  //   port: 3000,
  // },
  envPrefix: "v_",
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url)),
      "@components": fileURLToPath(
        new URL("./src/components", __vite_injected_original_import_meta_url)
      ),
      "@features": fileURLToPath(new URL("./src/features", __vite_injected_original_import_meta_url)),
      "@paper_prescription": fileURLToPath(
        new URL("./src/features/paper_prescription", __vite_injected_original_import_meta_url)
      ),
      "@cashier": fileURLToPath(
        new URL("./src/features/cashier", __vite_injected_original_import_meta_url)
      ),
      "@dispense": fileURLToPath(
        new URL("./src/features/dispense", __vite_injected_original_import_meta_url)
      ),
      "@utils": fileURLToPath(new URL("./src/utils", __vite_injected_original_import_meta_url)),
      "@stores": fileURLToPath(new URL("./src/stores", __vite_injected_original_import_meta_url)),
      "@skeletons": fileURLToPath(new URL("./src/skeletons", __vite_injected_original_import_meta_url)),
      "@directives": fileURLToPath(
        new URL("./src/directives", __vite_injected_original_import_meta_url)
      )
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
