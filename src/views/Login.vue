<script setup>
import InputPassword from "@/components/new_form_elements/InputPassword.vue";
import { Form, Input } from "@components/new_form_elements";
import { Icon } from "@iconify/vue";
import { useRoute, useRouter } from "vue-router";
import { useAuth } from "@/stores/auth";
import { useApiRequest } from "@/composables/useApiRequest";
import { login } from "./api/auth";
import { ref } from "vue";
import { toasted } from "@/utils/utils";

const router = useRouter();
const auth = useAuth();
const route = useRoute();

function reRoute(role) {
  if (role == "cashier") {
    router.push("/cashier");
  } else if (["pharmacist"].includes(role)) {
    router.push("/paper_prescription");
  } else if (role == "payer") {
    router.push("/institution");
  } else if (role == "admin") {
    router.push("/dashboard");
  } else if (role == "storekeeper") {
    router.push("/store");
  } else if (role == "doctor") {
    router.push("/doctor");
  } else if (role == "superadmin") {
    router.push("/institutions");
  } else if (role == "registrar") {
    router.push("/registrar");
  } else if (role == "case team leader") {
    router.push("/issued-batch");
  } else if (role == "emr") {
    router.push("/paper_prescription");
  }
}

const redirecting = ref(true);

const loginReq = useApiRequest();
function loginUser({ values }) {
  if (loginReq.pending.value) return;

  loginReq.send(
    () => login(values),
    (res) => {
      if (res.success) {
        auth.setAuth({
          user: { ...res.data, roleName: res.data.roleName.toLowerCase() },
          accessToken: res.data?.token,
        });
        localStorage.setItem(
          "userDetail",
          JSON.stringify({
            ...res.data,
            roleName: res.data.roleName.toLowerCase(),
          })
        );
        reRoute(res.data.roleName.toLowerCase());
      }
      toasted(res.success, "Successfully Logged In", res.error);
    }
  );
}
</script>
<template>
  <div class="h-full flex flex-col lg:flex-row">
    <div
      class="flex items-center w-full lg:w-1/2 !bg-red-500 bg-patter lg:h-full h-1/2"
    >
      <div
        class="flex flex-col backdrop-blur-lg h-[30rem] bg-[#ffffff66] px-16 justify-center place-items-center text-white"
      >
        <div
          class="w-full pt-4 pb-4 lg:leading-[5rem] leading-[3rem] text-[3rem] place-items-center lg:text-[5.5rem] font-extrabold"
        >
          Helping Society get Healthier!
        </div>
        <div>
          <p class="max-w-[66%] text-sm lg:text-base">
            Ensuring every resident of Addis Ababa has access to high-quality
            medicines and medical supplies. we are committed to delivering safe,
            effective, and affordable healthcare solutions within our available
            resources.
          </p>
        </div>
      </div>
    </div>
    <div
      class="lg:w-1/2 w-full isolate lg:h-full bg-patter2 relative h-1/2 show-scrollbar overflow-auto flex items-center justify-center"
    >
      <div class="min-h-full absolute inset-0 -z-10 bg-white/70"></div>
      <Form
        v-slot="{ submit }"
        class="flex flex-col gap-4 bg-white p-12 shadow-white rounded-md"
        id="login"
      >
        <div class="flex items-center gap-4 flex-col">
          <img
            class="object-cover w-[8rem] mt-10"
            src="/src/assets/img/kenema_logo.svg"
          />
          <p class="font-bold text-lg">Login</p>
          <div class="text-center opacity-60">
            Upon successful authentication, access your role-
            <p>specific dashboard within the eRx platform.</p>
          </div>
        </div>

        <Input
          name="email"
          validation="required"
          :attributes="{
            placeholder: 'Email',
          }"
          label="Email"
        />
        <InputPassword
          name="password"
          validation="required"
          :attributes="{
            placeholder: 'password',
          }"
          label="password"
        />
        <button
          type="button"
          @click.prevent="router.push('/forgetpassword')"
          class="text-left text-blue-600"
        >
          Forget Password?
        </button>
        <button
          type="submit"
          @click.prevent="submit(loginUser)"
          class="w-full cursor-pointer justify-center text-md flex bg-primary rounded-md text-white font-bold py-4"
        >
          <div v-if="loginReq.pending.value">
            <Icon icon="svg-spinners:3-dots-scale" class="text-2xl" />
          </div>
          <div v-if="!loginReq.pending.value">Login</div>
        </button>
      </Form>
    </div>
  </div>
</template>

<style>
.bg-patter {
  background-image: url("/src/assets/img/pattern4.svg"),
    linear-gradient(258.86deg, #ed6033 41.57%, #eb0004 107.4%) !important;
}

.bg-patter2,
.bg-patter {
  background-color: #fff;
  background-image: url("/src/assets/img/pattern4.svg");
}
</style>
tter {
