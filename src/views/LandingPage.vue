<script setup>
import navs from "@/config/nav2";
import navs3 from "@/config/nav3";
import icons from "@/utils/icons";
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

const show = ref(false);
function scrollHandler(currentPosition, position) {
  if (currentPosition >= position) {
    show.value = true;
  } else {
    show.value = false;
  }
}

const route = useRoute();
const open = ref(false);

const menuOpen = () => {
  open.value = !open.value;
};

const router = useRouter();

const homeSection = ref(null);
const aboutSection = ref(null);
const locationSection = ref(null);
const serviceSection = ref(null);
const departmentSection = ref(null);
const policiesSection = ref(null);

// const scrollToSection = (section) => {
//     console.log("ghjj", section.value);

//     if (section.value) {
//         section.value.scrollIntoView({ behavior: 'smooth', block: 'start' });
//     }
// };

// // Function to handle navigation
// function handleNavigation(navItem) {
//     if (navItem.path === '/home') {
//         scrollToSection(homeSection);
//     } else if (navItem.path === '/about') {
//         scrollToSection(aboutSection);
//     } else if (navItem.path === '/location') {
//         scrollToSection(locationSection);
//     } else if (navItem.path === '/services') {
//         scrollToSection(serviceSection);
//     } else {
//         router.push(navItem.path);
//     }
// };

const handleQuickLinkClick = (event) => {
  const link = event.currentTarget; // Get the clicked link

  // Remove 'front' class from other links
  const quickLinks = document.querySelectorAll(".Quick-link");
  quickLinks.forEach((otherLink) => {
    if (otherLink !== link) {
      otherLink.classList.remove("front");
    }
  });

  // Toggle the 'front' class on the clicked link
  link.classList.toggle("front");
};

const scrollDirection = ref("down");
let lastScrollTop = 0;

onMounted(() => {
  window.addEventListener("scroll", () => {
    const currentScroll = window.scrollY;

    scrollDirection.value = currentScroll > lastScrollTop ? "down" : "up";
    lastScrollTop = currentScroll <= 0 ? 0 : currentScroll;
  });
});

const animatedCards = ref([]);

function handleIntersect(entries) {
  entries.forEach((entry) => {
    if (entry.isIntersecting) {
      const card = entry.target;
      if (!card.classList.contains("animated")) {
        card.classList.add(
          "animate__animated",
          scrollDirection.value === "down"
            ? "animate__fadeInRight"
            : "animate__fadeInLeft",
          "animated"
        );
      }
    }
  });
}

onMounted(() => {
  const observer = new IntersectionObserver(handleIntersect, {
    threshold: 0.2,
  });

  animatedCards.value = document.querySelectorAll(".animated-card");

  animatedCards.value.forEach((card) => observer.observe(card));
});
</script>

<template>
  <div
    v-scroll-position.40="scrollHandler"
    class="h-full w-full flex flex-col overflow-auto"
  >
    <header
      :class="['fixed top-0 w-full z-50 bg-white', show ? 'shadow-lg' : '']"
      class="flex justify-between items-center bg-white shadow-md py-4 px-5 lg-px-24 sticky top-0"
    >
      <div>
        <img
          class="object-cover w-10 md:w-[5rem] pr-3 pl-5"
          src="/src/assets/img/kenema_logo.svg"
        />
      </div>
      <div
        class="text-4xl w-full text-black cursor-pointe mdd:hidden z-40"
        @click="menuOpen"
      >
        <i
          class="absolute left-0 text-4xl"
          :class="[open ? 'bi bi-x' : 'bi bi-list']"
        ></i>
      </div>
      <div
        :class="[open ? 'top-0' : 'top-[-390%]']"
        class="flex flex-col md:flex-row p-5 gap-2 md:gap-4 px-10 mdd:static absolute bg-white shadow-md md:shadow-none md:w-auto my-7 md:my-0 left-0 w-full duration-500"
      >
        <nav
          v-for="nav in navs"
          :key="nav.name"
          v-privilage.role="(nav.role || '').toLowerCase()"
        >
          <a
            :class="[route.hash == nav.path && 'active-link']"
            :href="nav.path"
            class="rounded px-4 p-2 hover:bg-primary cursor-pointer hover:text-white"
          >
            {{ nav.name }}
          </a>
        </nav>
      </div>
      <div class="flex flex-col items-center pr-5">
        <button
          @click="router.push('/login')"
          class="bg-primary px-4 py-2 rounded-md text-white"
        >
          Login
        </button>
      </div>
    </header>

    <div class="flex md:flex-row sm:flex-col gap-2">
      <div class="relative md:w-2/3 sm:w-full min-h-full bg-primary z-10">
        <div class="absolute rounded-full bg-gradient z-[-1]"></div>
        <div
          class="flex flex-col sm:px-[5rem] md:px-[10rem] h-[55rem] justify-center place-items-center items-center text-white"
        >
          <h1
            id="home"
            class="lg:leading-[5rem] leading-[3rem] text-[3rem] flex place-items-center items-center lg:text-[4rem] font-montserrat font-black uppercase"
          >
            Welcome to Alert comprehensive specialized <br />
            hospital
          </h1>
          <div>
            <div class="absolute bg-gradient1 rounded-full z-[-1]"></div>
            <p
              class="max-w-[66%] sm:text-sm pt-10 font-medium leading-[160%] md:text-lg"
            >
              Ensuring every resident of Addis Ababa has access to high-quality
              medicines and medical supplies. we are committed to delivering
              safe, effective, and affordable healthcare solutions within our
              available resources.
            </p>
            <div class="absolute bg-gradient2 rounded-full z-[-1]"></div>
            <div class="flex gap-6 pt-6">
              <button
                @click="router.push('/login')"
                class="bg-white px-11 py-5 rounded-md text-primary hover:bg-primary hover:text-white"
              >
                Login
              </button>
              <div class="absolute bg-gradient3 rounded-full z-[-1]"></div>
              <div
                class="px-8 py-5 rounded-md border border-[#F2F5F9] text-white hover:bg-white hover:text-primary"
              >
                <nav
                  v-for="nav in navs3"
                  :key="nav.name"
                  v-privilage.role="(nav.role || '').toLowerCase()"
                >
                  <a :href="nav.path"> Our Location </a>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="w-1/3 bg-white hidden md:block">
        <img class="object-cover h-[55rem]" src="/src/assets/img/pill.jpeg" />
      </div>
    </div>

    <div class="w-full px-40 py-32">
      <h2 id="services" class="text-primary text-2xl pb-7 font-plusJakarta">
        Services
      </h2>
      <div
        class="animated-card grid md:grid-cols-3 sm:grid-col-1 md:gap-8 sm:gap-4"
      >
        <div id="arrow">
          <div
            class="bg-[#A0E2E1] rounded-s-lg p-6 justify-between place-items-center items-center flex flex-col"
          >
            <i class="pb-4" v-html="icons.capsule" />
            <p
              class="text-primary font-bold text-2xl text-center leading-8 tracking-[-1%] pb-4"
            >
              Stocks Medications
            </p>
            <p
              class="w-[19rem] text-primary font-medium text-base text-center leading-6"
            >
              A reliable supply of high-quality medicines for all healthcare
              needs.
            </p>
          </div>
        </div>
        <div id="arrow">
          <div
            class="bg-[#C4F7E6] rounded-s-lg p-6 justify-between place-items-center items-center flex flex-col"
          >
            <i class="pb-4" v-html="icons.tube" />
            <p
              class="text-primary font-bold text-2xl text-center leading-8 tracking-[-1%] pb-4"
            >
              Compounding Medications
            </p>
            <p
              class="w-[19rem] text-primary font-medium text-base text-center leading-6"
            >
              Tailoring personalized medications to meet specific patient
              requirements.
            </p>
          </div>
        </div>
        <div id="arrow">
          <div
            class="bg-[#A0E2E1] rounded-s-lg p-6 justify-between place-items-center items-center flex flex-col"
          >
            <i class="pb-4" v-html="icons.allhands" />
            <p
              class="text-primary font-bold text-2xl text-center leading-8 tracking-[-1%] pb-4"
            >
              Consulting
            </p>
            <p
              class="w-[19rem] text-primary font-medium text-base text-center leading-6"
            >
              Providing expert medical guidance for informed healthcare
              decisions.
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="w-full px-40 py-32">
      <div class="grid md:grid-cols-2 sm:grid-col-1 md:gap-20 sm:gap-10">
        <div class="flex flex-col gap-6">
          <h2
            id="about"
            class="font-bold text-3xl leading-[3rem] tracking-[-2%]"
          >
            About ALERT
          </h2>
          <div
            class="grid grid-rows-3 gap-6 items-center font-medium text-[18px] leading-[174%] text-[#5D6A85]"
          >
            <p class="">
              Experience excellence in pharmaceutical care at our dedicated
              pharmacy, where we offer a comprehensive range of essential
              services tailored to meet your healthcare needs. Our dedicated
              team ensures the highest standards in medication management by
              providing services such as dispensing medications with proper
              labeling, counseling on usage and potential side effects,
              compounding customized medications, medication therapy management,
              and reliable drug information.
            </p>

            <p class="">
              Convenience is key, and our pharmacy strives to make access to a
              variety of medications seamless. We specialize in advising on
              antimicrobial drugs and extend our services to include clinical
              pharmacy services for disease management and medication
              monitoring. At our pharmacy, your well-being is our priority, and
              we are committed to meeting your needs through expert care,
              affordable high-quality products, and a wide array of services
              available for inpatient, outpatient, and emergency situations.
            </p>
            <p class="">
              Whether you require specialized compounding, comprehensive drug
              information, or round-the-clock accessibility, our pharmacy is
              dedicated to providing the highest level of pharmaceutical
              services for your convenience and peace of mind. Trust us to be
              your partner in health, delivering the care you deserve.
            </p>
          </div>
        </div>
        <div class="flex justify-end front">
          <img
            class="object-cover object-left"
            src="/src/assets/img/Alertttt.jpeg"
          />
        </div>
      </div>
    </div>

    <div class="w-full h-[25rem] bg-primary">
      <div class="flex px-40 py-32 gap-6">
        <i class="tetx-[#18E4A1]" v-html="icons.care" />
        <div class="flex flex-col gap-4">
          <h1
            class="text-[#18E4A1] font-bold leading-[3rem] text-3xl tracking-[-2%]"
          >
            Your Health is Our Mission
          </h1>
          <p class="font-medium text-lg leading-[160%] text-[#F2F5F9]">
            Experience the best medical treatment at our hospital. Our dedicated
            team of professionals provides personalized care with
            state-of-the-art facilities and advanced technology. Patient safety
            is our priority, and we ensure a clean and safe environment. Trust
            us as your healthcare partner, committed to your well-being.
          </p>
        </div>
      </div>
    </div>
    <div class="w-full h-[837px] bg-[#F2F5F9] px-[164px] py-[124px]">
      <div class="flex flex-col gap-8">
        <p
          class="font-normal text-3xl leadin[3rem] tracking-[-2%] text-[#052226]"
        >
          You can find us here
        </p>
        <div class="w-full h-[600px]">
          <iframe
            class="w-full h-full border-0"
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3940.551116793681!2d38.746360075767616!3d9.009820890894361!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x164b85cef5ab402d%3A0x8467b6b037a24d49!2sALERT%20Hospital!5e0!3m2!1sen!2sus!4v1716307000000!5m2!1sen!2sus"
            allowfullscreen=""
            loading="lazy"
            referrerpolicy="no-referrer-when-downgrade"
          ></iframe>
          <!-- Direct link to Google Maps with visible marker -->
          <div class="absolute bottom-4 right-4 bg-white p-2 rounded-md shadow-md">
            <a
              href="https://www.google.com/maps/place/ALERT+Hospital/@9.0098209,38.7463601,17z/data=!3m1!4b1!4m6!3m5!1s0x164b85cef5ab402d:0x8467b6b037a24d49!8m2!3d9.0098209!4d38.7463601!16s%2Fg%2F11c1z_3_0q?entry=ttu"
              target="_blank"
              class="flex items-center gap-2 text-primary font-medium"
            >
              <span>View on Google Maps</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                <polyline points="15 3 21 3 21 9"></polyline>
                <line x1="10" y1="14" x2="21" y2="3"></line>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
    <div
      class="w-full md:h-[506px] sm:h-[1011px] px-[170px] py-[84px] mt-10 flex flex-col gap-8 bg-[#052226]"
    >
      <img
        class="object-cover w-[75px] h-[61px]"
        src="/src/assets/img/alert22.png"
      />
      <div class="flex md:flex-row sm:flex-col sm:gap-8 justify-start">
        <div class="grid grid-cols-1 max-w-[692px]">
          <div class="flex flex-col flex-1 gap-12">
            <div class="flex flex-col gap-4">
              <h2
                id="location"
                class="font-normal leading-[22px] text-[15px] text-white"
              >
                Whether you require specialized compounding, comprehensive drug
                information, or round-the-clock accessibility, our pharmacy is
                dedicated to providing the highest level of pharmaceutical
                services for your convenience and peace of mind. Trust us to be
                your partner in health, delivering the care you deserve.
              </h2>
            </div>
            <div class="flex flex-col gap-7 text-[#F2F5F9]">
              <div class="flex gap-2">
                <i class="text-[#18E4A1]" v-html="icons.location" />
                <p class="font-normal sm:text-sm md:text-sm leading-5">
                  Zenebework Kolfe Keranyo Addis Ababa, Ethiopia
                </p>
              </div>
              <div class="flex gap-2">
                <i class="text-[#18E4A1]" v-html="icons.email" />
                <p class="font-normal sm:text-sm md:text-sm leading-5">
                  <EMAIL>
                </p>
              </div>
              <div class="flex gap-2">
                <i class="text-[#18E4A1]" v-html="icons.phone" />
                <p class="font-normal sm:text-sm md:text-sm leading-5">
                  +251911282828
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 ml-auto w-[400px] gap-4 items-center">
          <p
            class="text-[#18E4A1] font-bold text-base leading-8 tracking-[-1%]"
          >
            Quick links
          </p>
          <nav v-for="nav in navs" :key="nav.name">
            <a
              :class="[route.hash == nav.path && 'Quick-link']"
              :href="nav.path"
              @click="handleQuickLinkClick"
              class="text-[#F2F5F9] cursor-pointer hover:text-red-600 font-normal font-plusJakarta sm:text-sm md:text-sm leading-[100%] tracking-[-1%]"
            >
              {{ nav.name }}
            </a>
          </nav>
          <a
            @click.prevent="scrollToSection(departmentSection)"
            class="text-[#F2F5F9] cursor-pointer hover:text-red-600 font-normal font-plusJakarta sm:text-sm md:text-sm leading-[100%] tracking-[-1%]"
          >
            Department</a
          >
          <a
            @click.prevent="scrollToSection(policiesSection)"
            class="text-[#F2F5F9] cursor-pointer hover:text-red-600 font-normal font-plusJakarta sm:text-sm md:text-sm leading-[100%] tracking-[-1%]"
          >
            Policies and terms
          </a>
        </div>
      </div>
    </div>

    <div
      :style="{
        height: 'calc(100% - 5rem)',
      }"
      class="relative overflow-y-auto"
    >
      <RouterView />
    </div>
  </div>
</template>

<style scoped>
.active-link {
  background-color: #2e7987;
  /* Primary color */
  color: white;
  font-weight: bold;
}

.Quick-link {
  color: red;
  font-weight: bold;
  text-decoration: underline;
  position: relative;
  z-index: 9999;
}

.bg-gradient {
  width: 120.4px;
  height: 123.35px;
  top: 335px;
  left: -44px;
  transform: rotate(-44deg);
  box-shadow: 0 17.71px 28.98px 0 #0f9eb8,
    /* Drop shadow */ inset 4.83px 6.44px 35.42px 0 #0f9eb8;
  /* Inner shadow */
  background: linear-gradient(to right, #2e7987, #0a454d);
}

.bg-gradient1 {
  width: 183.3px;
  height: 185.03px;
  top: 395.73px;
  left: 160px;
  box-shadow: 0 17.71px 28.98px 0 #0f9eb8,
    /* Drop shadow */ inset 4.83px 6.44px 35.42px 0 #0f9eb8;
  /* Inner shadow */
  background: linear-gradient(to right, #2e7987, #0a454d);
}

.bg-gradient2 {
  width: 357.61px;
  height: 362.8px;
  top: 516px;
  left: -185px;
  box-shadow: 0 17.71px 28.98px 0 #0f9eb8,
    /* Drop shadow */ inset 4.83px 6.44px 35.42px 0 #0f9eb8;
  /* Inner shadow */
  background: linear-gradient(to right, #2e7987, #0a454d);
}

.bg-gradient3 {
  width: 210.25px;
  height: 212.24px;
  top: 610px;
  left: 205px;
  box-shadow: 0 17.71px 28.9px 0 #0f9eb8,
    /* Drop shadow */ inset 4.83px 6.44px 35.42px 0 #0f9eb8;
  /* Inner shadow */
  background: linear-gradient(to right, #2e7987, #0a454d);
}

#arrow i {
  position: relative;
  top: 0;
  transition: top ease o.5s;
}

#arrow:hover i {
  top: -10px;
}

.front:hover {
  width: 100%;
  transition: 1s;
  transform: scale(1.2);
}
</style>
