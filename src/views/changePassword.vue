<script setup>
import { ref } from "vue";
import { useApiRequest } from "@/composables/useApiRequest";
import Form from "@/new_form_builder/Form.vue";
import InputPassword from "@/components/new_form_elements/InputPassword.vue";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { mdiEmail } from "@mdi/js";
import { useRoute, useRouter } from "vue-router";
import { changePassword } from "./api/auth";
import ResetPassword from "./ResetPassword.vue";
import Button from "@/components/Button.vue";
import { toasted } from "@/utils/utils";

const message = ref("");
const changeReq = useApiRequest();
const router = useRouter();
const route = useRoute();
const userUuid = route.params.userUuid;
console.log(userUuid);

function changepass({ values }) {
  if (changeReq.pending.value) return;
  changeReq.send(
    () => changePassword(userUuid, values),
    (res) => {
      toasted(res.success, "Changed Successfully", res.error);
      if (res.success) {
        localStorage.clear();
        location.href = "/login";
      }
      message.value = res.success
        ? "Password has been changed successfully."
        : "Failed to change password. Please try again.";
    }
  );
}
</script>
<template>
  <div class="overflow-auto h-full">
    <div class="flex flex-col lg:flex-row px-10 gap-4 items-center mt-4">
      <img
        class="object-cover w-20"
        src="/src/assets/img/loginalert.png"
        alt=""
      />
      <p class="text-3xl md:text-2xl">Alert Hospital</p>
    </div>
    <div class="flex flex-col items-center gap-2 p-10 m-auto max-w-[400px]">
      <BaseIcon
        class="text-yellow-700"
        w="w-12"
        h="w-12"
        size="50"
        :path="mdiEmail"
      />
      <p class="font-bold text-3xl md:text-4xl justify-center">
        Change Password
      </p>
    </div>
    <div
      class="max-w-[400px] m-auto p-5 border border-solid border-[#ccc] rounded-md shadow-md"
    >
      <p class="mt-2 opacity-50 text-center">Please change your password.</p>
      <Form class="pt-4" id="changeform" v-slot="{ submit }">
        <div class="mb-4">
          <InputPassword
            validation="required"
            name="oldPassword"
            :attributes="{ placeholder: 'old password' }"
            label="Old Password"
          />
        </div>
        <div class="mb-4">
          <InputPassword
            name="newPassword"
            validation="required"
            :attributes="{ placeholder: 'new password' }"
            label="New Password"
          />
        </div>
        <div class="mb-4">
          <InputPassword
            name="confirmPassword"
            validation="required|equalTo-newPassword"
            :attributes="{ placeholder: 'confirm password' }"
            label="Confirm Password"
          />
        </div>
        <Button
          :pending="changeReq.pending.value"
          @click.prevent="submit(changepass)"
          type="submit"
          class="bg-[#007bff] text-white py-2 mt-3 rounded-lg cursor-pointer w-full"
        >
          Change
        </Button>
      </Form>
      <p
        v-if="message"
        :class="[changeReq.error.value ? 'text-red-500' : 'text-green-500']"
        class="mt-4"
      >
        {{ message }}
      </p>
    </div>
  </div>
</template>
