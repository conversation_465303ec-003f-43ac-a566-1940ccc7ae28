<script setup>
import { ref } from "vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { emailVerification } from "./api/auth"; // Create this function in your API module
import Form from "@/new_form_builder/Form.vue";
import Input from "@/components/new_form_elements/Input.vue";
import { openModal } from "@customizer/modal-x";
import { useRouter } from "vue-router";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { mdiEmail, mdiLockReset } from "@mdi/js";
import { toasted } from "@/utils/utils";

const router = useRouter();
const message = ref("");
const resetRequest = useApiRequest();

function requestResetCode({ values }) {
  if (resetRequest.pending.value) return;
  openModal(
    "Confirmation",
    {
      title: "Request For ResetPassword",
      message: "Are you sure you want to reset?",
    },
    (res) => {
      if (res) {
        resetRequest.send(
          () => emailVerification(values.emailVerificationToken),
          (res) => {
            console.log(res);

            if (res.success) {
              message.value =
                "A password reset code has been sent to your email.";
              router.push(
                `/resetpassword?email=${values.emailVerificationToken}`
              );
            } else {
              message.value = "Failed to send reset code. Please try again.";
            }
            toasted(res.success, "Succefully sent", res.error);
          }
        );
      }
    }
  );
}
</script>

<template>
  <div class="px-26 bg-[#f7fafc] h-full pb-10 overflow-auto">
    <div class="flex flex-col lg:flex-row px-10 gap-4 items-center mt-4">
      <img
        class="object-cover w-20"
        src="/src/assets/img/loginalert.png"
        alt=""
      />
      <p class="text-3xl md:text-2xl">Alert Hospital</p>
    </div>
    <div class="flex flex-col items-center gap-2 p-10 m-auto max-w-[400px]">
      <BaseIcon
        class="text-yellow-700"
        w="w-12"
        h="w-12"
        size="lg"
        :path="mdiEmail"
      />
      <p class="font-bold text-3xl md:text-4xl">Verify Your Email</p>
    </div>
    <div
      class="max-w-[400px] m-auto p-5 border border-solid border-[#ccc] rounded-md shadow-md"
    >
      <h1 class="text-2xl font-bold">Forgot Password</h1>
      <p class="mt-2 opacity-50">
        Please enter your email address to receive a password reset code.
      </p>
      <Form
        class="pt-4"
        id="forgetpasswordForm"
        v-slot="{ submit }"
      >
        <Input
					validation="required|email"
          name="emailVerificationToken"
          :attributes="{ placeholder: 'Email' }"
          label="Email"
        />
        <button
          @click.prevent="submit(requestResetCode)"
          class="bg-[#007bff] text-white p-[10px] mt-3 rounded-lg cursor-pointer"
        >
          Send Reset Code
        </button>
      </Form>
      <p
        v-if="message"
        :class="[resetRequest.error.value ? 'text-red-500' : 'text-green-500']"
        class="mt-4"
      >
        {{ message }}
      </p>
    </div>
  </div>
</template>
