/* @import url("https://fonts.googleapis.com/css2?family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap"); */
@import '/node_modules/bootstrap-icons/font/bootstrap-icons.min.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --drug-search-height: 4rem;
  --primary: 237 96 51;
  --secondary: 54 54 54;
  --dark: 29 28 27;
  --accent: 248 243 241;
  --base-clr-1: 248 248 248;
  --base-clr-2: 255 255 255;
  --base-clr-3: 238 238 238;
  --base-clr-4: 248 243 241;
  --text-clr: var(--dark);
  --error: 237 96 51;
}

* {
  box-sizing: border-box !important;
  scroll-padding-top: 8rem;
  scroll-behavior: smooth;
}

html,
body,
#app {
  height: 100%;
  overflow: hidden;
}

*,
*:focus {
  scrollbar-width: none;
  outline: none;
  box-shadow: none;
}

::-webkit-scrollbar {
  display: none;
}

.show-scrollbar {
  scrollbar-width: thin;
}

.show-scrollbar::-webkit-scrollbar {
  display: block;
  width: 5px;
}

button:focus {
  box-shadow: 0 0 0px 2px theme("colors.primary");
}

@font-face {
  font-family: 'Montserrat';
  src: url('/public/fonts/Montserrat-VariableFont_wght.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-size: 16px;
}

@font-face {
  font-family: 'PlusJakartaSans';
  src: url('/public/fonts/PlusJakartaSans-VariableFont_wght.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@layer base {
  body {
    font-family: "Ubuntu", sans-serif;
    font-weight: normal;
    color: rgb(var(--text-clr));
    background-color: rgb(var(--base-clr-1));
    @apply text-base;
  }

  .sys-focus {
    box-shadow: 0 0 1px 1px #000;
  }
}

.router-link-active.router-link-exact-active {
  @apply bg-primary text-white;
}
.router-link-active {
  @apply bg-primary/30 text-dark;
}
