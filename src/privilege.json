{"privileges": [{"privilegeName": "Read_Dashboard", "privilegeDescription": "Allow to read dashboard", "privilegeCategory": "Dashboard"}, {"privilegeName": "Create_Dashboard", "privilegeDescription": "Allow to create dashboard", "privilegeCategory": "Dashboard"}, {"privilegeName": "Update_Dashboard", "privilegeDescription": "Allow to update dashboard", "privilegeCategory": "Dashboard"}, {"privilegeName": "Delete_Dashboard", "privilegeDescription": "Allow to delete dashboard", "privilegeCategory": "Dashboard"}, {"privilegeName": "View_Dashboard", "privilegeDescription": "Allow to view dashboard", "privilegeCategory": "View"}, {"privilegeName": "Read_Privilege", "privilegeDescription": "Allow to read privilege", "privilegeCategory": "Privilege"}, {"privilegeName": "Create_Privilege", "privilegeDescription": "Allow to create privilege", "privilegeCategory": "Privilege"}, {"privilegeName": "View_Privilege", "privilegeDescription": "Allow to view privilege", "privilegeCategory": "View"}, {"privilegeName": "Read_Role", "privilegeDescription": "Allow to read role", "privilegeCategory": "Role"}, {"privilegeName": "Create_Role", "privilegeDescription": "Allow to create role", "privilegeCategory": "Role"}, {"privilegeName": "Update_Role", "privilegeDescription": "Allow to update role", "privilegeCategory": "Role"}, {"privilegeName": "Delete_Role", "privilegeDescription": "Allow to delete role", "privilegeCategory": "Role"}, {"privilegeName": "View_Role", "privilegeDescription": "Allow to view role", "privilegeCategory": "Role"}, {"privilegeName": "Read_User", "privilegeDescription": "Allow to read user", "privilegeCategory": "User"}, {"privilegeName": "Create_User", "privilegeDescription": "Allow to create user", "privilegeCategory": "User"}, {"privilegeName": "Update_User", "privilegeDescription": "Allow to update user", "privilegeCategory": "User"}, {"privilegeName": "Delete_User", "privilegeDescription": "Allow to delete user", "privilegeCategory": "User"}, {"privilegeName": "View_User", "privilegeDescription": "Allow to view user", "privilegeCategory": "View"}, {"privilegeName": "Create_Drugs", "privilegeDescription": "Allow to create drugs", "privilegeCategory": "drug"}, {"privilegeName": "Update_Drugs", "privilegeDescription": "Allow to update drugs", "privilegeCategory": "drug"}, {"privilegeName": "Delete_Drugs", "privilegeDescription": "Allow to delete drugs", "privilegeCategory": "drug"}, {"privilegeName": "View_Drugs", "privilegeDescription": "Allow to view drugs", "privilegeCategory": "View"}, {"privilegeName": "Read_Prescription", "privilegeDescription": "Allow to read prescription", "privilegeCategory": "Prescription"}, {"privilegeName": "Create_Prescription", "privilegeDescription": "Allow to create prescription", "privilegeCategory": "Prescription"}, {"privilegeName": "Update_Prescription", "privilegeDescription": "Allow to update prescription", "privilegeCategory": "Prescription"}, {"privilegeName": "Delete_Prescription", "privilegeDescription": "Allow to delete prescription", "privilegeCategory": "Prescription"}, {"privilegeName": "View_Prescription", "privilegeDescription": "Allow to view prescription", "privilegeCategory": "View"}, {"privilegeName": "Read_Refill", "privilegeDescription": "Allow to read refill", "privilegeCategory": "Refill"}, {"privilegeName": "Create_Refill", "privilegeDescription": "Allow to create refill", "privilegeCategory": "Refill"}, {"privilegeName": "Update_Refill", "privilegeDescription": "Allow to update refill", "privilegeCategory": "Refill"}, {"privilegeName": "Delete_Refill", "privilegeDescription": "Allow to delete refill", "privilegeCategory": "Refill"}, {"privilegeName": "View_Refill", "privilegeDescription": "Allow to view refill", "privilegeCategory": "View"}, {"privilegeName": "Read_PaidOrders", "privilegeDescription": "Allow to read paidorders", "privilegeCategory": "PaidOrders"}, {"privilegeName": "Create_PaidOrders", "privilegeDescription": "Allow to create paidorders", "privilegeCategory": "PaidOrders"}, {"privilegeName": "Update_PaidOrders", "privilegeDescription": "Allow to update paidorders", "privilegeCategory": "PaidOrders"}, {"privilegeName": "Delete_PaidOrders", "privilegeDescription": "Allow to delete paidorders", "privilegeCategory": "PaidOrders"}, {"privilegeName": "View_PaidOrders", "privilegeDescription": "Allow to view paidorders", "privilegeCategory": "View"}, {"privilegeName": "Read_EPrescription", "privilegeDescription": "Allow to read ePrescription", "privilegeCategory": "EPrescription"}, {"privilegeName": "Create_EPrescription", "privilegeDescription": "Allow to create ePrescription", "privilegeCategory": "EPrescription"}, {"privilegeName": "Update_EPrescription", "privilegeDescription": "Allow to update ePrescription", "privilegeCategory": "EPrescription"}, {"privilegeName": "Delete_EPrescription", "privilegeDescription": "Allow to delete ePrescription", "privilegeCategory": "EPrescription"}, {"privilegeName": "View_EPrescription", "privilegeDescription": "Allow to view ePrescription", "privilegeCategory": "View"}, {"privilegeName": "Read_ReturnedOrders", "privilegeDescription": "Allow to read returnedOrders", "privilegeCategory": "ReturnedOrders"}, {"privilegeName": "Create_ReturnedOrders", "privilegeDescription": "Allow to create returnedOrders", "privilegeCategory": "ReturnedOrders"}, {"privilegeName": "Update_ReturnedOrders", "privilegeDescription": "Allow to update returnedOrders", "privilegeCategory": "ReturnedOrders"}, {"privilegeName": "Delete_ReturnedOrders", "privilegeDescription": "Allow to delete returnedOrders", "privilegeCategory": "ReturnedOrders"}, {"privilegeName": "View_ReturnedOrders", "privilegeDescription": "Allow to view returnedOrders", "privilegeCategory": "View"}, {"privilegeName": "Read_Stock", "privilegeDescription": "Allow to read stock", "privilegeCategory": "Stock"}, {"privilegeName": "Create_Stock", "privilegeDescription": "Allow to create stock", "privilegeCategory": "Stock"}, {"privilegeName": "Update_Stock", "privilegeDescription": "Allow to update stock", "privilegeCategory": "Stock"}, {"privilegeName": "Delete_Stock", "privilegeDescription": "Allow to delete stock", "privilegeCategory": "Stock"}, {"privilegeName": "View_Stock", "privilegeDescription": "Allow to view stock", "privilegeCategory": "View"}, {"privilegeName": "Read_Store", "privilegeDescription": "Allow to read store", "privilegeCategory": "Store"}, {"privilegeName": "Create_Store", "privilegeDescription": "Allow to create store", "privilegeCategory": "Store"}, {"privilegeName": "Update_Store", "privilegeDescription": "Allow to update store", "privilegeCategory": "Store"}, {"privilegeName": "Delete_Store", "privilegeDescription": "Allow to delete store", "privilegeCategory": "Store"}, {"privilegeName": "View_Store", "privilegeDescription": "Allow to view store", "privilegeCategory": "View"}, {"privilegeName": "Read_Request", "privilegeDescription": "Allow to read request", "privilegeCategory": "Request"}, {"privilegeName": "Create_Request", "privilegeDescription": "Allow to create request", "privilegeCategory": "Request"}, {"privilegeName": "Update_Request", "privilegeDescription": "Allow to update request", "privilegeCategory": "Request"}, {"privilegeName": "Delete_Request", "privilegeDescription": "Allow to delete request", "privilegeCategory": "Request"}, {"privilegeName": "View_Request", "privilegeDescription": "Allow to view request", "privilegeCategory": "View"}, {"privilegeName": "Read_StockPurchases", "privilegeDescription": "Allow to read StockPurchases", "privilegeCategory": "StockPurchases"}, {"privilegeName": "Create_StockPurchases", "privilegeDescription": "Allow to create StockPurchases", "privilegeCategory": "StockPurchases"}, {"privilegeName": "Update_StockPurchases", "privilegeDescription": "Allow to update StockPurchases", "privilegeCategory": "StockPurchases"}, {"privilegeName": "Delete_StockPurchases", "privilegeDescription": "Allow to delete StockPurchases", "privilegeCategory": "StockPurchases"}, {"privilegeName": "View_StockPurchases", "privilegeDescription": "Allow to view StockPurchases", "privilegeCategory": "View"}, {"privilegeName": "Read_-Cashier", "privilegeDescription": "Allow to read cashier", "privilegeCategory": "Cashier"}, {"privilegeName": "Create_Cashier", "privilegeDescription": "Allow to create cashier", "privilegeCategory": "Cashier"}, {"privilegeName": "Update_Cashier", "privilegeDescription": "Allow to update cashier", "privilegeCategory": "Cashier"}, {"privilegeName": "Delete_Cashier", "privilegeDescription": "Allow to delete cashier", "privilegeCategory": "Cashier"}, {"privilegeName": "View_Cashier", "privilegeDescription": "Allow to view cashier", "privilegeCategory": "View"}, {"privilegeName": "Read_CompoundingCashier", "privilegeDescription": "Allow to read Compounding<PERSON>ashier", "privilegeCategory": "CompoundingCashier"}, {"privilegeName": "Create_CompoundingCashier", "privilegeDescription": "Allow to create CompoundingCashier", "privilegeCategory": "CompoundingCashier"}, {"privilegeName": "Update_CompoundingCashier", "privilegeDescription": "Allow to update CompoundingCashier", "privilegeCategory": "CompoundingCashier"}, {"privilegeName": "Delete_CompoundingCashier", "privilegeDescription": "Allow to delete CompoundingCashier", "privilegeCategory": "CompoundingCashier"}, {"privilegeName": "View_CompoundingCashier", "privilegeDescription": "Allow to view CompoundingCashier", "privilegeCategory": "View"}, {"privilegeName": "Read_DoctorPrescription", "privilegeDescription": "Allow to read DoctorPrescription", "privilegeCategory": "DoctorPrescription"}, {"privilegeName": "Create_DoctorPrescription", "privilegeDescription": "Allow to create DoctorPrescription", "privilegeCategory": "DoctorPrescription"}, {"privilegeName": "Update_DoctorPrescription", "privilegeDescription": "Allow to update DoctorPrescription", "privilegeCategory": "DoctorPrescription"}, {"privilegeName": "Delete_DoctorPrescription", "privilegeDescription": "Allow to delete DoctorPrescription", "privilegeCategory": "DoctorPrescription"}, {"privilegeName": "View_DoctorPrescription", "privilegeDescription": "Allow to view DoctorPrescription", "privilegeCategory": "View"}, {"privilegeName": "Read_DoctorCompounding", "privilegeDescription": "Allow to read <PERSON><PERSON><PERSON>pounding", "privilegeCategory": "Doctor<PERSON>ompounding"}, {"privilegeName": "Create_DoctorCompounding", "privilegeDescription": "Allow to create Doctor<PERSON>ompounding", "privilegeCategory": "Doctor<PERSON>ompounding"}, {"privilegeName": "Update_DoctorCompounding", "privilegeDescription": "Allow to update <PERSON><PERSON><PERSON><PERSON>unding", "privilegeCategory": "Doctor<PERSON>ompounding"}, {"privilegeName": "Delete_DoctorCompounding", "privilegeDescription": "Allow to delete Doctor<PERSON>ompounding", "privilegeCategory": "Doctor<PERSON>ompounding"}, {"privilegeName": "View_DoctorCompounding", "privilegeDescription": "Allow to view <PERSON><PERSON><PERSON>pounding", "privilegeCategory": "View"}]}