export default [
  {
    path: "/dashboard",
    name: "Dashboard",
    // role: "Admin",
    privileges: ["view_dashboard"],
  },
  {
    path: "/paper_prescription",
    name: "Paper Prescriptions",
    // role: "pharmacist",
    privileges: ["view_prescription"],
  },
    {
    path: "/ePrescription",
    name: "e-Prescription",
    // role: "pharmacist",
    privileges: ["view_eprescription"],
  },
  {
    path: "/cashier",
    name: "Medicine Payment",
    // role: "Cashier",
    privileges: ["view_cashier", "view_compoundingcashier"],
  },
  {
    path: "/compounding",
    name: "Compounding",
    role: "pharmacist",
    privileges: ["view_compounding"],

  },
  // {
  //   path: "/cashiercompounding",
  //   name: "Compounding Payment",
  //   role: "Cashier",
  //   privileges: ["view_cashier", "view_compoundingcashier"],
  // },
  {
    path: "/refill",
    name: "Refill",
    // role: "pharmacist",
    privileges: ["view_refill"],
  },
  {
    path: "/paidOrder",
    name: "Counseling",
    // role: "pharmacist",
    privileges: ["view_paidorders"],
  },
  {
    path: "/ReturnedOrders",
    name: "Returned Orders",
    // role: "pharmacist",
    privileges: ["view_returnedorders"],
  },
  {
    path: "/issued-batch",
    name: "Issued",
    // role: "pharmacist",
    privileges: ["view_issued"],
  },
  {
    path: "/loss-adjestment",
    name: "Loss / Adjestment",
    // role: "pharmacist",
    privileges: ["view_issued"],
  },
  // {
  //   path: "/institution",
  //   name: "Institution",
  //   // role: "Payer",
  //   privileges: ["view_institution"],
  // },
  {
    path: "/claim",
    name: "Claim",
    // role: "Payer",
    privileges: ["view_claim"],
  },
  // {
  //   path: "/national_drug",
  //   name: "National Drug (DB)",
  //   // role: "pharmacist",
  //   privileges: ["view_institutions"],
  // },
  // {
  //   path: "/reports",
  //   name: "Reports",
  //   role: "pharmacist",
  //   privileges: ["view_report"],
  // },
  {
    path: "/stock",
    name: "IFRR",
    // role: "pharmacist",
    privileges: ["view_stock"],
  },
  
  {
    path: "/role",
    name: "Role",
    // role: "Admin",
    privileges: ["view_role"],
  },
  {
    path: "/privileges",
    name: "Privileges",
    // role: "Admin",
    privileges: ["view_privilege"],
  },

  {
    path: "/drugs",
    name: "Drug",
    // role: "Admin",
    privileges: ["view_drugs"],
  },
  {
    path: "/store",
    name: "Stock Status",
    // role: "storekeeper",
    privileges: ["view_store"],
  },
  {
    path: "/store-ifrr",
    name: "Store IFRR",
    // role: "storekeeper",
    privileges: ["view_store"],
  },
  {
    path: "/request",
    name: "Requested IFRR",
    // role: "storekeeper",
    privileges: ["view_request"],
  },
  {
    path: "/stockpurchases",
    name: "Stock Recieve",
    // role: "storekeeper",
    privileges: ["view_stockpurchases"],
  },
  {
    path: "/doctor",
    name: "Prescription",
    // role: "doctor",
    privileges: ["view_doctorprescription"],
  },
  {
    path: "/doctorCompounding",
    name: "Compounding",
    // role: "doctor",
    privileges: ["view_doctorcompounding"],
  },
  // {
  //   path: "/kotera",
  //   name: "Kotera",
  //   privileges: ["view_kotera"],
  // },
  {
    path: "/dispensarykoteracrosscheck",
    name: "Inventory",
    privileges: ["view_dispensarykoteracrosscheck"],
  },
  {
    path: "/koteracrosscheck",
    name: "Inventory", // Store Inventory
    privileges: ["view_kotera"],
  },
  {
    path: "/returnedorderstodoctor",
    name: "Returned Orders",
    privileges: ["view_returnedorderstodoctor"],
  },
  {
    path: "/cashierreport",
    name: "Cashier Report",
    privileges: ["view_cashierreport"],
  },
  {
    path: "/registrar",
    name: "Add Patient",
    hidden: true,
    privileges: ['view_add_patient']
  },
  {
    path: "/institutions",
    name: "Institutions",
    // role: "pharmacist",
    privileges: ["view_institutions"],
  },
  {
    path: "/admin/inventory",
    name: "Retail Price Update",
    privileges: ["view_admin_inventory"],
  },
  {
    path: "/report",
    name: "Report",
    privileges: ["view_report"],
  },
];
