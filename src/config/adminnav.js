import icons from "@/utils/icons";

export default [
  {
    path: "/dashboard",
    name: "Dashboard",
    icon: icons.dashboard,
    meta: {
      requiresAuth: true,
      privileges: ["view_dashboard"],
    },
    navs: [],
    addDivider: true, // Add divider after Dashboard
  },
    {
    name: "Inventory",
    icon: icons.shelf,
    meta: {
      requiresAuth: true,
      privileges: ["view_dashboard"],
    },
    navs: [
      {
        path: "/drugs",
        name: "Drugs Library",
        icon: icons.drugslibrary,
        meta: {
          requiresAuth: true,
          privileges: ["view_drugs"],
        },
      },
      {
        path: "/transfers",
        name: "Transfers",
        icon: icons.transfers,
        meta: {
          requiresAuth: true,
          privileges: ["view_dashboard"],
        },
      },
      {
        path: "/centralwarehouse",
        name: "Central Warehouse",
        icon: icons.centralinventory,
        meta: {
          requiresAuth: true,
          privileges: ["view_dashboard"],
        },
      },
    ],
  },
  {
    name: "Procurement ",
    icon: icons.purchases,
    meta: {
      requiresAuth: true,
      privileges: ["view_dashboard"],
    },
    navs: [
      {
        path: "/purchaserequests",
        name: "Purchase Request",
        meta: {
          requiresAuth: true,
          privileges: ["view_purchaserequest"],
        },
        childRoutes: ["/purchaserequests/view"],
      },
      {
        path: "/purchaserequests/purchaserequest-detail",
        name: "Purchase Request Detail",
        meta: {
          requiresAuth: true,
          privileges: ["view_purchaserequest"],
        },
        childRoutes: ["/purchaserequests/view"],
      },
      {
        path: "/inpurchaseprocess",
        name: "Purchase Order",
        meta: {
          requiresAuth: true,
          privileges: ["view_purchaserequest"],
        },
        childRoutes: ["/purchaserequests/view"],
      },
      {
        path: "/warehouse-stock-recieve",
        name: "Stock Recieve",
        meta: {
          requiresAuth: true,
          privileges: ["view_purchaserequest"],
        },
        childRoutes: ["/purchaserequests/view"],
      },
      {
        path: "/warehouse-stock-status",
        name: "Stock Status",
        meta: {
          requiresAuth: true,
          privileges: ["view_purchaserequest"],
        },
        childRoutes: ["/purchaserequests/view"],
      },
      {
        path: "suppliers",
        name: "Suppliers",
        privileges: ["view_suppliers"],
      },
      {
        path: "/manufacturers",
        name: "Manufacturers",
        privileges: ["view_manufacturers"],
      },
      // {
      //   path: "/requestapproval",
      //   name: "Approval",
      //   meta: {
      //     requiresAuth: true,
      //     privileges: ["view_dashboard"],
      //   },
      // },
      // {
      //   path: "/inpurchaseprocess",
      //   name: "In Purchase Process",
      //   meta: {
      //     requiresAuth: true,
      //     privileges: ["view_dashboard"],
      //   },
      // },
      // {
      //   path: "/purchases",
      //   name: "Procurements",
      //   meta: {
      //     requiresAuth: true,
      //     privileges: ["view_dashboard"],
      //   },
      // },
      // {
      //   path: "/distribution",
      //   name: "Distribution",
      //   meta: {
      //     requiresAuth: true,
      //     privileges: ["view_dashboard"],
      //   },
      // },
    ],
  },

  {
    name: "Sales",
    icon: icons.sales,
    meta: {
      requiresAuth: true,
      privileges: ["view_dashboard"],
    },
    navs: [
      {
        path: "/customer-list",
        name: "Customer List",
        privileges: ["view_admin_inventory"],
      },
      {
        path: "/instituions",
        name: "Institutions / Insurers",
        privileges: ["view_admin_inventory"],
      },
      {
        path: "/admin/inventory",
        name: "Retail Price Update",
        privileges: ["view_admin_inventory"],
      },
    ],
  },
  {
    name: "Settings",
    icon: icons.settings,
    meta: {
      requiresAuth: true,
      privileges: ["view_dashboard"],
    },
    navs: [
      {
        path: "/branches",
        name: "Branches",
        icon: icons.bransches,
        meta: {
          requiresAuth: true,
          privileges: ["view_dashboard"],
        },
        childRoutes: ["/addbranch"],
      },
      {
        name: 'Units',
        path: '/units',
        icon: icons.unit,
        meta: {
          requiresAuth: true,
          privileges: ["view_unit"],
        }
      },
      {
        path: "/user",
        name: "Users",
        icon: icons.users,
        meta: {
          requiresAuth: true,
          privileges: ["view_user"],
        },
      },
      {
        path: "/role",
        name: "Roles",
        icon: icons.roles,
        meta: {
          requiresAuth: true,
          privileges: ["view_role"],
        },
        childRoutes: ["/create-role", "/edit_role/:roleUuid"],
      },
      {
        path: "/privileges",
        name: "Privileges",
        icon: icons.privileges,
        meta: {
          requiresAuth: true,
          privileges: ["view_privilege"],
        },
        childRoutes: ["/create-privilege", "/edit_privilege/:privilegeUuid"],
        addDivider: true,
      },
      
      {
        path: "/managewebsite",
        name: "Manage Website",
        privileges: ["view_managewebsite"],
      },
    ],
  },

  {
    path: "/report",
    name: "Report",
    icon: icons.report,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
  {
    path: "/notification",
    name: "Notifications",
    icon: icons.notifications,
    meta: {
      requiresAuth: true,
      privileges: ["view_privilege"],
    },
  },
];
