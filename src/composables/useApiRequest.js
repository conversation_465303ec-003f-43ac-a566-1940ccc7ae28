import { provide, ref } from "vue";

// The function accepts a parameter provideValues, which determines whether to provide the pending and error states to child components.
export function useApiRequest(provideValues = true) {
  const response = ref();
  const pending = ref(false);
  const error = ref("");
  const dirty = ref(false); //A flag indicating whether the request has been made.

  //provide allows you to make data available to child components.
  // If provideValues is true, the pending and error states are made available for injection in child components.
  if (provideValues) {
    provide("pending", pending);
    provide("error", error);
  }

  // request: A function that performs the API request.
  // cb: A callback function to execute after the request completes.
  // remove: A flag to reset the response state.
  // beforeResolve: If true, the callback cb is called before resolving the promise.

  function send(
    request = (f) => f,
    cb = (f) => f,
    remove = false,
    beforeResolve = false
  ) {
    if (typeof request != "function")
      return console.error("can not be called. not a function");

    pending.value = true;
    error.value = "";

    if (remove) {
      response.value = null;
    }
    // return new Promise((resolve, reject) => {
    try {
      dirty.value = true;
      request().then((res) => {
        if (beforeResolve) cb(res);

        // setTimeout(() => {
        pending.value = false;
        if (!(typeof cb == "function")) return; //resolve(res);

        response.value = res?.data;
        error.value = res?.error;

        cb(res);
        // resolve(res);
        // }, 0);
      });
    } catch (err) {
      console.error(err);
      pending.value = false;
      error.value = err.message;
      // reject(error.value)
    }
    // });
  }

  return {
    response,
    send,
    pending,
    error,
    dirty,
  };
}
