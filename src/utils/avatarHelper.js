import { createAvatar } from '@dicebear/core';
import { avataaars, initials } from '@dicebear/collection';

/**
 * Generates an avatar image URL based on user information
 * 
 * @param {Object} options - Configuration options
 * @param {string} options.imageData - Base64 encoded image data if available
 * @param {string} options.userName - User's name for generating avatar
 * @param {string} options.userId - User's ID as fallback for generating avatar
 * @param {string} options.roleName - User's role name as another fallback
 * @returns {string} URL or data URI for the avatar image
 */
export function generateAvatar(options) {
  const { imageData, userName, userId, roleName } = options;
  
  // If user has a profile image, use it
  if (imageData) {
    return `data:image/png;base64,${imageData}`;
  }
  
  try {
    // Generate avatar based on user's name or ID
    const seed = userName || userId || roleName || 'default';
    const avatar = createAvatar(initials, {
      seed: seed,
      backgroundColor: ['b6e3f4', 'c0aede', 'd1d4f9', 'ffd5dc', 'ffdfbf'],
      radius: 50
    });
    
    return avatar.toDataUri();
  } catch (error) {
    console.error('Error generating avatar:', error);
    // Return a default image URL if avatar generation fails
    return 'https://ui-avatars.com/api/?name=' + encodeURIComponent(userName || roleName || userId || 'User');
  }
}
