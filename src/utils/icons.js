export default {
  report: `
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 512 512"><path fill="currentColor" d="M376 160v32h65.372L252 381.373l-72-72L76.686 412.686l22.628 22.628L180 354.627l72 72l212-211.999V280h32V160z"/><path fill="currentColor" d="M48 104H16v392h480v-32H48z"/></svg>
  `,
  settings: `
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><path fill="currentColor" d="M8 0a8 8 0 0 1 .701.031C9.444.095 9.99.645 10.16 1.29l.288 1.107c.018.066.079.158.212.224q.347.171.668.386c.123.082.233.09.299.071l1.103-.303c.644-.176 1.392.021 1.82.63q.406.578.704 1.218c.315.675.111 1.422-.364 1.891l-.814.806c-.049.048-.098.147-.088.294q.024.386 0 .772c-.01.147.038.246.088.294l.814.806c.475.469.679 1.216.364 1.891a8 8 0 0 1-.704 1.217c-.428.61-1.176.807-1.82.63l-1.102-.302c-.067-.019-.177-.011-.3.071a6 6 0 0 1-.668.386c-.133.066-.194.158-.211.224l-.29 1.106c-.168.646-.715 1.196-1.458 1.26a8 8 0 0 1-1.402 0c-.743-.064-1.289-.614-1.458-1.26l-.289-1.106c-.018-.066-.079-.158-.212-.224a6 6 0 0 1-.668-.386c-.123-.082-.233-.09-.299-.071l-1.103.303c-.644.176-1.392-.021-1.82-.63a8 8 0 0 1-.704-1.218c-.315-.675-.111-1.422.363-1.891l.815-.806c.05-.048.098-.147.088-.294a6 6 0 0 1 0-.772c.01-.147-.038-.246-.088-.294l-.815-.806C.635 6.045.431 5.298.746 4.623a8 8 0 0 1 .704-1.217c.428-.61 1.176-.807 1.82-.63l1.102.302c.067.019.177.011.3-.071q.321-.215.668-.386c.133-.066.194-.158.211-.224l.29-1.106C6.009.645 6.556.095 7.299.03Q7.646 0 8 0m-.571 1.525c-.036.003-.108.036-.137.146l-.289 1.105c-.147.561-.549.967-.998 1.189q-.26.13-.5.29c-.417.278-.97.423-1.529.27l-1.103-.303c-.109-.03-.175.016-.195.045q-.33.47-.573.99c-.014.031-.021.11.059.19l.815.806c.411.406.562.957.53 1.456a5 5 0 0 0 0 .582c.032.499-.119 1.05-.53 1.456l-.815.806c-.081.08-.073.159-.059.19q.243.52.573.989c.02.03.085.076.195.046l1.102-.303c.56-.153 1.113-.008 1.53.27q.242.16.501.29c.447.222.85.629.997 1.189l.289 1.105c.029.109.101.143.137.146a6.6 6.6 0 0 0 1.142 0c.036-.003.108-.036.137-.146l.289-1.105c.147-.561.549-.967.998-1.189q.26-.13.5-.29c.417-.278.97-.423 1.529-.27l1.103.303c.109.029.175-.016.195-.045q.33-.47.573-.99c.014-.031.021-.11-.059-.19l-.815-.806c-.411-.406-.562-.957-.53-1.456a5 5 0 0 0 0-.582c-.032-.499.119-1.05.53-1.456l.815-.806c.081-.08.073-.159.059-.19a6.5 6.5 0 0 0-.573-.989c-.02-.03-.085-.076-.195-.046l-1.102.303c-.56.153-1.113.008-1.53-.27a4 4 0 0 0-.501-.29c-.447-.222-.85-.629-.997-1.189l-.289-1.105c-.029-.11-.101-.143-.137-.146a6.6 6.6 0 0 0-1.142 0M11 8a3 3 0 1 1-6 0a3 3 0 0 1 6 0M9.5 8a1.5 1.5 0 1 0-3.001.001A1.5 1.5 0 0 0 9.5 8"/></svg>
  `,
  sales: `
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 32 32"><path fill="currentColor" d="M30 6V4h-3V2h-2v2h-1c-1.103 0-2 .898-2 2v2c0 1.103.897 2 2 2h4v2h-6v2h3v2h2v-2h1c1.103 0 2-.897 2-2v-2c0-1.102-.897-2-2-2h-4V6zm-6 14v2h2.586L23 25.586l-2.292-2.293a1 1 0 0 0-.706-.293H20a1 1 0 0 0-.706.293L14 28.586L15.414 30l4.587-4.586l2.292 2.293a1 1 0 0 0 1.414 0L28 23.414V26h2v-6zM4 30H2v-5c0-3.86 3.14-7 7-7h6c1.989 0 3.89.85 5.217 2.333l-1.49 1.334A5 5 0 0 0 15 20H9c-2.757 0-5 2.243-5 5zm8-14a7 7 0 1 0 0-14a7 7 0 0 0 0 14m0-12a5 5 0 1 1 0 10a5 5 0 0 1 0-10"/></svg>
  `,
  shelf: `
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><path fill="none" stroke="currentColor" d="M2 8h12m-9.5 5v-3h-2v3m2 0v.5h-2V13m2 0h-2M9 13v-3H7v3m2 0v.5H7V13m2 0H7m6.5 0v-3h-2v3m2 0v.5h-2V13m2 0h-2m-7-7.5v-3h-2v3m2 0V6h-2v-.5m2 0h-2m6.5 0v-3H7v3m2 0V6H7v-.5m2 0H7m6.5 0v-3h-2v3m2 0V6h-2v-.5m2 0h-2" stroke-width="1"/></svg>
  `,
  start: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linejoin="round" stroke-width="1.5"><path d="M7 13.732c-1.333-.77-1.333-2.694 0-3.464l9-5.196c1.333-.77 3 .192 3 1.732v10.392c0 1.54-1.667 2.502-3 1.732z"/><path stroke-linecap="round" d="M4 19V5"/></g></svg>
  `,
  lock: `
		<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M6.616 21q-.672 0-1.144-.472T5 19.385v-8.77q0-.67.472-1.143Q5.944 9 6.616 9H8V7q0-1.671 1.165-2.835Q10.329 3 12 3t2.836 1.165T16 7v2h1.385q.67 0 1.143.472q.472.472.472 1.144v8.769q0 .67-.472 1.143q-.472.472-1.143.472zm0-1h10.769q.269 0 .442-.173t.173-.442v-8.77q0-.269-.173-.442T17.385 10H6.615q-.269 0-.442.173T6 10.616v8.769q0 .269.173.442t.443.173M12 16.5q.633 0 1.066-.434q.434-.433.434-1.066t-.434-1.066T12 13.5t-1.066.434Q10.5 14.367 10.5 15t.434 1.066q.433.434 1.066.434M9 9h6V7q0-1.25-.875-2.125T12 4t-2.125.875T9 7zM6 20V10z"/></svg>
	`,
  openLock: `
		<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M6.616 9H15V7q0-1.25-.875-2.125T12 4t-2.125.875T9 7H8q0-1.671 1.165-2.835Q10.329 3 12 3t2.836 1.165T16 7v2h1.385q.67 0 1.143.472q.472.472.472 1.144v8.769q0 .67-.472 1.143q-.472.472-1.143.472H6.615q-.67 0-1.143-.472Q5 20.056 5 19.385v-8.77q0-.67.472-1.143Q5.944 9 6.616 9m0 11h10.769q.269 0 .442-.173t.173-.442v-8.77q0-.269-.173-.442T17.385 10H6.615q-.269 0-.442.173T6 10.616v8.769q0 .269.173.442t.443.173M12 16.5q.633 0 1.066-.434q.434-.433.434-1.066t-.434-1.066T12 13.5t-1.066.434Q10.5 14.367 10.5 15t.434 1.066q.433.434 1.066.434M6 20V10z"/></svg>
	`,
  paste: `
		<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36"><path fill="currentColor" d="M30 12h-4v2h4v2h2v-2a2 2 0 0 0-2-2" class="clr-i-solid clr-i-solid-path-1"/><path fill="currentColor" d="M30 18h2v6h-2z" class="clr-i-solid clr-i-solid-path-2"/><path fill="currentColor" d="M30 30h-2v2h2a2 2 0 0 0 2-2v-4h-2Z" class="clr-i-solid clr-i-solid-path-3"/><rect width="20" height="20" x="4" y="4" fill="currentColor" class="clr-i-solid clr-i-solid-path-4" rx="2" ry="2"/><path fill="currentColor" d="M20 30h6v2h-6z" class="clr-i-solid clr-i-solid-path-5"/><path fill="currentColor" d="M14 26h-2v4a2 2 0 0 0 2 2h4v-2h-4Z" class="clr-i-solid clr-i-solid-path-6"/><path fill="none" d="M0 0h36v36H0z"/></svg>
	`,
  copy: `
		<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="1.5"><path d="M20.998 10c-.012-2.175-.108-3.353-.877-4.121C19.243 5 17.828 5 15 5h-3c-2.828 0-4.243 0-5.121.879C6 6.757 6 8.172 6 11v5c0 2.828 0 4.243.879 5.121C7.757 22 9.172 22 12 22h3c2.828 0 4.243 0 5.121-.879C21 20.243 21 18.828 21 16v-1"/><path d="M3 10v6a3 3 0 0 0 3 3M18 5a3 3 0 0 0-3-3h-4C7.229 2 5.343 2 4.172 3.172C3.518 3.825 3.229 4.7 3.102 6"/></g></svg>
	`,
  quotations: `
		<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 64 64"><path fill="currentColor" d="M19.2 49.5c-1.2 0-2.3-.7-2.7-1.8l-3.3-8c-3.7-.6-7-2.5-9.1-5.4c-2.2-2.9-2.9-6.5-2-10c1.4-5.7 7.3-10 13.8-9.9c4.6.1 8.4 2 10.9 5.4c2.4 3.4 3 8 1.6 12.2c-1.3 3.8-3 7.6-4.6 11.3c-.6 1.5-1.3 3-1.9 4.4c-.4 1.1-1.5 1.8-2.7 1.8m-3.5-30.6c-4.4 0-8.4 2.8-9.3 6.5c-.5 2.2-.1 4.4 1.3 6.2c1.6 2.2 4.3 3.6 7.2 3.8l1.4.1l2.9 7c.1-.3.3-.7.4-1c1.6-3.6 3.2-7.3 4.4-11c1-2.8.6-5.9-1-8.1s-4.1-3.4-7.2-3.5zm36.6 30.7c-1.2 0-2.3-.7-2.7-1.8l-3.3-8c-3.7-.6-7-2.5-9.1-5.4c-2.2-2.9-2.9-6.5-2-10c1.4-5.7 7.3-10 13.8-9.9c4.6.1 8.4 2 10.8 5.4s3 8 1.6 12.2c-1.3 3.8-3 7.6-4.6 11.3c-.6 1.5-1.3 3-1.9 4.4c-.3 1.1-1.4 1.8-2.6 1.8m-3.5-30.7c-4.4 0-8.4 2.8-9.3 6.5c-.5 2.2-.1 4.4 1.3 6.3c1.6 2.2 4.3 3.6 7.2 3.8l1.4.1l2.9 7c.1-.3.3-.7.4-1c1.6-3.6 3.2-7.3 4.4-11c1-2.8.6-5.9-1-8.2c-1.6-2.2-4.1-3.4-7.2-3.5z"/></svg>
	`,
  filter: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.97 20.344L9 21v-8.5L4.52 7.572A2 2 0 0 1 4 6.227V4h16v2.172a2 2 0 0 1-.586 1.414L15 12v1.5m3.42 2.11a2.1 2.1 0 0 1 2.97 2.97L18 22h-3v-3z"/></svg>
	`,
  delete: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-width="1.5"><path stroke-linecap="round" d="M20.5 6h-17m15.333 2.5l-.46 6.9c-.177 2.654-.265 3.981-1.13 4.79s-2.196.81-4.856.81h-.774c-2.66 0-3.991 0-4.856-.81c-.865-.809-.954-2.136-1.13-4.79l-.46-6.9"/><path d="M6.5 6h.11a2 2 0 0 0 1.83-1.32l.034-.103l.097-.291c.083-.249.125-.373.18-.479a1.5 1.5 0 0 1 1.094-.788C9.962 3 10.093 3 10.355 3h3.29c.262 0 .393 0 .51.019a1.5 1.5 0 0 1 1.094.788c.055.106.097.23.18.479l.097.291A2 2 0 0 0 17.5 6"/></g></svg>
	`,
  downAngle: `
		<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 14 14"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M.5 3.85L6.65 10a.48.48 0 0 0 .7 0l6.15-6.15"/></svg>
	`,
  group: `
		<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 14 14"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><circle cx="5" cy="3.75" r="2.25"/><path d="M9.5 13.5h-9v-1a4.5 4.5 0 0 1 9 0ZM9 1.5A2.25 2.25 0 0 1 9 6m1.6 2.19a4.5 4.5 0 0 1 2.9 4.2v1.11H12"/></g></svg>
	`,
  edit: `
		<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 14 14"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><path d="M6.13 12.06C4.6 13.6 2 14.11.5 12.57C2.5 10.5.5 9.5 2 8a2.9 2.9 0 1 1 4.09 4.1Z"/><path d="M12.92 1.08A2 2 0 0 0 11.44.5a2 2 0 0 0-1.44.67l-5.38 6A2.85 2.85 0 0 1 6.13 8a3 3 0 0 1 .77 1.31L12.83 4a2 2 0 0 0 .67-1.43a2 2 0 0 0-.58-1.49Z"/></g></svg>
	`,
  import: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 14 14"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><path d="m1.91 9.5l-1.3 2.55a1 1 0 0 0 0 1a1 1 0 0 0 .87.47h11a1 1 0 0 0 .87-.47a1 1 0 0 0 0-1L12.09 9.5ZM5 2.5l2-2l2 2m-2-2v6"/><path d="M3 4.5a1 1 0 0 0-1 1v4h10v-4a1 1 0 0 0-1-1"/></g></svg>
	`,
  provider: `
		<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M10.75 2h2c1.886 0 2.828 0 3.414.586S16.75 4.114 16.75 6v15.25h5a.75.75 0 0 1 0 1.5h-20a.75.75 0 0 1 0-1.5h5V6c0-1.886 0-2.828.586-3.414S8.864 2 10.75 2m1 2.25a.75.75 0 0 1 .75.75v1.25h1.25a.75.75 0 0 1 0 1.5H12.5V9A.75.75 0 0 1 11 9V7.75H9.75a.75.75 0 0 1 0-1.5H11V5a.75.75 0 0 1 .75-.75M9 12a.75.75 0 0 1 .75-.75h4a.75.75 0 0 1 0 1.5h-4A.75.75 0 0 1 9 12m0 3a.75.75 0 0 1 .75-.75h4a.75.75 0 0 1 0 1.5h-4A.75.75 0 0 1 9 15m2.75 3.25a.75.75 0 0 1 .75.75v2.25H11V19a.75.75 0 0 1 .75-.75" clip-rule="evenodd"/><path fill="currentColor" d="M20.913 5.889c.337.504.337 1.206.337 2.611v12.75h.5a.75.75 0 0 1 0 1.5h-20a.75.75 0 1 1 0-1.5h.5V8.5c0-1.405 0-2.107.337-2.611a2 2 0 0 1 .552-.552c.441-.295 2.537-.332 3.618-.336q-.005.437-.004.91V7.25H4.25a.75.75 0 1 0 0 1.5h2.503v1.5H4.25a.75.75 0 0 0 0 1.5h2.503v1.5H4.25a.75.75 0 0 0 0 1.5h2.503v6.5h10v-6.5h2.497a.75.75 0 1 0 0-1.5h-2.497v-1.5h2.497a.75.75 0 1 0 0-1.5h-2.497v-1.5h2.497a.75.75 0 0 0 0-1.5h-2.497V5.91q.001-.471-.004-.91c1.081.005 3.17.042 3.612.337a2 2 0 0 1 .552.552" opacity=".5"/></svg>
	`,
  coverage: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 14 14"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><path d="M8.315 1.031a.5.5 0 0 0-.5.5v1.407H6.409a.5.5 0 0 0-.5.5v1.625a.5.5 0 0 0 .5.5h1.406v1.406a.5.5 0 0 0 .5.5H9.94a.5.5 0 0 0 .5-.5V5.563h1.406a.5.5 0 0 0 .5-.5V3.438a.5.5 0 0 0-.5-.5H10.44V1.53a.5.5 0 0 0-.5-.5zm-7.732 9.75l2.444 2.037a2 2 0 0 0 1.28.463h6.443c.46 0 .833-.373.833-.833c0-.92-.746-1.667-1.667-1.667H5.437"/><path d="m3.583 9.781l.75.75a1.06 1.06 0 1 0 1.5-1.5L4.669 7.867a2 2 0 0 0-1.414-.586H.583"/></g></svg>
	`,
  insured: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 14 14"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><circle cx="5" cy="2.75" r="2.25"/><path d="M6 6.61A4.49 4.49 0 0 0 .5 11v1.5H6m4.67.97h0a.5.5 0 0 1-.34 0h0A4.48 4.48 0 0 1 7.5 9.31V8a.47.47 0 0 1 .5-.5h5a.47.47 0 0 1 .5.5v1.31a4.48 4.48 0 0 1-2.83 4.16Z"/></g></svg>
	`,
  plus: `
		<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 14 14"><path fill="currentColor" fill-rule="evenodd" d="M8 1a1 1 0 0 0-2 0v5H1a1 1 0 0 0 0 2h5v5a1 1 0 1 0 2 0V8h5a1 1 0 1 0 0-2H8z" clip-rule="evenodd"/></svg>
	`,
  slash: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="currentColor" d="m7 21l7.9-18H17L9.1 21z"/></svg>
	`,
  back: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 14 14"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M13.5 7H.5M4 3.5L.5 7L4 10.5"/></svg>
	`,
  forward: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 14 14"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M.5 7h13M10 10.5L13.5 7L10 3.5"/></svg>
	`,
  spinner: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="currentColor" d="M10.72,19.9a8,8,0,0,1-6.5-9.79A7.77,7.77,0,0,1,10.4,4.16a8,8,0,0,1,9.49,6.52A1.54,1.54,0,0,0,21.38,12h.13a1.37,1.37,0,0,0,1.38-1.54,11,11,0,1,0-12.7,12.39A1.54,1.54,0,0,0,12,21.34h0A1.47,1.47,0,0,0,10.72,19.9Z"><animateTransform attributeName="transform" dur="0.75s" repeatCount="indefinite" type="rotate" values="0 12 12;360 12 12"/></path></svg>
	`,
  close: `
		<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 14 14"><path fill="currentColor" fill-rule="evenodd" d="M1.707.293A1 1 0 0 0 .293 1.707L5.586 7L.293 12.293a1 1 0 1 0 1.414 1.414L7 8.414l5.293 5.293a1 1 0 0 0 1.414-1.414L8.414 7l5.293-5.293A1 1 0 0 0 12.293.293L7 5.586z" clip-rule="evenodd"/></svg>
	`,
  eye: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 14 14"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><path d="M13.23 6.246c.166.207.258.476.258.754c0 .279-.092.547-.258.754C12.18 9.025 9.79 11.5 7 11.5S1.82 9.025.77 7.754A1.2 1.2 0 0 1 .512 7c0-.278.092-.547.258-.754C1.82 4.975 4.21 2.5 7 2.5s5.18 2.475 6.23 3.746"/><path d="M7 9a2 2 0 1 0 0-4a2 2 0 0 0 0 4"/></g></svg>
	`,
  eyeSlash: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 14 14"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M1.68 4.206C2.652 6.015 4.67 7.258 7 7.258s4.348-1.243 5.322-3.052M2.75 5.596L.5 7.481m4.916-.415L4.333 9.794m6.917-4.198l2.25 1.885m-4.92-.415l1.083 2.728"/></svg>
	`,
  search: `
		<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 14 14"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M6 11.5a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m7.5 2L10 10"/></svg>
	`,
  no_data: `
		<svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" width="862.70323" height="644.78592" viewBox="0 0 862.70323 644.78592" xmlns:xlink="http://www.w3.org/1999/xlink"><polygon points="629.943 612.644 612.777 612.644 604.608 546.435 629.943 546.435 629.943 612.644" fill="#9e616a"/><path d="M807.65107,769.99215H795.34112l-2.19727-11.62205-5.62754,11.62205H754.86738A7.33919,7.33919,0,0,1,750.697,756.6135l26.07247-18.00658v-11.7495l27.42368,1.63683Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><polygon points="731.923 590.981 718.148 601.224 672.085 552.969 692.415 537.851 731.923 590.981" fill="#9e616a"/><path d="M925.58816,737.04791,915.71,744.39344l-8.69827-8.015,2.41922,12.68419-26.19923,19.48211a7.33918,7.33918,0,0,1-11.32976-8.24721l10.17712-30.00728-7.0111-9.42842,22.98294-15.05066Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><path d="M818.57583,398.64705s32.56879,28.13791,17.542,108.35207l-18.3454,78.59653,59.8294,99.2561-19.07664,23.20771-77.77961-107.4334-28.18529-66.11365L744.6516,416.843Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><polygon points="599.447 425.746 597.488 456.084 603.483 585.365 631.692 580.452 637.083 488.406 599.447 425.746" fill="#2f2e41"/><polygon points="237.445 628.211 252.796 628.21 260.098 569.001 237.443 569.002 237.445 628.211" fill="#ffb6b6"/><path d="M402.178,750.80612l4.32074-.00018,16.86888-6.86018,9.0412,6.85913H432.41A19.26648,19.26648,0,0,1,451.67546,770.07v.62605l-49.49658.00183Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><polygon points="296.932 618.538 311.905 621.918 332.071 565.772 309.972 560.782 296.932 618.538" fill="#ffb6b6"/><path d="M462.86463,740.39329l4.21465.9516,17.96568-2.97583,7.3082,8.68223.0012.00027a19.26648,19.26648,0,0,1,14.54854,23.03569l-.1379.61067L458.48379,759.7967Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><path d="M386.6516,393.843c-7.19708,21.70636-6.43618,45.268,1.72992,70.55606l3.49087,142.37821S386.67128,700.146,403.4543,733.00177h24.34l12.05112-134.75129,1.5133-90.44591,52.18244,76.30583L460.30462,730.79868l29.9568,2.678,53.93408-159.1909L477.6516,419.843Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><path d="M667.346,332.01487c18.61732-16.77656,46.30893-25.21208,69.53714-15.805a115.466,115.466,0,0,0-51.888,59.93484c-3.6979,9.83846-6.78644,21.16623-15.88188,26.43349-5.65933,3.27753-12.70027,3.4377-19.04568,1.85557-6.34568-1.58237-12.16226-4.75415-17.89913-7.89422l-1.63218-.03691C637.86406,372.53682,648.72872,348.79142,667.346,332.01487Z" transform="translate(-168.64838 -127.60704)" fill="#e6e6e6"/><path d="M736.75328,316.71942A98.69239,98.69239,0,0,0,681.847,342.64994a42.50049,42.50049,0,0,0-8.34534,10.37667,24.37584,24.37584,0,0,0-2.81751,12.51568c.10054,4.05833.67335,8.19792-.21438,12.21a14.92537,14.92537,0,0,1-7.42454,9.68865c-4.54586,2.613-9.7595,3.43673-14.886,4.0651-5.692.69769-11.61526,1.33219-16.54238,4.5248-.597.38683-1.16231-.56211-.56622-.94836,8.57235-5.5546,19.41969-3.5335,28.63724-7.24065,4.30108-1.72983,8.10691-4.76631,9.454-9.35719,1.17794-4.01452.5909-8.2838.45359-12.39207a26.01068,26.01068,0,0,1,2.299-12.34028,39.29038,39.29038,0,0,1,7.9156-10.65924,95.74917,95.74917,0,0,1,24.3333-17.41978A100.44256,100.44256,0,0,1,736.743,315.61475c.70319-.09065.70886,1.01461.01026,1.10467Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M686.44718,337.79134a14.807,14.807,0,0,1,1.63241-19.1039c.50628-.49873,1.30506.26457.79811.764a13.71094,13.71094,0,0,0-1.48216,17.77371c.41512.5769-.53561,1.13983-.94836.56623Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M670.36216,363.49127a28.53932,28.53932,0,0,0,20.3938-4.08346c.59834-.38471,1.16384.56412.56622.94836a29.68517,29.68517,0,0,1-21.23023,4.20607c-.70085-.12626-.42683-1.19655.27021-1.071Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M714.44656,321.9478a8.38148,8.38148,0,0,0,6.2686,4.89443c.7021.11732.42732,1.18753-.27021,1.071a9.39213,9.39213,0,0,1-6.94675-5.39917.57084.57084,0,0,1,.19107-.7573.55506.55506,0,0,1,.75729.19107Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M762.46124,397.11454c-.44048-.06079-.881-.12157-1.32791-.1756a110.37862,110.37862,0,0,0-17.88208-.90839c-.46221.00673-.93053.02051-1.39159.0405a116.3646,116.3646,0,0,0-41.75015,9.61014,113.00482,113.00482,0,0,0-15.16291,8.0555c-6.68773,4.23438-13.602,9.35764-21.078,11.08459a19.38584,19.38584,0,0,1-2.36217.42086l-30.88864-26.74546c-.03969-.096-.0858-.18531-.12584-.28162l-1.28212-1.01147c.23872-.17556.49008-.35251.72879-.52808.138-.10241.283-.19887.421-.30128.09422-.06639.18881-.13253.27-.19782.03128-.02222.0629-.04413.08811-.05934.08122-.06529.1636-.11732.23871-.17556q2.10345-1.4895,4.23516-2.95463c.00611-.007.00611-.007.0191-.00815a166.15689,166.15689,0,0,1,34.601-18.59939c.36686-.13859.73948-.28453,1.12045-.4109a107.831,107.831,0,0,1,16.93919-4.76651,95.32878,95.32878,0,0,1,9.5528-1.33433,79.272,79.272,0,0,1,24.72335,1.7516c16.14332,3.7433,30.90977,12.60785,39.65578,26.43254C762.02688,396.40555,762.24387,396.75367,762.46124,397.11454Z" transform="translate(-168.64838 -127.60704)" fill="#e6e6e6"/><path d="M762.05235,397.44645a98.69236,98.69236,0,0,0-59.45156-12.3533A42.50006,42.50006,0,0,0,689.69,388.35387a24.3758,24.3758,0,0,0-9.78493,8.29673c-2.36313,3.30088-4.39808,6.951-7.52245,9.62a14.92533,14.92533,0,0,1-11.76132,3.26575c-5.2028-.6506-9.86156-3.13185-14.3331-5.71664-4.9648-2.86991-10.0762-5.92951-15.93241-6.34685-.70956-.05056-.5896-1.14861.11888-1.09812,10.1888.72611,17.633,8.8707,27.22462,11.46035,4.47564,1.20837,9.34256,1.07528,13.18213-1.77925,3.35754-2.49617,5.45923-6.25839,7.82305-9.62129a26.01082,26.01082,0,0,1,9.26529-8.46889,39.29037,39.29037,0,0,1,12.73777-3.74506,95.74907,95.74907,0,0,1,29.91669.7416,100.44263,100.44263,0,0,1,32.085,11.59611c.616.351-.04488,1.23688-.65689.88819Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M709.199,383.98345a14.807,14.807,0,0,1,12.80526-14.27057c.7045-.09339.88272.997.17729,1.0905a13.711,13.711,0,0,0-11.88443,13.29895c-.01588.71056-1.11391.58761-1.09812-.11888Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M680.88287,394.81911a28.53928,28.53928,0,0,0,18.74183,9.01806c.70936.05308.58963,1.15113-.11888,1.09812a29.68518,29.68518,0,0,1-19.4835-9.42375c-.48357-.52277.37961-1.21236.86055-.69243Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M741.09383,388.19084a8.38147,8.38147,0,0,0,2.05834,7.68205c.49.51638-.37378,1.20545-.86055.69243a9.39216,9.39216,0,0,1-2.29591-8.49336.57082.57082,0,0,1,.6085-.48962.55506.55506,0,0,1,.48962.6085Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M219.92162,754.74293c-1.45,5.44-5.26,9.97-9.86,13.27-.75.54-1.52,1.04-2.3,1.51-.24.14-.48.29-.73.42q-.405.24-.81.45h-21.63c-.39-.79-.77-1.59-1.15-2.38-9.27-19.48-15.78-40.5-14.67-61.91a79.25417,79.25417,0,0,1,5.17-24.25c5.94-15.47,16.78-28.86,31.69-35.6.37-.17.76-.34,1.14-.5-.12.43-.24.85-.36,1.28a110.78533,110.78533,0,0,0-3.38,17.59c-.06.46-.11.92-.15,1.39a116.05427,116.05427,0,0,0,3.72,42.69c.01.03.01995.07.03.1q1.27506,4.605,2.96,9.07c.88,2.35,1.83,4.67,2.87,6.95C216.80163,734.393,222.62157,744.593,219.92162,754.74293Z" transform="translate(-168.64838 -127.60704)" fill="#e6e6e6"/><path d="M207.04162,646.203c-.21.28-.42005.55-.63.83a98.12885,98.12885,0,0,0-11.12,18.76c-.16.33-.31.66-.44,1a97.8135,97.8135,0,0,0-7.82,29.24,1.49,1.49,0,0,0-.02.21c-.25,2.36005-.4,4.74-.46,7.12a42.48011,42.48011,0,0,0,1.43,13.24,23.7688,23.7688,0,0,0,5.46,9.42c.25.27.5.54.77.8.2.21.42.42.63.62,2.02,1.93,4.23,3.72,6.13,5.79a21.43163,21.43163,0,0,1,2.35,3,14.90407,14.90407,0,0,1,1.6,12.1c-1.36,5.06-4.47,9.33-7.65,13.4-1.59,2.04-3.23,4.1-4.65,6.28-.51995.78-1,1.57-1.43994,2.38h-1.26c.42-.81.88-1.6,1.38-2.38,3.65-5.75,8.84-10.69,11.53-17.02,1.82-4.26995,2.37-9.11.07-13.3a17.68156,17.68156,0,0,0-2.43-3.38c-1.83-2.07-4.02-3.84-6.01-5.71-.5-.47-.99-.95-1.46-1.45a24.96377,24.96377,0,0,1-5.64-8.9,39.23028,39.23028,0,0,1-1.94-13.13c0-2.84.15-5.7.43-8.54.03-.36.07-.73.11-1.1a100.76663,100.76663,0,0,1,19.67-49.23c.2-.28.41-.55.62-.82C206.68163,644.87294,207.47161,645.653,207.04162,646.203Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M186.36526,696.67763a14.807,14.807,0,0,1-12.3542-14.66278.55275.55275,0,0,1,1.10455-.02415,13.711,13.711,0,0,0,11.51986,13.616c.70147.11439.42725,1.18471-.27021,1.071Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M193.164,726.22406a28.5393,28.5393,0,0,0,11.53315-17.308c.15106-.69512,1.22186-.42407,1.071.27021a29.68514,29.68514,0,0,1-12.0379,17.98619c-.58485.40629-1.1479-.54428-.56622-.94836Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M194.96075,665.676a8.38149,8.38149,0,0,0,7.89345-.97168c.57941-.41351,1.14186.53754.56622.94836a9.39215,9.39215,0,0,1-8.72989,1.09429.57082.57082,0,0,1-.40038-.67059.55507.55507,0,0,1,.6706-.40038Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M282.06158,684.87294c-.35.27-.71.54-1.06.82a110.362,110.362,0,0,0-13.29,12c-.32.33-.64.67-.95,1.01l-.01.01a116.347,116.347,0,0,0-22.66,36.14l-.03.09c-.01.03-.02.05-.03.08a114.44321,114.44321,0,0,0-5.03,16.42c-1.22,5.46-2.22,11.31-4.13,16.57-.29.81-.61,1.61-.95,2.38h-44.46c.15-.79.31-1.59.47-2.38a160.30168,160.30168,0,0,1,10.54-33.7c.16-.36.32-.72.5-1.08a108.30478,108.30478,0,0,1,8.61-15.35.0098.0098,0,0,1,.01-.01,94.95585,94.95585,0,0,1,5.8-7.69,79.11871,79.11871,0,0,1,18.72-16.24c.04-.03.09-.05.13-.08,14.04-8.71,30.68-12.86,46.59-9.27h.01C281.25158,684.68294,281.6516,684.773,282.06158,684.87294Z" transform="translate(-168.64838 -127.60704)" fill="#e6e6e6"/><path d="M282.01159,685.403c-.34.09-.68.19-1.01.29a98.5888,98.5888,0,0,0-20.17,8.27c-.32.17-.64.35-.96.53a98.25544,98.25544,0,0,0-23.79,18.59.035.035,0,0,0-.01.02c-.08.08-.17.17-.24.25-1.6,1.72-3.14,3.51-4.6,5.35a42.769,42.769,0,0,0-6.82,11.43,23.67365,23.67365,0,0,0-1.31,10.81c.03.37.08.73.13,1.1.04.29.08.58.13.88.66,4.01,1.8,8.03,1.48,12.12a14.90913,14.90913,0,0,1-6.01,10.63,23.794,23.794,0,0,1-3.68,2.34,36.85232,36.85232,0,0,1-5.77,2.38h-3.93c.53-.15,1.05-.3,1.58-.45a48.21182,48.21182,0,0,0,5.53-1.93,26.912,26.912,0,0,0,3-1.48c4.02-2.31,7.37005-5.85,8.07-10.58.61-4.14-.57-8.28-1.27-12.33-.12-.7-.23-1.39-.29-2.08a24.43856,24.43856,0,0,1,.85-10.46,39.0623,39.0623,0,0,1,6.36-11.66,83.355,83.355,0,0,1,5.48-6.55q.36-.40494.75-.81a100.901,100.901,0,0,1,24.21-18.73h.01a99.28782,99.28782,0,0,1,21.1-8.74h.01c.33-.1.67-.2,1-.29C282.53161,684.12294,282.69158,685.213,282.01159,685.403Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M235.116,713.25243a14.807,14.807,0,0,1-1.03613-19.1455c.43212-.5642,1.32915.08079.89646.64574A13.711,13.711,0,0,0,235.97653,712.56c.49121.51367-.37215,1.20316-.86055.69243Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M222.75543,740.93692a28.53931,28.53931,0,0,0,19.62921-6.87574c.53912-.46406,1.2309.397.69242.86054a29.68514,29.68514,0,0,1-20.44051,7.11332c-.71159-.02772-.58885-1.12569.11888-1.09812Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M260.64411,693.67444a8.38149,8.38149,0,0,0,6.8875,3.97657c.71159.01869.58807,1.11668-.11888,1.09812a9.39215,9.39215,0,0,1-7.62917-4.38226.57083.57083,0,0,1,.08406-.77649.55507.55507,0,0,1,.77649.08406Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M625.03076,300.73673a11.59945,11.59945,0,0,1-17.7667.83759l-37.80039,16.44009,3.682-21.10161,35.3314-12.37668a11.66235,11.66235,0,0,1,16.55372,16.20061Z" transform="translate(-168.64838 -127.60704)" fill="#ffb8b8"/><path d="M599.80571,307.32525l-87.7976,39.10831-.18835-.06738-100.067-35.65889a32.95966,32.95966,0,0,1-14.78168-42.75569h0a32.92423,32.92423,0,0,1,46.9872-14.63652l74.4685,44.85908,72.21121-9.35878Z" transform="translate(-168.64838 -127.60704)" fill="#e6e6e6"/><path d="M1031.35162,771.203a1.1865,1.1865,0,0,1-1.19,1.19h-860.29a1.19,1.19,0,0,1,0-2.38h860.29A1.1865,1.1865,0,0,1,1031.35162,771.203Z" transform="translate(-168.64838 -127.60704)" fill="#ccc"/><path d="M481.99193,424.40352l-88.50585-14.15674a16.89334,16.89334,0,0,1-9.95557-23.646l4.01367-8.02832-1.55908-84.34668A62.48156,62.48156,0,0,1,416.32152,239.572l8.63086-5.16064,4.36182-11.07666,40.22022.981.11718,14.52734,14.40381,22.96826-.00049.09522-.90381,125.01367-3.96972,12.90137,6.00244,15.00586Z" transform="translate(-168.64838 -127.60704)" fill="#e6e6e6"/><circle cx="284.4591" cy="45.40997" r="36.54413" fill="#ffb8b8"/><path d="M415.05385,180.98352c-1.09-4.59187-.58956-11.05349.02641-15.677,1.61485-12.12129,8.3464-23.64474,18.57336-30.47048a13.37957,13.37957,0,0,1,6.66453-2.64845c2.41939-.101,5.04189,1.19418,5.78465,3.499a11.99254,11.99254,0,0,1,6.76552-6.709,21.1355,21.1355,0,0,1,9.63075-1.29746,35.19728,35.19728,0,0,1,29.36306,20.98947c.97609,2.3188,3.70246-6.24621,4.93916-4.05528a9.7407,9.7407,0,0,0,5.52388,4.85342c2.4233.67619,3.40756,10.66034,4.3612,8.33222a11.0984,11.0984,0,0,1-10.61055,15.47525c-2.46642-.09228-4.82489-.99947-7.262-1.39-8.71512-1.39642-17.96,4.92316-19.82312,13.55058a23.98689,23.98689,0,0,0-3.15565-7.021,8.1187,8.1187,0,0,0-6.51321-3.57866c-2.47957.09278-4.6591,1.7139-6.26793,3.60295s-2.81713,4.093-4.43782,5.97186c-4.7555,5.513-11.18745,18.3697-17.96453,17.432C425.30335,201.103,416.54206,187.25309,415.05385,180.98352Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><path d="M674.01238,342.14754a7.1328,7.1328,0,0,0-4.80706-7.85363l-98.41317-32.77709a7.13219,7.13219,0,0,0-2.933-.3368l-24.66687,2.33267-14.15377,1.34255-26.11867,2.46833a7.15519,7.15519,0,0,0-6.38357,5.98973l-13.26135,82.8376a7.18646,7.18646,0,0,0,4.48439,7.79592l99.4404,38.38442a6.94669,6.94669,0,0,0,1.44636.38836,7.13621,7.13621,0,0,0,2.17571.01648l64.25546-9.52349a7.12057,7.12057,0,0,0,6.023-5.99919Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M490.01349,398.1102l99.44009,38.38234a.89711.89711,0,0,0,.457.05366l64.247-9.52224a.88347.88347,0,0,0,.7549-.75161l12.91979-85.06677a.90469.90469,0,0,0-.59937-.98151l-.66169-.22392-97.75762-32.54588a.67787.67787,0,0,0-.13742-.03318.88732.88732,0,0,0-.23-.01192l-60.16426,5.6932-4.77428.44794a.90314.90314,0,0,0-.7947.74781l-13.259,82.83439A.89735.89735,0,0,0,490.01349,398.1102Z" transform="translate(-168.64838 -127.60704)" fill="#6b4ce8"/><path d="M508.28194,313.10237l60.16426-5.6932a.88732.88732,0,0,1,.23.01192.67787.67787,0,0,1,.13742.03318l97.75762,32.54588-25.78658,2.72965-9.65046,1.01669-27.46045,2.90123a1.939,1.939,0,0,1-.24081-.0029c-.04881-.01472-.09762-.02944-.15639-.04511Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><path d="M487.75761,403.95209l99.44009,38.38233a6.72242,6.72242,0,0,0,1.4505.37968,7.22358,7.22358,0,0,0,2.17727.02722l64.247-9.52224a7.13521,7.13521,0,0,0,6.02839-6.00387l12.90982-85.06772a7.19014,7.19014,0,0,0-.4184-3.71669c-.06533-.15688-.13072-.31384-.207-.46172a6.99031,6.99031,0,0,0-2.26369-2.69758,7.13789,7.13789,0,0,0-1.91579-.97662l-.11659-.04131-98.29175-32.73751a8.95539,8.95539,0,0,0-1.22721-.29807,7.08573,7.08573,0,0,0-1.71463-.03323l-24.66295,2.32468-14.15253,1.35L502.917,307.3259a7.09173,7.09173,0,0,0-3.01853.99744,1.32948,1.32948,0,0,0-.20245.12125,1.1922,1.1922,0,0,0-.12992.09813,7.14818,7.14818,0,0,0-3.02682,4.76367l-13.2699,82.84346A7.19418,7.19418,0,0,0,487.75761,403.95209Zm10.54219-90.35694a5.29965,5.29965,0,0,1,1.26984-2.6713,4.65147,4.65147,0,0,1,.67571-.65875,5.31719,5.31719,0,0,1,2.32365-1.08389,4.059,4.059,0,0,1,.50915-.07189l43.98466-4.15521,20.96479-1.995c.14217-.01658.27254-.01418.40386-.02168a5.00673,5.00673,0,0,1,.94761.07043,4.14489,4.14489,0,0,1,.84467.20125l98.4084,32.77882c.07775.02754.14554.05407.22323.0816a5.218,5.218,0,0,1,2.27305,1.6537,5.25912,5.25912,0,0,1,1.12074,4.14541l-12.92068,85.07674a5.34916,5.34916,0,0,1-4.5086,4.50155l-64.257,9.52134a5.41346,5.41346,0,0,1-2.72281-.31038l-99.441-38.37237a5.40237,5.40237,0,0,1-3.35921-5.846Z" transform="translate(-168.64838 -127.60704)" fill="#3f3d56"/><path d="M499.35216,308.99439a.87724.87724,0,0,1,.268-.38623,1.05132,1.05132,0,0,1,.129-.08817c.04169-.01607.08434-.04216.12611-.05828a.87349.87349,0,0,1,.62383-.01066l2.06994.73016,101.1157,35.66943,23.66513-2.5004,13.24288-1.39675,28.02932-2.96742,2.50639-.26279.48732-.05387a.9043.9043,0,0,1,.95216.65352.73938.73938,0,0,1,.02649.14313.893.893,0,0,1-.55014.92188.98843.98843,0,0,1-.24752.06673l-3.40944.35738-27.60268,2.91775-9.65046,1.01669-27.46045,2.90123a1.939,1.939,0,0,1-.24081-.0029c-.04881-.01472-.09762-.02944-.15639-.04511L500.24535,310.2651l-.3498-.1238a.67025.67025,0,0,1-.21942-.12146A.91016.91016,0,0,1,499.35216,308.99439Z" transform="translate(-168.64838 -127.60704)" fill="#3f3d56"/><path d="M588.91905,442.97456a.89376.89376,0,0,1-.74251-1.01574l14.51687-96.33414a.894.894,0,0,1,1.017-.75056l.008.00129a.89377.89377,0,0,1,.74252,1.01574l-14.51687,96.33414a.894.894,0,0,1-1.017.75055Z" transform="translate(-168.64838 -127.60704)" fill="#3f3d56"/><path d="M625.716,436.86342l-9.6548,1.01888,11.29337-95.5347s12.89458-2.33464,13.23951-1.39846C640.80631,341.50808,625.80805,436.25066,625.716,436.86342Z" transform="translate(-168.64838 -127.60704)" fill="#3f3d56"/><polygon points="331.25 182.533 330.99 226.1 408.116 255.488 435.813 218.284 331.25 182.533" fill="#3f3d56"/><path d="M671.13144,337.72465a5.30105,5.30105,0,0,0-2.49688-1.73654l-98.40594-32.7777a5.10582,5.10582,0,0,0-.848-.20665,5.00894,5.00894,0,0,0-.95065-.07115l.15966-.99731.98511-.71323,23.36822-16.9188,78.04053,23.91705.13549,27.05154Z" transform="translate(-168.64838 -127.60704)" fill="#3f3d56"/><path d="M503.829,380.07963a1.51326,1.51326,0,0,1,.326.06843l30.19365,9.91686a1.50014,1.50014,0,0,1-.93555,2.85069l-30.19364-9.91685a1.50039,1.50039,0,0,1,.60952-2.91913Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><circle cx="457.00322" cy="423.23593" r="12" fill="#f2f2f2"/><circle cx="151.00322" cy="467.23593" r="12" fill="#f2f2f2"/><circle cx="401.00322" cy="70.23593" r="12" fill="#f2f2f2"/><path d="M589.34024,397.72852A11.59947,11.59947,0,0,1,573.433,389.7714L532.421,385.62792l13.53022-16.60628,36.87128,6.48065a11.66236,11.66236,0,0,1,6.5177,22.22623Z" transform="translate(-168.64838 -127.60704)" fill="#ffb8b8"/><path d="M564.115,391.14082l-95.70849-8.81836-.13135-.15088L398.42455,302.135a32.95967,32.95967,0,0,1,8.01319-44.52344h0a32.92425,32.92425,0,0,1,48.14355,10.209l43.02246,75.54443,67.56543,27.147Z" transform="translate(-168.64838 -127.60704)" fill="#e6e6e6"/><path d="M804.33859,237.22376c-2.37688-17.43387-5.35788-36.15172-17.65411-48.7369a41.34992,41.34992,0,0,0-59.74384.61837c-8.95079,9.54876-12.90365,22.95672-13.2654,36.03983s2.55205,26.02081,5.78442,38.70347a119.28958,119.28958,0,0,0,49.78577-9.79937c3.92617-1.70407,7.789-3.63056,11.93689-4.68634,4.14784-1.05571,7.10454,1.60088,10.96292,3.45335l2.118-4.05545c1.73377,3.22659,7.10244,2.27017,9.04978-.83224C805.26007,244.82608,804.83352,240.853,804.33859,237.22376Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><path d="M736.532,334.53244l-69.876,1.49441a11.05455,11.05455,0,1,0-4.93974,15.57383c9.26761.52674,81.77191,10.81733,86.0974,4.18549,4.39027-6.73106,27.82423-30.48612,27.82423-30.48612l-18.01271-25.64378Z" transform="translate(-168.64838 -127.60704)" fill="#9e616a"/><circle cx="584.91096" cy="94.03525" r="32.83012" fill="#9e616a"/><path d="M599.36147,299.184" transform="translate(-168.64838 -127.60704)" fill="#6b4ce8"/><path d="M806.14195,284.81075c-3.86888-7.69981-5.74873-17.212-13.99671-19.70823-5.56965-1.68563-28.09691.84048-33.17312,3.6859-8.44356,4.73313-.79189,13.60234-5.77332,21.90214-5.41517,9.02271-20.132,27.12978-25.5472,36.15241-3.72279,6.20279,8.8171,24.40947,6.80408,31.358-2.01273,6.94848-2.10962,14.74736,1.31952,21.11722,3.06888,5.70141-1.37137,10.745,1.71521,16.437,3.20957,5.91962,7.14849,28.05274,4.16119,34.08785l-2,6c19.84682,1.16609,36.53459-22.54427,56.25813-25.04188,4.89894-.62032,9.98565-1.43073,14.02251-4.27435,5.94639-4.18864,8.29717-11.78923,9.76638-18.91282A159.32576,159.32576,0,0,0,806.14195,284.81075Z" transform="translate(-168.64838 -127.60704)" fill="#3f3d56"/><path d="M835.89793,366.11245c-2.76443-7.54563-7.769-40.5366-7.769-40.5366l-31.32417-.91848,15.31443,37.772-41.79036,58.50283s.07739.12853.21808.35778a11.052,11.052,0,1,0,9.26964,11.74483.76305.76305,0,0,0,.95807-.16445C785.42465,427.035,838.66236,373.65815,835.89793,366.11245Z" transform="translate(-168.64838 -127.60704)" fill="#9e616a"/><path d="M839.0826,345.27741c-2.87511-12.13478-5.77152-24.33549-10.61887-35.82566s-11.78661-22.34286-21.54669-30.10543c-3.12048-2.48179-6.609-4.67232-10.52078-5.44389-3.91147-.77165-8.31967.09193-11.0667,2.98137-4.39621,4.62357-3.07339,12.0451-1.4611,18.21781Q791,322.40224,798.13123,349.70286q20.59418-2.18287,41.188-4.36591Z" transform="translate(-168.64838 -127.60704)" fill="#3f3d56"/><path d="M793.7871,226.19592c-1.20908-7.942-2.47188-15.95043-5.31228-23.42857-2.8404-7.47821-7.41882-14.48249-13.98647-18.71882-10.39879-6.70709-23.862-5.41352-35.52074-1.55544-9.01622,2.9837-17.81761,7.51864-24.17574,14.8093-6.35848,7.29074-9.92957,17.69379-7.56439,27.22665q18.65464-4.40738,37.30893-8.81483l-1.36137.962a30.03765,30.03765,0,0,1,16.03083,20.8927,31.12209,31.12209,0,0,1-6.56554,25.84773q12.72244-4.51323,25.44489-9.0263c5.23526-1.85713,10.83833-3.997,13.94267-8.76047C795.62723,240.107,794.79091,232.78685,793.7871,226.19592Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/></svg>
	`,
  edit: `
	<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M17.6741 7.875C17.6741 7.50271 17.3723 7.20091 17 7.20091C16.6277 7.20091 16.3259 7.50271 16.3259 7.875H17.6741ZM10.625 2.17409C10.9973 2.17409 11.2991 1.87229 11.2991 1.5C11.2991 1.12771 10.9973 0.825914 10.625 0.825914V2.17409ZM9.5 15.8259C7.71318 15.8259 6.44087 15.8245 5.47505 15.6946C4.52862 15.5674 3.97805 15.3281 3.575 14.925L2.6217 15.8783C3.31699 16.5736 4.19948 16.8834 5.29541 17.0308C6.37194 17.1755 7.75129 17.1741 9.5 17.1741V15.8259ZM1.32591 9C1.32591 10.7487 1.32448 12.1281 1.46922 13.2046C1.61656 14.3005 1.9264 15.183 2.6217 15.8783L3.575 14.925C3.17195 14.5219 2.93261 13.9714 2.80537 13.0249C2.67552 12.0591 2.67409 10.7868 2.67409 9H1.32591ZM16.3259 9C16.3259 10.7868 16.3245 12.0591 16.1946 13.0249C16.0674 13.9714 15.8281 14.5219 15.425 14.925L16.3783 15.8783C17.0736 15.183 17.3834 14.3005 17.5308 13.2046C17.6755 12.1281 17.6741 10.7487 17.6741 9H16.3259ZM9.5 17.1741C11.2487 17.1741 12.6281 17.1755 13.7046 17.0308C14.8005 16.8834 15.683 16.5736 16.3783 15.8783L15.425 14.925C15.0219 15.3281 14.4714 15.5674 13.5249 15.6946C12.5591 15.8245 11.2868 15.8259 9.5 15.8259V17.1741ZM9.5 0.825914C7.75129 0.825914 6.37194 0.824483 5.29541 0.969219C4.19948 1.11656 3.31699 1.4264 2.6217 2.1217L3.575 3.075C3.97805 2.67195 4.52862 2.43261 5.47505 2.30537C6.44087 2.17552 7.71318 2.17409 9.5 2.17409V0.825914ZM2.67409 9C2.67409 7.21318 2.67552 5.94087 2.80537 4.97505C2.93261 4.02862 3.17195 3.47805 3.575 3.075L2.6217 2.1217C1.9264 2.81699 1.61656 3.69948 1.46922 4.79541C1.32448 5.87194 1.32591 7.25129 1.32591 9H2.67409ZM17.6741 9V7.875H16.3259V9H17.6741ZM9.5 2.17409H10.625V0.825914H9.5V2.17409Z" fill="white"/>
<path d="M13.4756 2.10468L12.999 1.62803L12.999 1.62803L13.4756 2.10468ZM16.3953 5.02435L16.872 5.501V5.501L16.3953 5.02435ZM6.52859 11.2672L5.88909 11.054L6.52859 11.2672ZM7.24317 9.12343L7.88266 9.3366L7.24317 9.12343ZM8.51538 7.06495L8.99203 7.5416L8.51538 7.06495ZM9.37657 11.2568L9.16341 10.6173L9.1634 10.6173L9.37657 11.2568ZM7.23282 11.9714L7.01965 11.3319H7.01965L7.23282 11.9714ZM10.8135 10.5694L10.3989 10.0379L10.3989 10.0379L10.8135 10.5694ZM10.1776 10.9624L9.88761 10.3539H9.88761L10.1776 10.9624ZM7.53758 8.32239L6.92907 8.03238L6.92907 8.03238L7.53758 8.32239ZM7.93057 7.68651L8.4621 8.10109H8.4621L7.93057 7.68651ZM8.075 11.6907L8.28816 12.3302H8.28817L8.075 11.6907ZM13.4657 3.06795L13.9523 2.58133L12.999 1.62803L12.5124 2.11464L13.4657 3.06795ZM15.9187 4.5477L15.4321 5.03431L16.3854 5.98762L16.872 5.501L15.9187 4.5477ZM12.989 2.5913C12.3161 2.63088 12.3161 2.63116 12.3161 2.63144C12.3162 2.63153 12.3162 2.63182 12.3162 2.63202C12.3162 2.63241 12.3162 2.63282 12.3163 2.63325C12.3163 2.63411 12.3164 2.63503 12.3164 2.63602C12.3166 2.63799 12.3167 2.64023 12.3169 2.64272C12.3173 2.6477 12.3177 2.6537 12.3183 2.6607C12.3195 2.67468 12.3212 2.69264 12.3237 2.71427C12.3287 2.75751 12.3366 2.81562 12.3493 2.88621C12.3746 3.02711 12.419 3.21945 12.4967 3.44332C12.652 3.8911 12.9431 4.47192 13.4856 5.01439L14.4389 4.06109C14.069 3.69116 13.8735 3.29876 13.7703 3.00142C13.7188 2.85273 13.6909 2.72953 13.6763 2.64803C13.669 2.60742 13.6651 2.57762 13.6631 2.56074C13.6622 2.55232 13.6617 2.54717 13.6616 2.54556C13.6615 2.54476 13.6615 2.54485 13.6616 2.54586C13.6616 2.54637 13.6617 2.5471 13.6617 2.54808C13.6618 2.54856 13.6618 2.54911 13.6618 2.54972C13.6619 2.55002 13.6619 2.55034 13.6619 2.55067C13.6619 2.55083 13.6619 2.5511 13.6619 2.55118C13.6619 2.55144 13.662 2.55171 12.989 2.5913ZM13.4856 5.01439C14.0281 5.55686 14.6089 5.848 15.0567 6.00335C15.2806 6.08102 15.4729 6.1254 15.6138 6.15069C15.6844 6.16336 15.7425 6.17131 15.7857 6.17627C15.8074 6.17875 15.8253 6.18048 15.8393 6.18168C15.8463 6.18228 15.8523 6.18275 15.8573 6.18311C15.8598 6.18329 15.862 6.18344 15.864 6.18357C15.865 6.18363 15.8659 6.18369 15.8667 6.18374C15.8672 6.18377 15.8676 6.1838 15.868 6.18382C15.8682 6.18383 15.8685 6.18385 15.8686 6.18386C15.8688 6.18387 15.8691 6.18389 15.9087 5.51097C15.9483 4.83804 15.9486 4.83806 15.9488 4.83807C15.9489 4.83808 15.9492 4.83809 15.9493 4.8381C15.9497 4.83813 15.95 4.83814 15.9503 4.83816C15.9509 4.8382 15.9514 4.83824 15.9519 4.83827C15.9529 4.83833 15.9536 4.83838 15.9541 4.83842C15.9552 4.83849 15.9552 4.83851 15.9544 4.83844C15.9528 4.8383 15.9477 4.83783 15.9393 4.83687C15.9224 4.83493 15.8926 4.83101 15.852 4.82372C15.7705 4.8091 15.6473 4.78124 15.4986 4.72966C15.2012 4.6265 14.8088 4.43102 14.4389 4.06109L13.4856 5.01439ZM8.99203 7.5416L13.4657 3.06795L12.5124 2.11464L8.03873 6.5883L8.99203 7.5416ZM15.4321 5.03431L10.9584 9.50797L11.9117 10.4613L16.3854 5.98762L15.4321 5.03431ZM10.9584 9.50797C10.6442 9.82221 10.5258 9.93897 10.3989 10.0379L11.2281 11.101C11.4353 10.9393 11.6199 10.753 11.9117 10.4613L10.9584 9.50797ZM9.58973 11.8963C9.98119 11.7658 10.2303 11.684 10.4676 11.5709L9.88761 10.3539C9.7424 10.4231 9.58501 10.4768 9.16341 10.6173L9.58973 11.8963ZM10.3989 10.0379C10.2405 10.1615 10.069 10.2675 9.88761 10.3539L10.4676 11.5709C10.7374 11.4424 10.9924 11.2848 11.2281 11.101L10.3989 10.0379ZM7.13973 11.3603C7.17116 11.3917 7.18213 11.4382 7.16808 11.4803L5.88909 11.054C5.74166 11.4963 5.85677 11.9839 6.18643 12.3136L7.13973 11.3603ZM7.88266 9.3366C8.0232 8.91499 8.07689 8.7576 8.14609 8.61239L6.92907 8.03238C6.81598 8.26966 6.73416 8.51881 6.60368 8.91027L7.88266 9.3366ZM8.03873 6.5883C7.74696 6.88007 7.56071 7.06467 7.39905 7.27194L8.4621 8.10109C8.56103 7.97425 8.67779 7.85584 8.99203 7.5416L8.03873 6.5883ZM8.14609 8.61239C8.23255 8.43099 8.33851 8.25954 8.4621 8.10109L7.39905 7.27194C7.21524 7.5076 7.05765 7.76259 6.92907 8.03238L8.14609 8.61239ZM6.18643 12.3136C6.51609 12.6432 7.0037 12.7583 7.44598 12.6109L7.01965 11.3319C7.06182 11.3179 7.1083 11.3288 7.13973 11.3603L6.18643 12.3136ZM15.9187 2.58133C16.4617 3.12433 16.4617 4.00471 15.9187 4.5477L16.872 5.501C17.9415 4.43151 17.9415 2.69752 16.872 1.62803L15.9187 2.58133ZM16.872 1.62803C15.8025 0.558541 14.0685 0.558541 12.999 1.62803L13.9523 2.58133C14.4953 2.03834 15.3757 2.03834 15.9187 2.58133L16.872 1.62803ZM9.1634 10.6173L7.86184 11.0512L8.28817 12.3302L9.58973 11.8963L9.1634 10.6173ZM7.86184 11.0512L7.01965 11.3319L7.44598 12.6109L8.28816 12.3302L7.86184 11.0512ZM7.16808 11.4803L7.44881 10.6382L6.16982 10.2118L5.88909 11.054L7.16808 11.4803ZM7.44881 10.6382L7.88266 9.3366L6.60368 8.91027L6.16982 10.2118L7.44881 10.6382ZM8.55165 11.214L7.28596 9.94835L6.33266 10.9017L7.59835 12.1673L8.55165 11.214Z" fill="white"/>
</svg>
	`,
  capsule: `
<svg width="84" height="84" viewBox="0 0 84 84" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M46.4755 25.5897L55.4266 16.6386M27.0816 27.0816L56.9184 56.9184M9.17942 74.8206C0.940193 66.5814 0.940193 53.2229 9.17942 44.9837L44.9837 9.17942C53.2229 0.940193 66.5814 0.940192 74.8206 9.17942C83.0598 17.4187 83.0598 30.7771 74.8206 39.0163L39.0163 74.8206C30.7771 83.0598 17.4187 83.0598 9.17942 74.8206Z" stroke="#2E7987" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

`,
  tube: `
<svg width="84" height="84" viewBox="0 0 84 84" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M29.6002 3H54.3998M29.6002 3V21.3459C29.6002 22.7747 29.2248 24.1789 28.511 25.4194L3.60139 68.7159C0.452277 74.1895 4.43152 81 10.7788 81H73.2212C79.5685 81 83.5477 74.1895 80.3986 68.7159L55.489 25.4195C54.7752 24.1789 54.3998 22.7747 54.3998 21.3459V3M29.6002 3H21.3336M54.3998 3H62.6664M29.6002 62.5263H54.3998" stroke="#2E7987" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>


`,
  allhands: `
<svg width="82" height="84" viewBox="0 0 82 84" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.5 50.2014H28.2579C32.2207 50.2014 36.1835 56.1015 40.1462 56.1015C43.5713 56.1015 49.9568 53.163 52.9059 58.7152C53.8379 60.4699 52.5931 62.475 50.747 63.2347C46.4709 64.9945 41.7081 67.3851 40.1462 67.9019L28.2579 63.9684M54.0159 60.035L72.1494 52.8352C73.2503 52.3981 74.4475 52.2557 75.6212 52.4221C79.7693 53.0103 80.9412 58.4037 77.406 60.6367L47.1152 79.7701C45.2032 80.9778 42.8619 81.3153 40.6828 80.6973L2.5 69.8686M40.1462 42.3345C32.5179 39.6689 18.351 28.2099 18.351 15.6049C18.351 8.64345 23.718 3 30.3384 3C34.3926 3 37.9768 5.11637 40.1462 8.35572C42.3156 5.11637 45.8998 3 49.9541 3C56.5745 3 61.9414 8.64345 61.9414 15.6049C61.9414 28.2099 47.7745 39.6689 40.1462 42.3345Z" stroke="#2E7987" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

`,
  care: `
<svg width="83" height="57" viewBox="0 0 83 57" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M47.6582 30.0488C47.6582 19.959 49.9271 12.5117 54.4648 7.70703C59.056 2.90234 65.569 0.5 74.0039 0.5V10.1895C71.5482 10.1895 69.3594 10.4297 67.4375 10.9102C65.569 11.3906 63.9674 12.2715 62.6328 13.5527C61.3516 14.834 60.3639 16.569 59.6699 18.7578C58.9759 20.9466 58.5755 23.7227 58.4688 27.0859H73.6836V47.9062H47.6582V30.0488ZM0.412109 30.0488C0.412109 19.959 2.68099 12.5117 7.21875 7.70703C11.8099 2.90234 18.3229 0.5 26.7578 0.5V10.1895C24.3021 10.1895 22.1133 10.4297 20.1914 10.9102C18.3229 11.3906 16.7214 12.2715 15.3867 13.5527C14.1055 14.834 13.1178 16.569 12.4238 18.7578C11.7298 20.9466 11.3294 23.7227 11.2227 27.0859H26.4375V47.9062H0.412109V30.0488ZM64.9551 21.6406C65.222 19.6654 65.9961 18.224 67.2773 17.3164C68.612 16.3555 70.8542 15.875 74.0039 15.875H79.4492V4.98438H82.6523V18.918H73.6836C72.4557 18.918 71.388 18.9714 70.4805 19.0781C69.6263 19.1849 68.8789 19.3717 68.2383 19.6387C67.5977 19.8522 67.0104 20.1191 66.4766 20.4395C65.9961 20.7598 65.4889 21.1602 64.9551 21.6406ZM17.709 21.6406C17.9759 19.6654 18.75 18.224 20.0312 17.3164C21.3659 16.3555 23.6081 15.875 26.7578 15.875H32.2031V4.98438H35.4062V18.918H26.4375C25.2096 18.918 24.1419 18.9714 23.2344 19.0781C22.3802 19.1849 21.6328 19.3717 20.9922 19.6387C20.3516 19.8522 19.7643 20.1191 19.2305 20.4395C18.75 20.7598 18.2428 21.1602 17.709 21.6406ZM52.4629 53.5117H79.4492V30.0488H82.6523V56.5547H52.4629V53.5117ZM5.2168 53.5117H32.2031V30.0488H35.4062V56.5547H5.2168V53.5117Z" fill="#18E4A1"/>
</svg>

`,
  location: `
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.5 11C15.5 12.933 13.933 14.5 12 14.5C10.067 14.5 8.5 12.933 8.5 11C8.5 9.067 10.067 7.5 12 7.5C13.933 7.5 15.5 9.067 15.5 11Z" stroke="#18E4A1" stroke-width="1.5"/>
<path d="M12 2C16.8706 2 21 6.03298 21 10.9258C21 15.8965 16.8033 19.3847 12.927 21.7567C12.6445 21.9162 12.325 22 12 22C11.675 22 11.3555 21.9162 11.073 21.7567C7.2039 19.3616 3 15.9137 3 10.9258C3 6.03298 7.12944 2 12 2Z" stroke="#18E4A1" stroke-width="1.5"/>
</svg>

`,
  email: `
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7 8.5L9.94202 10.2394C11.6572 11.2535 12.3428 11.2535 14.058 10.2394L17 8.5" stroke="#18E4A1" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M2.01577 13.4756C2.08114 16.5412 2.11383 18.0739 3.24496 19.2094C4.37608 20.3448 5.95033 20.3843 9.09883 20.4634C11.0393 20.5122 12.9607 20.5122 14.9012 20.4634C18.0497 20.3843 19.6239 20.3448 20.7551 19.2094C21.8862 18.0739 21.9189 16.5412 21.9842 13.4756C22.0053 12.4899 22.0053 11.5101 21.9842 10.5244C21.9189 7.45886 21.8862 5.92609 20.7551 4.79066C19.6239 3.65523 18.0497 3.61568 14.9012 3.53657C12.9607 3.48781 11.0393 3.48781 9.09882 3.53656C5.95033 3.61566 4.37608 3.65521 3.24495 4.79065C2.11382 5.92608 2.08114 7.45885 2.01576 10.5244C1.99474 11.5101 1.99475 12.4899 2.01577 13.4756Z" stroke="#18E4A1" stroke-width="1.5" stroke-linejoin="round"/>
</svg>

`,
  phone: `
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.77762 11.9424C2.8296 10.2893 2.37185 8.93948 2.09584 7.57121C1.68762 5.54758 2.62181 3.57081 4.16938 2.30947C4.82345 1.77638 5.57323 1.95852 5.96 2.6524L6.83318 4.21891C7.52529 5.46057 7.87134 6.08139 7.8027 6.73959C7.73407 7.39779 7.26737 7.93386 6.33397 9.00601L3.77762 11.9424ZM3.77762 11.9424C5.69651 15.2883 8.70784 18.3013 12.0576 20.2224M12.0576 20.2224C13.7107 21.1704 15.0605 21.6282 16.4288 21.9042C18.4524 22.3124 20.4292 21.3782 21.6905 19.8306C22.2236 19.1766 22.0415 18.4268 21.3476 18.04L19.7811 17.1668C18.5394 16.4747 17.9186 16.1287 17.2604 16.1973C16.6022 16.2659 16.0661 16.7326 14.994 17.666L12.0576 20.2224Z" stroke="#18E4A1" stroke-width="1.5" stroke-linejoin="round"/>
<path d="M3.77762 11.9424C2.8296 10.2893 2.37185 8.93948 2.09584 7.57121C1.68762 5.54758 2.62181 3.57081 4.16938 2.30947C4.82345 1.77638 5.57323 1.95852 5.96 2.6524L6.83318 4.21891C7.52529 5.46057 7.87134 6.08139 7.8027 6.73959C7.73407 7.39779 7.26737 7.93386 6.33397 9.00601L3.77762 11.9424Z" stroke="#18E4A1" stroke-width="1.5" stroke-linejoin="round"/>
<path d="M12.0576 20.2224C13.7107 21.1704 15.0605 21.6282 16.4288 21.9042C18.4524 22.3124 20.4292 21.3782 21.6905 19.8306C22.2236 19.1766 22.0415 18.4268 21.3476 18.04L19.7811 17.1668C18.5394 16.4747 17.9186 16.1287 17.2604 16.1973C16.6022 16.2659 16.0661 16.7326 14.994 17.666L12.0576 20.2224Z" stroke="#18E4A1" stroke-width="1.5" stroke-linejoin="round"/>
</svg>

`,
  drugs: `<svg width="41" height="41" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="20.2436" cy="20.2436" r="20.1157" fill="#55291B" fill-opacity="0.5"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.201 11.1919C12.0901 11.1919 11.1895 12.0925 11.1895 13.2035V27.2844C11.1895 28.3954 12.0901 29.296 13.201 29.296H27.282C28.393 29.296 29.2936 28.3954 29.2936 27.2844V13.2035C29.2936 12.0925 28.393 11.1919 27.282 11.1919H13.201ZM16.2184 21.2497C16.2184 20.6942 15.7681 20.244 15.2126 20.244C14.6571 20.244 14.2068 20.6942 14.2068 21.2497V25.2729C14.2068 25.8284 14.6571 26.2787 15.2126 26.2787C15.7681 26.2787 16.2184 25.8284 16.2184 25.2729V21.2497ZM20.2415 17.2266C20.797 17.2266 21.2473 17.6769 21.2473 18.2324V25.2729C21.2473 25.8284 20.797 26.2787 20.2415 26.2787C19.686 26.2787 19.2357 25.8284 19.2357 25.2729V18.2324C19.2357 17.6769 19.686 17.2266 20.2415 17.2266ZM26.2762 15.215C26.2762 14.6595 25.8259 14.2092 25.2704 14.2092C24.715 14.2092 24.2646 14.6595 24.2646 15.215V25.2729C24.2646 25.8284 24.715 26.2787 25.2704 26.2787C25.8259 26.2787 26.2762 25.8284 26.2762 25.2729V15.215Z" fill="white"/>
</svg>
`,
  compounding: `<svg width="41" height="41" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="20.7436" cy="20.2436" r="20.1157" fill="#FFCF00"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.6973 14.2087C12.6973 11.9868 14.4985 10.1855 16.7204 10.1855H22.7551V14.2087C22.7551 16.4306 24.5563 18.2318 26.7782 18.2318H28.7898V26.2781C28.7898 28.5 26.9886 30.3012 24.7667 30.3012H16.7204C14.4985 30.3012 12.6973 28.5 12.6973 26.2781V14.2087ZM16.7204 19.2376C16.1649 19.2376 15.7146 19.6879 15.7146 20.2434C15.7146 20.7989 16.1649 21.2492 16.7204 21.2492H18.732C19.2874 21.2492 19.7378 20.7989 19.7378 20.2434C19.7378 19.6879 19.2874 19.2376 18.732 19.2376H16.7204ZM16.7204 23.2607C16.1649 23.2607 15.7146 23.711 15.7146 24.2665C15.7146 24.822 16.1649 25.2723 16.7204 25.2723H20.7435C21.299 25.2723 21.7493 24.822 21.7493 24.2665C21.7493 23.711 21.299 23.2607 20.7435 23.2607H16.7204ZM25.4524 12.3971L25.3207 14.3725C25.2802 14.9797 25.784 15.4834 26.3912 15.443L28.3666 15.3113C29.2288 15.2538 29.622 14.2075 29.0109 13.5965L27.1672 11.7528C26.5561 11.1417 25.5099 11.5348 25.4524 12.3971Z" fill="white"/>
</svg>
`,
  export: `<svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.677 6.09632L8.99997 3.80194C8.87498 3.69237 8.71719 3.63631 8.55857 3.63623C8.45004 3.63617 8.34112 3.66232 8.24205 3.71547C8.19791 3.73909 8.15596 3.76794 8.11718 3.80194L5.4401 6.09632C5.15892 6.3373 5.12633 6.7606 5.36732 7.04179C5.6083 7.32297 6.0316 7.35555 6.31278 7.11457L7.88801 5.76453V10.9575C7.888 11.3278 8.18821 11.628 8.55853 11.628C8.92885 11.628 9.22905 11.3278 9.22905 10.9575L9.22905 5.76445L10.8044 7.11457C11.0855 7.35555 11.5088 7.32297 11.7498 7.04179C11.9908 6.7606 11.9582 6.3373 11.677 6.09632ZM4.53538 10.3415C4.53538 9.97114 4.83558 9.67094 5.2059 9.67094H6.21169C6.58201 9.67094 6.88221 9.37073 6.88221 9.00041C6.88221 8.63009 6.58201 8.32989 6.21169 8.32989H5.2059C4.09495 8.32989 3.19434 9.2305 3.19434 10.3415V12.353C3.19434 13.464 4.09495 14.3646 5.2059 14.3646H11.9111C13.0221 14.3646 13.9227 13.464 13.9227 12.353V10.3415C13.9227 9.2305 13.0221 8.32989 11.9111 8.32989H10.9053C10.535 8.32989 10.2348 8.63009 10.2348 9.00041C10.2348 9.37073 10.535 9.67094 10.9053 9.67094H11.9111C12.2815 9.67094 12.5817 9.97114 12.5817 10.3415V12.353C12.5817 12.7233 12.2815 13.0236 11.9111 13.0236H5.2059C4.83558 13.0236 4.53538 12.7233 4.53538 12.353V10.3415Z" fill="#0F3659"/>
</svg>
`,
  allPatient: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.8954 5.90025L13.5765 6.37439C13.0694 6.44682 12.6552 6.68851 12.3593 7.02252L4.61537 14.7665C3.82981 15.5521 3.82978 16.8257 4.61538 17.6113L7.46016 20.4561C8.24576 21.2417 9.51938 21.2416 10.305 20.4561L18.0489 12.7121C18.3829 12.4163 18.6246 12.002 18.6971 11.495L19.1712 8.17605C19.3609 6.84853 18.2229 5.71055 16.8954 5.90025ZM14.5721 10.4993C14.9649 10.8921 15.6017 10.8921 15.9945 10.4993C16.3873 10.1065 16.3873 9.46967 15.9945 9.07691C15.6018 8.68416 14.9649 8.68413 14.5721 9.07691C14.1793 9.4697 14.1794 10.1066 14.5721 10.4993Z" fill="white"/>
</svg>

`,
  newPatient: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.20102 3.5061C4.09006 3.5061 3.18945 4.40673 3.18945 5.51767V19.5987C3.18945 20.7096 4.09006 21.6102 5.20102 21.6102H19.282C20.393 21.6102 21.2936 20.7096 21.2936 19.5987V5.51767C21.2936 4.40673 20.393 3.5061 19.282 3.5061H5.20102ZM8.21837 13.5639C8.21837 13.0084 7.76807 12.5582 7.21259 12.5582C6.65711 12.5582 6.20681 13.0084 6.20681 13.5639V17.5871C6.20681 18.1426 6.65711 18.5929 7.21259 18.5929C7.76807 18.5929 8.21837 18.1426 8.21837 17.5871V13.5639ZM12.2415 9.54081C12.797 9.54081 13.2473 9.99109 13.2473 10.5466V17.5871C13.2473 18.1426 12.797 18.5929 12.2415 18.5929C11.686 18.5929 11.2357 18.1426 11.2357 17.5871V10.5466C11.2357 9.99109 11.686 9.54081 12.2415 9.54081ZM18.2762 7.52924C18.2762 6.97374 17.8259 6.52346 17.2704 6.52346C16.715 6.52346 16.2646 6.97374 16.2646 7.52924V17.5871C16.2646 18.1426 16.715 18.5929 17.2704 18.5929C17.8259 18.5929 18.2762 18.1426 18.2762 17.5871V7.52924Z" fill="white"/>
</svg>

`,
  returningPatient: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.19727 6.52289C4.19727 4.30098 5.99849 2.49976 8.2204 2.49976H14.2551V6.52289C14.2551 8.74481 16.0563 10.546 18.2782 10.546H20.2898V18.5923C20.2898 20.8142 18.4886 22.6154 16.2667 22.6154H8.2204C5.99849 22.6154 4.19727 20.8142 4.19727 18.5923V6.52289ZM8.2204 11.5518C7.66492 11.5518 7.21462 12.0021 7.21462 12.5576C7.21462 13.1131 7.66492 13.5634 8.2204 13.5634H10.232C10.7874 13.5634 11.2378 13.1131 11.2378 12.5576C11.2378 12.0021 10.7874 11.5518 10.232 11.5518H8.2204ZM8.2204 15.575C7.66492 15.575 7.21462 16.0253 7.21462 16.5807C7.21462 17.1362 7.66492 17.5865 8.2204 17.5865H12.2435C12.799 17.5865 13.2493 17.1362 13.2493 16.5807C13.2493 16.0253 12.799 15.575 12.2435 15.575H8.2204ZM16.9524 4.71126L16.8207 6.68671C16.7802 7.29389 17.284 7.79765 17.8912 7.75717L19.8666 7.62548C20.7288 7.56799 21.122 6.52175 20.5109 5.91072L18.6672 4.06697C18.0561 3.45594 17.0099 3.84905 16.9524 4.71126Z" fill="white"/>
</svg>

`,
  leftArrow: `
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_95_1639)">
<path d="M14 8L10 12L14 16" stroke="#444444" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M1 12C1 6.81455 1 4.22183 2.61091 2.61091C4.22183 1 6.81455 1 12 1C17.1855 1 19.7782 1 21.3891 2.61091C23 4.22183 23 6.81455 23 12C23 17.1854 23 19.7782 21.3891 21.3891C19.7782 23 17.1855 23 12 23C6.81455 23 4.22183 23 2.61091 21.3891C1 19.7782 1 17.1854 1 12Z" stroke="#444444" stroke-width="1.5"/>
</g>
<defs>
<clipPath id="clip0_95_1639">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>


`,
  dashboard: `<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.4697 2.30798H12.9697C11.4697 2.30798 10.7197 3.05798 10.7197 4.55798V6.05798C10.7197 7.55798 11.4697 8.30798 12.9697 8.30798H14.4697C15.9697 8.30798 16.7197 7.55798 16.7197 6.05798V4.55798" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M3.96973 17.308H5.46973C6.96973 17.308 7.71973 16.558 7.71973 15.058V13.558C7.71973 12.058 6.96973 11.308 5.46973 11.308H3.96973C2.46973 11.308 1.71973 12.058 1.71973 13.558V15.058" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M4.71973 8.30798C6.37658 8.30798 7.71973 6.96484 7.71973 5.30798C7.71973 3.65113 6.37658 2.30798 4.71973 2.30798C3.06287 2.30798 1.71973 3.65113 1.71973 5.30798C1.71973 6.96484 3.06287 8.30798 4.71973 8.30798Z" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M13.7197 17.308C15.3766 17.308 16.7197 15.9648 16.7197 14.308C16.7197 12.6511 15.3766 11.308 13.7197 11.308C12.0629 11.308 10.7197 12.6511 10.7197 14.308C10.7197 15.9648 12.0629 17.308 13.7197 17.308Z" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  transfers: `<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.4697 12.172V5.047C14.4697 4.222 13.7947 3.547 12.9697 3.547H8.84473" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.7197 1.672L8.46973 3.547L10.7197 5.422" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14.4697 16.672C15.7124 16.672 16.7197 15.6646 16.7197 14.422C16.7197 13.1794 15.7124 12.172 14.4697 12.172C13.2271 12.172 12.2197 13.1794 12.2197 14.422C12.2197 15.6646 13.2271 16.672 14.4697 16.672Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M3.96973 9.172V13.297C3.96973 14.122 4.64473 14.797 5.46973 14.797H9.59473" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.71973 16.672L9.96973 14.797L7.71973 12.922" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M3.96973 6.172C5.21237 6.172 6.21973 5.16464 6.21973 3.922C6.21973 2.67936 5.21237 1.672 3.96973 1.672C2.72709 1.672 1.71973 2.67936 1.71973 3.922C1.71973 5.16464 2.72709 6.172 3.96973 6.172Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  purchases: `<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1.71973 13.319H9.21973" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M4.48656 17.024H3.39154C2.05654 17.024 1.72656 16.694 1.72656 15.374V12.299C1.72656 10.979 2.05654 10.649 3.39154 10.649H7.56154C8.89654 10.649 9.22656 10.979 9.22656 12.299V15.3815C9.22656 16.7015 8.89654 17.0315 7.56154 17.0315" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16.7197 11.774C16.7197 14.6765 14.3722 17.024 11.4697 17.024L12.2572 15.7115" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M1.71973 7.27405C1.71973 4.37155 4.06723 2.02405 6.96973 2.02405L6.18224 3.33655" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14.0947 8.77405C15.9622 8.77405 17.4697 7.26655 17.4697 5.39905C17.4697 3.53155 15.9622 2.02405 14.0947 2.02405C12.2272 2.02405 10.7197 3.53155 10.7197 5.39905C10.7197 5.84155 10.8022 6.26155 10.9597 6.64405" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  centralinventory: `<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">

Bubu, [5/19/25 12:46 PM]
<path d="M1.71973 10.4985V12.126C1.71973 15.876 3.21973 17.376 6.96973 17.376H11.4697C15.2197 17.376 16.7197 15.876 16.7197 12.126V7.62598C16.7197 3.87598 15.2197 2.37598 11.4697 2.37598H6.96973C3.21973 2.37598 1.71973 3.87598 1.71973 7.62598" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.80244 9.23853H5.81495C5.34245 9.23853 4.95996 9.62099 4.95996 10.0935V13.9335H7.80244V9.23853V9.23853Z" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.78992 5.82593H8.64991C8.17741 5.82593 7.79492 6.20844 7.79492 6.68094V13.926H10.6374V6.68094C10.6374 6.20844 10.2624 5.82593 9.78992 5.82593Z" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.632 10.5134H10.6445V13.926H13.487V11.3684C13.4795 10.8959 13.097 10.5134 12.632 10.5134Z" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  drugslibrary: `<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.5944 8.47803V12.978C15.5944 13.083 15.5944 13.188 15.5869 13.293" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M2.84473 13.728V5.47803C2.84473 2.47803 3.59473 1.72803 6.59473 1.72803H11.8447C14.8447 1.72803 15.5947 2.47803 15.5947 5.47803" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M4.98223 11.478H15.5947V14.103C15.5947 15.5505 14.4172 16.728 12.9697 16.728H5.46973C4.02223 16.728 2.84473 15.5505 2.84473 14.103V13.6155C2.84473 12.438 3.80473 11.478 4.98223 11.478Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6.21973 5.47803H12.2197" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6.21973 8.10303H9.96973" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  managewebsite: `<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1.7207 6.02496C1.7207 3.35496 2.28321 2.37998 4.36071 2.14748C4.75071 2.09498 5.17821 2.07996 5.67321 2.07996H12.7757C13.2632 2.07996 13.6982 2.09498 14.0882 2.14748C16.1657 2.37998 16.7282 3.35496 16.7282 6.02496V10.765C16.7282 13.435 16.1657 14.41 14.0882 14.6425C13.6982 14.695 13.2707 14.71 12.7757 14.71H5.67321C5.18571 14.71 4.75071 14.695 4.36071 14.6425C2.28321 14.41 1.7207 13.435 1.7207 10.765V8.90494" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.4062 6.81995H13.1662" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5.27539 11.1625H5.29038H13.1729" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5.4707 17.08H12.9707" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5.61697 6.80493H5.62371" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8.09158 6.80493H8.09832" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  users: `<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.7193 6.30201C13.6743 6.29451 13.6218 6.29451 13.5768 6.30201C12.5418 6.26451 11.7168 5.41701 11.7168 4.36701C11.7168 3.29451 12.5793 2.43201 13.6518 2.43201C14.7243 2.43201 15.5868 3.30201 15.5868 4.36701C15.5793 5.41701 14.7543 6.26451 13.7193 6.30201Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.9463 11.762C13.9738 11.9345 15.1063 11.7545 15.9013 11.222C16.9588 10.517 16.9588 9.36201 15.9013 8.65701C15.0988 8.12451 13.9513 7.9445 12.9238 8.1245" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>

Bubu, [5/19/25 12:46 PM]
<path d="M4.69758 6.30201C4.74258 6.29451 4.79508 6.29451 4.84008 6.30201C5.87508 6.26451 6.70008 5.41701 6.70008 4.36701C6.70008 3.29451 5.83758 2.43201 4.76508 2.43201C3.69258 2.43201 2.83008 3.30201 2.83008 4.36701C2.83758 5.41701 3.66258 6.26451 4.69758 6.30201Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5.4698 11.762C4.4423 11.9345 3.3098 11.7545 2.5148 11.222C1.4573 10.517 1.4573 9.36201 2.5148 8.65701C3.3173 8.12451 4.4648 7.9445 5.4923 8.1245" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.2193 11.9045C9.1743 11.897 9.1218 11.897 9.0768 11.9045C8.0418 11.867 7.2168 11.0195 7.2168 9.96955C7.2168 8.89705 8.0793 8.03455 9.1518 8.03455C10.2243 8.03455 11.0868 8.90455 11.0868 9.96955C11.0793 11.0195 10.2543 11.8745 9.2193 11.9045Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M11.4023 14.267C10.2098 13.472 8.23727 13.472 7.03727 14.267C5.97977 14.972 5.97977 16.127 7.03727 16.832C8.23727 17.6345 10.2023 17.6345 11.4023 16.832" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  roles: `<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.3717 13.7839C15.0842 13.2514 15.6692 12.0889 15.6692 11.2039V5.63144C15.6692 4.70894 14.9642 3.68892 14.1017 3.36642L10.3592 1.96393C9.73666 1.73143 8.71667 1.73143 8.09417 1.96393L4.35168 3.36642C3.48918 3.68892 2.78418 4.70894 2.78418 5.63144V11.2039C2.78418 12.0889 3.36917 13.2514 4.08167 13.7839L7.30668 16.1914C8.36418 16.9864 10.1042 16.9864 11.1617 16.1914L11.7167 15.7714" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.2195 8.474C9.1895 8.474 9.15199 8.474 9.12199 8.474C8.41699 8.4515 7.85449 7.86649 7.85449 7.15399C7.85449 6.42649 8.447 5.83398 9.1745 5.83398C9.902 5.83398 10.4945 6.42649 10.4945 7.15399C10.487 7.87399 9.9245 8.4515 9.2195 8.474Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.7275 10.5741C7.0075 11.0541 7.0075 11.8416 7.7275 12.3216C8.545 12.8691 9.8875 12.8691 10.705 12.3216C11.425 11.8416 11.425 11.0541 10.705 10.5741C9.895 10.0266 8.5525 10.0266 7.7275 10.5741Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  privileges: `<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1.71973 10.3635V11.886C1.71973 15.636 3.21973 17.136 6.96973 17.136H11.4697C15.2197 17.136 16.7197 15.636 16.7197 11.886V7.38599C16.7197 3.63599 15.2197 2.13599 11.4697 2.13599H6.96973C3.21973 2.13599 1.71973 3.63599 1.71973 7.38599" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.4296 10.8435C11.5821 11.691 10.3671 11.9535 9.29463 11.616L7.35213 13.551C7.21713 13.6935 6.93963 13.7835 6.73713 13.7535L5.83713 13.6335C5.53713 13.596 5.26713 13.311 5.22213 13.0185L5.10213 12.1185C5.07213 11.9235 5.16963 11.646 5.30463 11.5035L7.23963 9.56847C6.90963 8.49597 7.16463 7.28097 8.01213 6.43347C9.22713 5.21847 11.2071 5.21847 12.4296 6.43347C13.6446 7.64097 13.6446 9.62097 12.4296 10.8435Z" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8.05742 12.8461L7.41992 12.201" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.2654 8.66089H10.2721" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  notifications: `<svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">

Bubu, [5/19/25 12:46 PM]
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.35203 13.925C13.1386 13.925 14.8904 13.4387 15.0595 11.4866C15.0595 9.53581 13.8382 9.66125 13.8382 7.26776C13.8382 5.39817 12.0683 3.271 9.35203 3.271C6.63578 3.271 4.86583 5.39817 4.86583 7.26776C4.86583 9.66125 3.64453 9.53581 3.64453 11.4866C3.81438 13.446 5.56613 13.925 9.35203 13.925Z" stroke="white" stroke-width="1.1415" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.8741 16.208C10.0213 17.2167 8.69101 17.2286 7.83008 16.208" stroke="white" stroke-width="1.1415" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  bransches: `<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.9697 5.32007C15.9697 3.66257 15.2197 2.32007 12.9697 2.32007H5.46973C3.21973 2.32007 2.46973 3.66257 2.46973 5.32007V17.3201H15.9697V8.33507" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M1.71973 17.3201H16.7197" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.7654 12.0701H7.66788C7.28538 12.0701 6.96289 12.3851 6.96289 12.7751V17.3201H11.4629V12.7751C11.4704 12.3851 11.1554 12.0701 10.7654 12.0701Z" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.21973 5.32007V9.07007" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.34473 7.19507H11.0947" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  addbranch: `<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M16.3301 4.5C16.3301 2.8425 15.5801 1.5 13.3301 1.5H5.83008C3.58008 1.5 2.83008 2.8425 2.83008 4.5V16.5H16.3301V7.515" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M2.08008 16.5H17.0801" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M11.1248 11.25H8.02726C7.64476 11.25 7.32227 11.565 7.32227 11.955V16.5H11.8223V11.955C11.8298 11.565 11.5148 11.25 11.1248 11.25Z" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.58008 4.5V8.25" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.70508 6.375H11.4551" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  editbranch: `
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_5776_3226)">
      <path d="M17.1741 7.875C17.1741 7.50271 16.8723 7.20091 16.5 7.20091C16.1277 7.20091 15.8259 7.50271 15.8259 7.875H17.1741ZM10.125 2.17409C10.4973 2.17409 10.7991 1.87229 10.7991 1.5C10.7991 1.12771 10.4973 0.825914 10.125 0.825914V2.17409ZM9 15.8259C7.21318 15.8259 5.94087 15.8245 4.97505 15.6946C4.02862 15.5674 3.47805 15.3281 3.075 14.925L2.1217 15.8783C2.81699 16.5736 3.69948 16.8834 4.79541 17.0308C5.87194 17.1755 7.25129 17.1741 9 17.1741V15.8259ZM0.825914 9C0.825914 10.7487 0.824483 12.1281 0.969219 13.2046C1.11656 14.3005 1.4264 15.183 2.1217 15.8783L3.075 14.925C2.67195 14.5219 2.43261 13.9714 2.30537 13.0249C2.17552 12.0591 2.17409 10.7868 2.17409 9H0.825914ZM15.8259 9C15.8259 10.7868 15.8245 12.0591 15.6946 13.0249C15.5674 13.9714 15.3281 14.5219 14.925 14.925L15.8783 15.8783C16.5736 15.183 16.8834 14.3005 17.0308 13.2046C17.1755 12.1281 17.1741 10.7487 17.1741 9H15.8259ZM9 17.1741C10.7487 17.1741 12.1281 17.1755 13.2046 17.0308C14.3005 16.8834 15.183 16.5736 15.8783 15.8783L14.925 14.925C14.5219 15.3281 13.9714 15.5674 13.0249 15.6946C12.0591 15.8245 10.7868 15.8259 9 15.8259V17.1741ZM9 0.825914C7.25129 0.825914 5.87194 0.824483 4.79541 0.969219C3.69948 1.11656 2.81699 1.4264 2.1217 2.1217L3.075 3.075C3.47805 2.67195 4.02862 2.43261 4.97505 2.30537C5.94087 2.17552 7.21318 2.17409 9 2.17409V0.825914ZM2.17409 9C2.17409 7.21318 2.17552 5.94087 2.30537 4.97505C2.43261 4.02862 2.67195 3.47805 3.075 3.075L2.1217 2.1217C1.4264 2.81699 1.11656 3.69948 0.969219 4.79541C0.824483 5.87194 0.825914 7.25129 0.825914 9H2.17409ZM17.1741 9V7.875H15.8259V9H17.1741ZM9 2.17409H10.125V0.825914H9V2.17409Z" fill="#4C4B4B"/>
      <path d="M12.9756 2.10468L12.499 1.62803L12.499 1.62803L12.9756 2.10468ZM15.8953 5.02435L16.372 5.501V5.501L15.8953 5.02435ZM6.02859 11.2672L5.38909 11.054L6.02859 11.2672ZM6.74317 9.12343L7.38266 9.3366L6.74317 9.12343ZM8.01538 7.06495L8.49203 7.5416L8.01538 7.06495ZM8.87657 11.2568L8.66341 10.6173L8.6634 10.6173L8.87657 11.2568ZM6.73282 11.9714L6.51965 11.3319H6.51965L6.73282 11.9714ZM10.3135 10.5694L9.89891 10.0379L9.89891 10.0379L10.3135 10.5694ZM9.67761 10.9624L9.38761 10.3539H9.38761L9.67761 10.9624ZM7.03758 8.32239L6.42907 8.03238L6.42907 8.03238L7.03758 8.32239ZM7.43057 7.68651L7.9621 8.10109H7.9621L7.43057 7.68651ZM7.575 11.6907L7.78816 12.3302H7.78817L7.575 11.6907ZM12.9657 3.06795L13.4523 2.58133L12.499 1.62803L12.0124 2.11464L12.9657 3.06795ZM15.4187 4.5477L14.9321 5.03431L15.8854 5.98762L16.372 5.501L15.4187 4.5477ZM12.489 2.5913C11.8161 2.63088 11.8161 2.63116 11.8161 2.63144C11.8162 2.63153 11.8162 2.63182 11.8162 2.63202C11.8162 2.63241 11.8162 2.63282 11.8163 2.63325C11.8163 2.63411 11.8164 2.63503 11.8164 2.63602C11.8166 2.63799 11.8167 2.64023 11.8169 2.64272C11.8173 2.6477 11.8177 2.6537 11.8183 2.6607C11.8195 2.67468 11.8212 2.69264 11.8237 2.71427C11.8287 2.75751 11.8366 2.81562 11.8493 2.88621C11.8746 3.02711 11.919 3.21945 11.9967 3.44332C12.152 3.8911 12.4431 4.47192 12.9856 5.01439L13.9389 4.06109C13.569 3.69116 13.3735 3.29876 13.2703 3.00142C13.2188 2.85273 13.1909 2.72953 13.1763 2.64803C13.169 2.60742 13.1651 2.57762 13.1631 2.56074C13.1622 2.55232 13.1617 2.54717 13.1616 2.54556C13.1615 2.54476 13.1615 2.54485 13.1616 2.54586C13.1616 2.54637 13.1617 2.5471 13.1617 2.54808C13.1618 2.54856 13.1618 2.54911 13.1618 2.54972C13.1619 2.55002 13.1619 2.55034 13.1619 2.55067C13.1619 2.55083 13.1619 2.5511 13.1619 2.55118C13.1619 2.55144 13.162 2.55171 12.489 2.5913ZM12.9856 5.01439C13.5281 5.55686 14.1089 5.848 14.5567 6.00335C14.7806 6.08102 14.9729 6.1254 15.1138 6.15069C15.1844 6.16336 15.2425 6.17131 15.2857 6.17627C15.3074 6.17875 15.3253 6.18048 15.3393 6.18168C15.3463 6.18228 15.3523 6.18275 15.3573 6.18311C15.3598 6.18329 15.362 6.18344 15.364 6.18357C15.365 6.18363 15.3659 6.18369 15.3667 6.18374C15.3672 6.18377 15.3676 6.1838 15.368 6.18382C15.3682 6.18383 15.3685 6.18385 15.3686 6.18386C15.3688 6.18387 15.3691 6.18389 15.4087 5.51097C15.4483 4.83804 15.4486 4.83806 15.4488 4.83807C15.4489 4.83808 15.4492 4.83809 15.4493 4.8381C15.4497 4.83813 15.45 4.83814 15.4503 4.83816C15.4509 4.8382 15.4514 4.83824 15.4519 4.83827C15.4529 4.83833 15.4536 4.83838 15.4541 4.83842C15.4552 4.83849 15.4552 4.83851 15.4544 4.83844C15.4528 4.8383 15.4477 4.83783 15.4393 4.83687C15.4224 4.83493 15.3926 4.83101 15.352 4.82372C15.2705 4.8091 15.1473 4.78124 14.9986 4.72966C14.7012 4.6265 14.3088 4.43102 13.9389 4.06109L12.9856 5.01439ZM8.49203 7.5416L12.9657 3.06795L12.0124 2.11464L7.53873 6.5883L8.49203 7.5416ZM14.9321 5.03431L10.4584 9.50797L11.4117 10.4613L15.8854 5.98762L14.9321 5.03431ZM10.4584 9.50797C10.1442 9.82221 10.0258 9.93897 9.89891 10.0379L10.7281 11.101C10.9353 10.9393 11.1199 10.753 11.4117 10.4613L10.4584 9.50797ZM9.08973 11.8963C9.48119 11.7658 9.73033 11.684 9.96762 11.5709L9.38761 10.3539C9.2424 10.4231 9.08501 10.4768 8.66341 10.6173L9.08973 11.8963ZM9.89891 10.0379C9.74046 10.1615 9.56901 10.2675 9.38761 10.3539L9.96762 11.5709C10.2374 11.4424 10.4924 11.2848 10.7281 11.101L9.89891 10.0379ZM6.63973 11.3603C6.67116 11.3917 6.68213 11.4382 6.66808 11.4803L5.38909 11.054C5.24166 11.4963 5.35677 11.9839 5.68643 12.3136L6.63973 11.3603ZM7.38266 9.3366C7.5232 8.91499 7.57689 8.7576 7.64609 8.61239L6.42907 8.03238C6.31598 8.26966 6.23416 8.51881 6.10368 8.91027L7.38266 9.3366ZM7.53873 6.5883C7.24696 6.88007 7.06071 7.06467 6.89905 7.27194L7.9621 8.10109C8.06103 7.97425 8.17779 7.85584 8.49203 7.5416L7.53873 6.5883ZM7.64609 8.61239C7.73255 8.43099 7.83851 8.25954 7.9621 8.10109L6.89905 7.27194C6.71524 7.5076 6.55765 7.76259 6.42907 8.03238L7.64609 8.61239ZM5.68643 12.3136C6.01609 12.6432 6.5037 12.7583 6.94598 12.6109L6.51965 11.3319C6.56182 11.3179 6.6083 11.3288 6.63973 11.3603L5.68643 12.3136ZM15.4187 2.58133C15.9617 3.12433 15.9617 4.00471 15.4187 4.5477L16.372 5.501C17.4415 4.43151 17.4415 2.69752 16.372 1.62803L15.4187 2.58133ZM16.372 1.62803C15.3025 0.558541 13.5685 0.558541 12.499 1.62803L13.4523 2.58133C13.9953 2.03834 14.8757 2.03834 15.4187 2.58133L16.372 1.62803ZM8.6634 10.6173L7.36184 11.0512L7.78817 12.3302L9.08973 11.8963L8.6634 10.6173ZM7.36184 11.0512L6.51965 11.3319L6.94598 12.6109L7.78816 12.3302L7.36184 11.0512ZM6.66808 11.4803L6.94881 10.6382L5.66982 10.2118L5.38909 11.054L6.66808 11.4803ZM6.94881 10.6382L7.38266 9.3366L6.10368 8.91027L5.66982 10.2118L6.94881 10.6382ZM8.05165 11.214L6.78596 9.94835L5.83266 10.9017L7.09835 12.1673L8.05165 11.214Z" fill="#4C4B4B"/>
      </g>
      <defs>
      <clipPath id="clip0_5776_3226">
      <rect width="18" height="18" fill="white"/>
      </clipPath>
      </defs>
    </svg>

  `,
  changestatus: `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_5776_3229)">
<path d="M13.875 4.125L4.12501 13.8748" stroke="#1D1C1B" stroke-width="1.34817" stroke-linecap="round"/>
<circle cx="9" cy="9" r="7.5" stroke="#1D1C1B" stroke-width="1.34817"/>
</g>
<defs>
<clipPath id="clip0_5776_3229">
<rect width="18" height="18" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  delete: `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.3751 4.5H2.625" stroke="#F45B69" stroke-width="1.5" stroke-linecap="round"/>
<path d="M14.125 6.375L13.78 11.5493C13.6473 13.5405 13.5809 14.5361 12.9322 15.1431C12.2834 15.75 11.2856 15.75 9.29001 15.75H8.70999C6.71439 15.75 5.71659 15.75 5.06783 15.1431C4.41907 14.5361 4.3527 13.5405 4.21996 11.5493L3.875 6.375" stroke="#F45B69" stroke-width="1.5" stroke-linecap="round"/>
<path d="M7.125 8.25L7.5 12" stroke="#F45B69" stroke-width="1.5" stroke-linecap="round"/>
<path d="M10.875 8.25L10.5 12" stroke="#F45B69" stroke-width="1.5" stroke-linecap="round"/>

Bubu, [5/19/25 12:46 PM]
<path d="M4.875 4.5C4.91691 4.5 4.93786 4.5 4.95686 4.49952C5.57444 4.48387 6.11927 4.09118 6.32941 3.51024C6.33588 3.49237 6.3425 3.47249 6.35576 3.43273L6.42857 3.21429C6.49073 3.02781 6.52181 2.93457 6.56304 2.8554C6.72751 2.53955 7.03181 2.32023 7.38346 2.26407C7.4716 2.25 7.56988 2.25 7.76645 2.25H10.2336C10.4301 2.25 10.5284 2.25 10.6165 2.26407C10.9682 2.32023 11.2725 2.53955 11.437 2.8554C11.4782 2.93457 11.5093 3.02781 11.5714 3.21429L11.6442 3.43273C11.6575 3.47244 11.6641 3.49238 11.6706 3.51024C11.8807 4.09118 12.4256 4.48387 13.0431 4.49952C13.0621 4.5 13.0831 4.5 13.125 4.5" stroke="#F45B69" stroke-width="1.5"/>
</svg>
`,
  leftcirclearrow: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.5 12H9.5" stroke="#1D1C1B" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M11.5 9L8.5 12L11.5 15" stroke="#1D1C1B" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M4 6C2.75 7.67 2 9.75 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2C10.57 2 9.2 2.3 7.97 2.85" stroke="#1D1C1B" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  selectArrow: `<svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.64758 2.32581L1.19596 5.74864C0.940167 6.00198 0.527612 6.00198 0.271819 5.74864C0.149623 5.62792 0.0808916 5.46327 0.0808916 5.29153C0.0808916 5.11979 0.149623 4.95514 0.271819 4.83442L4.18482 0.954489C4.44033 0.700345 4.85345 0.700345 5.10896 0.954489L9.02196 4.83426C9.14416 4.95514 9.21289 5.11979 9.21289 5.29153C9.21289 5.46327 9.14416 5.62792 9.02196 5.7488C8.76613 6.00198 8.35357 6.00198 8.09782 5.74847L4.64758 2.32581Z" fill="white" fill-opacity="0.48"/>
</svg>
`,

  contact: `<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1.76562 9.675C1.76562 7.05 2.81563 6 5.44063 6H8.59063C11.2156 6 12.2656 7.05 12.2656 9.675V12.825C12.2656 15.45 11.2156 16.5 8.59063 16.5H5.44063C2.81563 16.5 1.76562 15.45 1.76562 12.825" stroke="#1D1C1B" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16.7656 8.325C16.7656 10.95 15.7156 12 13.0906 12H12.2656V9.675C12.2656 7.05 11.2156 6 8.59063 6H6.26562V5.175C6.26562 2.55 7.31562 1.5 9.94062 1.5H13.0906C15.7156 1.5 16.7656 2.55 16.7656 5.175" stroke="#1D1C1B" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  Drugs: `<svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M6.12 5.46045H11.8725C12.3525 5.46045 12.7425 5.85045 12.7425 6.33045V7.29045C12.7425 7.64295 12.525 8.07795 12.3075 8.29545L10.4325 9.95295C10.17 10.1704 9.9975 10.6054 9.9975 10.9579V12.8329C9.9975 13.0954 9.825 13.4404 9.6075 13.5754L9 13.9579C8.43 14.3104 7.65 13.9129 7.65 13.2154V10.9054C7.65 10.5979 7.4775 10.2079 7.2975 9.99045L5.64 8.24295C5.4225 8.03295 5.25 7.63545 5.25 7.37295V6.37545C5.25 5.85045 5.64 5.46045 6.12 5.46045Z" stroke="white" stroke-width="1.125" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M1.5 10.1629V11.7229C1.5 15.4729 3 16.9729 6.75 16.9729H11.25C15 16.9729 16.5 15.4729 16.5 11.7229V7.2229C16.5 3.4729 15 1.9729 11.25 1.9729H6.75C3 1.9729 1.5 3.4729 1.5 7.2229" stroke="white" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  DAte: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-500">
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
          </svg>`,
  leftArrowRounded: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M11.943 1.25h.114c2.309 0 4.118 0 5.53.19c1.444.194 2.584.6 3.479 1.494c.895.895 1.3 2.035 1.494 3.48c.19 1.411.19 3.22.19 5.529v.114c0 2.309 0 4.118-.19 5.53c-.194 1.444-.6 2.584-1.494 3.479c-.895.895-2.035 1.3-3.48 1.494c-1.411.19-3.22.19-5.529.19h-.114c-2.309 0-4.118 0-5.53-.19c-1.444-.194-2.584-.6-3.479-1.494c-.895-.895-1.3-2.035-1.494-3.48c-.19-1.411-.19-3.22-.19-5.529v-.114c0-2.309 0-4.118.19-5.53c.194-1.444.6-2.584 1.494-3.479c.895-.895 2.035-1.3 3.48-1.494c1.411-.19 3.22-.19 5.529-.19m-5.33 1.676c-1.278.172-2.049.5-2.618 1.069c-.57.57-.897 1.34-1.069 2.619c-.174 1.3-.176 3.008-.176 5.386s.002 4.086.176 5.386c.172 1.279.5 2.05 1.069 2.62c.57.569 1.34.896 2.619 1.068c1.3.174 3.008.176 5.386.176s4.086-.002 5.386-.176c1.279-.172 2.05-.5 2.62-1.069c.569-.57.896-1.34 1.068-2.619c.174-1.3.176-3.008.176-5.386s-.002-4.086-.176-5.386c-.172-1.279-.5-2.05-1.069-2.62c-.57-.569-1.34-.896-2.619-1.068c-1.3-.174-3.008-.176-5.386-.176s-4.086.002-5.386.176M14.03 8.47a.75.75 0 0 1 0 1.06L11.56 12l2.47 2.47a.75.75 0 1 1-1.06 1.06l-3-3a.75.75 0 0 1 0-1.06l3-3a.75.75 0 0 1 1.06 0" clip-rule="evenodd"/></svg>
`,
};
