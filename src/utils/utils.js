import { addToast } from "@/toast";
import ApiService from "@/service/ApiService";

export const UserRole = {
  Physician: "Physician",
  Admin: "Admin",
  SuperAdmin: "SuperAdmin",
  Cashier: "Cashier",
  Patient: "Patient",
  Payer: "Payer",
  StoreKeeper: "StoreKeeper",
  Pharmacist: "Pharmacist",
  Registration: "Registration",
  Case_Team_Leader: 'CaseTeamLeader'
};

export const BranchStatus = {
  ACTIVE: "ACTIVE",
  INACTIVE: "INACTIVE",
};

export function getApi(url, baseUrl) {
  let path;
  if (!baseUrl) {
    path = `${import.meta.env.v_API_URL}${url}`;
  } else {
    path = `${baseUrl}${url}`;
  }
  return new ApiService(path);
}

export const baseUrl = import.meta.env.v_BASE_URL;
export const RequestOptions = {
  Emergency: "Emergency",
  Regular: "Regular_Report",
};

export const LOSS_ADJEstment_STATUS = {
  REQUESTED_LOSS: "REQUESTED_LOSS",
  LOSS: "LOSS",
  POSITIVE_ADJUSTMENT: "POSITIVE_ADJUSTMENT",
  NEGATIVE_ADJUSTMENT: "NEGATIVE_ADJUSTMENT",
};

export const BatchStatus = {
  PROCESSED: "PROCESSED",
  PURCHASED: "PURCHASED",
  REQUESTED: "REQUESTED",
  ISSUED: "ISSUED",
  APPROVED: "APPROVED",
  RECEIVED: "RECEIVED",
  CONFIRMED: "CONFIRMED",
};

export const Title = ["Mr.", "Mrs.", "Ms.", "Dr.", "Prof."];
export const Regions = [
  "Afar",
  "Addis Ababa(City)",
  "Amhara",
  "Benishangul-Gumuz",
  "Centeral Ethiopia Regional State",
  "Dire Dawa(City)",
  "Gambela",
  "Harari",
  "Oromia",
  "Sidama",
  "Somali",
  "Southern Ethiopia Regional State",
  "South West Ethiopia Peoples",
  "Tigray",
];
let id = 0;
function* getId() {
  while (true) {
    yield `generated_id_${++id}`;
  }
}

export const genId = getId();
export function getQueryFormObject(query) {
  return Object.keys(query).reduce((querys, q, idx) => {
    querys += `${q}=${query[q]}`;
    if (idx != Object.keys(query).length - 1) querys += `&`;
    return querys;
  }, "?");
}

export const toast = {
  success: (message) => {
    addToast({
      type: "success",
      message,
    });
  },
  error: (message) => {
    addToast({
      type: "error",
      message,
    });
  },
};

export function toasted(type, succMsg, errMsg) {
  if (type) {
    toast.success(succMsg);
  } else {
    toast.error(errMsg);
  }
}

export function addFullname(data) {
  if (!(data instanceof Array)) return;
  return data.reduce((el, payload) => {
    payload.fullname = `${payload.firstName} ${payload.fatherName} ${payload.grandFatherName}`;
    el.push(payload);
    return el;
  }, []);
}

export function getImageUrlFormBlob(blob) {
  if (!blob) return;
  return URL.createObjectURL(blob);
}

export function getExtension(filename) {
  if (!filename) return;

  const extension = filename.match(/\.([^.]+)$/)?.[1];
  return extension;
}

export function openNewTab(url) {
  const a = document.createElement("a");
  a.target = "_blank";
  a.href = url;
  a.click();
}

export function getFileType(filename) {
  if (!filename) return;
  const ext = getExtension(filename);
  if (imageExtensions.includes(ext)) return "img";
  if (documentExtensions.includes(ext)) return "file";
  if (videoFileTypes.includes(ext)) return "video";
}

const response = {
  success: false,
  data: "",
  status: null,
  error: "",
};
export async function allRequest(funs) {
  try {
    const keys = Object.keys(funs);

    const res = await Promise.all(keys.map((name) => funs[name]));

    return {
      success: res.every((r) => r.success),
      data: keys.reduce((state, name, idx) => {
        state[name] = res[idx]?.data;
        return state;
      }, {}),
      status: 200,
      error: keys.reduce((state, name, idx) => {
        state[name] = res[idx]?.error;
        return state;
      }, {}),
    };
  } catch (err) {
    return {
      success: false,
      data: null,
      status: err?.response?.status,
      error: err?.message,
    };
  }
}

export function formatCurrency(currencyValue) {
  if ([undefined, null].includes(currencyValue)) return;
  const currencyFormat = new Intl.NumberFormat("am-ET", {
    style: "currency",
    currency: "ETB",
  }).format(parseFloat(currencyValue));
  return currencyFormat;
}
export function getColumnValue(key, row) {
  return key.split(".").reduce((all, el) => {
    return all?.[el];
  }, row);
}

export function secondDateFormat(d) {
  if (!d) return " ";
  try {
    const date = new Date(d);
    const dateFormat = new Intl.DateTimeFormat("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
    }).format(date);
    return dateFormat;
  } catch (err) {
    return "";
  }
}

export function secondDateFormatWithTime(d) {
  if (!d) return " ";
  try {
    const date = new Date(d);
    const dateFormat = new Intl.DateTimeFormat("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
      hour: "numeric",
      minute: "numeric",
    }).format(date);
    return dateFormat;
  } catch (err) {
    return "";
  }
}

export function getAgeFormDate(date) {
  const now = new Date().getFullYear() - 8;
  const age = new Date(date).getFullYear();
  return parseInt(now - age);
}

export async function getBgbase64Url(url) {
  return new Promise(async (res, rej) => {
    try {
      const logoBlob = await (await fetch(url)).blob();
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64Image = reader.result;
        res(base64Image);
      };
      reader.readAsDataURL(logoBlob);
    } catch (error) {
      rej(error);
    }
  });
}

export function removeUndefined(values) {
  return JSON.parse(JSON.stringify(values));
}

export function calculateAge(dateOfBirth) {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  return age;
}

export function addYear(date, year) {
  const givenDate = new Date(date);

  // Add two years
  const modifiedDate = new Date(givenDate);
  modifiedDate.setFullYear(modifiedDate.getFullYear() + year);

  console.log(modifiedDate);
  return modifiedDate;
}

export async function aggregateFunctionResponse(req) {
  const keys = Object.keys(req);
  try {
    const res = await Promise.all(keys.map((key) => req[key]));
    return {
      success: true,
      data: keys.reduce((state, el, idx) => {
        state[el] = res?.[idx]?.data;
        return state;
      }, {}),
    };
  } catch (err) {
    return {
      success: false,
      data: "",
      error: err.message,
    };
  }
}

export function getDateFromAge(age) {
  const today = new Date();
  const birthYear = today?.getFullYear?.() - age - 8;

  // Set to middle of the year (July 1st) as a reasonable default
  // when only age is known
  const birthDate = new Date(birthYear, 6, 1);

  return birthDate;
}

export function getFormData(data) {
  const fd = new FormData();
  Object.keys(data).forEach((key) => {
    fd.append(key, data[key]);
  });
  return fd;
}

export function addDay(date, date2) {
  const givenDate = new Date(date);
  const modifiedDate = new Date(givenDate);
  modifiedDate.setUTCDate(modifiedDate.getDay() - 1 + date2);
  return modifiedDate;
}

export function addDayToDate(date, days) {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

export function formatDateToYYMMDD(date) {
  if (!date) return undefined;

  let d = new Date(date);

  const year = d.getFullYear().toString(); // Extract the last two digits of the year
  const month = (d.getMonth() + 1).toString().padStart(2, "0"); // Month ranges from 0 to 11, so add 1 and pad with leading zero if necessary
  const day = d.getDate().toString().padStart(2, "0"); // Pad day with leading zero if necessary

  return `${year}-${month}-${day}`;
}

export function formatDateToDDMMYY(date) {
  if (!date) return undefined;

  const year = date?.getFullYear?.()?.toString?.(); // Extract the last two digits of the year
  const month = (date?.getMonth?.() + 1)?.toString?.()?.padStart?.(2, "0"); // Month ranges from 0 to 11, so add 1 and pad with leading zero if necessary
  const day = date?.getDate?.()?.toString?.()?.padStart?.(2, "0"); // Pad day with leading zero if necessary

  return `${year}-${month}-${day}`;
}

export function formatNumber(number) {
  const formatter = new Intl.NumberFormat("en-US");

  const formattedNumber = formatter.format(number);
  return formattedNumber;
}

export const imageExtensions = [
  "jpeg",
  "jpg",
  "png",
  "gif",
  "bmp",
  "tiff",
  "tif",
  "webp",
  "svg",
  "ico",
  "jfif",
  "jpe",
  "jif",
  "jfif-tbnl",
  "jfi",
  "jp2",
  "jpx",
  "j2k",
  "j2c",
  "fpx",
  "pcd",
];

export const documentExtensions = [
  "txt",
  "doc",
  "docx",
  "rtf",
  "pdf",
  "odt",
  "xls",
  "xlsx",
  "csv",
  "ppt",
  "pptx",
  "pps",
  "odp",
  "ods",
  "odp",
  "odg",
  "odf",
  "odc",
  "odm",
  "ott",
  "oth",
  "odft",
  "pub",
  "pages",
  "numbers",
  "key",
];

const videoFileTypes = [
  "mp4",
  "webm",
  "ogg",
  "mpeg",
  "3gpp",
  "3gpp2",
  "x-ms",
  "x-flv",
  "x-matroska",
  "x-ms-wmv",
  "quicktime",
  "x-ms-asf",
  "x-ms-dvr",
  "x-ms-dxv",
  "x-ms-wvx",
  "x-la-asf",
  "x-ms-vob",
  "x-ms-wmx",
  "vnd.dlna.mpeg-tts",
  "vnd.dlna.mpeg-tts-protected",
  "vnd.sealed.mpeg1",
  "vnd.sealed.mpeg4",
  "vnd.sealed.swf",
  "vnd.sealedmedia.softseal.mov",
  "x-f4v",
  "x-fli",
  "x-flic",
  "x-m4v",
  "x-mng",
  "x-ms-ivf",
  "x-sgi-movie",
  "x-smv",
  "x-swf",
  "x-vob",
];
// export const routes = [
//   "Oral",
//   "Topical",
//   "Intraosasseous",
//   "Intramuscular",
//   "Nasal",
//   "Intrathecal",
//   "Intraperitoneal",
//   "Intradermal",
//   "Nasogastric",
//   "Sub Lingual",
//   "Per Rectum",
//   "Sub Cutaneous",
//   "Inhalation",
//   "Per Vaginal",
// ];

export const routes = [
  "Oral",
  "Topical",
  "Intravenous",
  "Intramuscular",
  "Subcutaneous",
  "Nasal",
  "Inhalation",
  "Ophthalmic",
  "Otic",
  "Rectal",
  "Vaginal",
  "Sublingual",
  "Buccal",
  "Transdermal",
  "Intrathecal",
  "Intraperitoneal",
  "Intradermal",
  "Epidural",
  "Dental",
];

export const routesAndDosageForms = [
  {
    route: "Oral",
    dosageForm: [
      "Tablet",
      "Capsule",
      "Syrup",
      "Suspension",
      "Solution",
      "Elixir",
      "Emulsion",
      "Powder for Oral Solution",
      "Powder for Oral Suspension",
      "Granules",
      "Oral Gel",
      "Oral Paste",
      "Chewable Tablet",
      "Dispersible Tablet",
      "Effervescent Tablet",
      "Modified Release Tablet",
      "Extended Release Tablet",
      "Delayed Release Tablet",
      "Film Coated Tablet",
      "Sugar Coated Tablet",
      "Enteric Coated Tablet",
      "Hard Capsule",
      "Soft Capsule",
      "Modified Release Capsule",
      "Medicated Gum",
    ],
  },
  {
    route: "Topical",
    dosageForm: [
      "Cream",
      "Ointment",
      "Gel",
      "Lotion",
      "Paste",
      "Powder",
      "Solution",
      "Foam",
      "Spray",
      "Medicated Plaster",
      "Transdermal Patch",
      "Shampoo",
      "Soap",
      "Wound Dressing",
    ],
  },
  {
    route: "Intravenous",
    dosageForm: [
      "Solution for Injection",
      "Suspension",
      "Emulsion for Injection",
      "Powder for Injection",
      "Concentrate for Solution for Injection",
    ],
  },
  {
    route: "Intramuscular",
    dosageForm: [
      "Solution for Injection",
      "Suspension",
      "Powder for Injection",
      "Oil-based Injection",
    ],
  },
  {
    route: "Subcutaneous",
    dosageForm: [
      "Solution for Injection",
      "Suspension",
      "Powder for Injection",
      "Implant",
    ],
  },
  {
    route: "Nasal",
    dosageForm: ["Spray", "Drops", "Gel", "Powder", "Solution"],
  },
  {
    route: "Inhalation",
    dosageForm: [
      "Dry Powder Inhaler",
      "Pressurized Metered Dose Inhaler",
      "Nebulizer Solution",
      "Respiratory Solution",
      "Powder",
      "Solution",
      "Suspension",
    ],
  },
  {
    route: "Ophthalmic",
    dosageForm: [
      "Eye Drops",
      "Eye Ointment",
      "Eye Gel",
      "Ophthalmic Insert",
      "Ophthalmic Solution",
      "Ophthalmic Suspension",
    ],
  },
  {
    route: "Otic",
    dosageForm: [
      "Ear Drops",
      "Ear Ointment",
      "Ear Spray",
      "Ear Solution",
      "Ear Suspension",
    ],
  },
  {
    route: "Rectal",
    dosageForm: ["Suppository", "Cream", "Ointment", "Foam", "Gel", "Solution"],
  },
  {
    route: "Vaginal",
    dosageForm: [
      "Tablet",
      "Capsule",
      "Pessary",
      "Ring",
      "Cream",
      "Gel",
      "Foam",
      "Solution",
    ],
  },
  {
    route: "Sublingual",
    dosageForm: ["Tablet", "Spray", "Film", "Solution"],
  },
  {
    route: "Buccal",
    dosageForm: ["Tablet", "Film", "Patch", "Solution"],
  },
  {
    route: "Transdermal",
    dosageForm: ["Patch", "System", "Gel", "Solution"],
  },
  {
    route: "Intrathecal",
    dosageForm: ["Solution for Injection", "Suspension"],
  },
  {
    route: "Intraperitoneal",
    dosageForm: ["Solution for Injection", "Dialysis Solution"],
  },
  {
    route: "Intradermal",
    dosageForm: ["Solution for Injection", "Suspension"],
  },
  {
    route: "Epidural",
    dosageForm: ["Solution for Injection", "Suspension"],
  },
  {
    route: "Dental",
    dosageForm: ["Gel", "Insert", "Solution", "Paste", "Powder"],
  },
];

export const formatAgeToFraction = (ageValue) => {
  if (ageValue < 1) {
    // Convert fraction to months/12 format
    const months = Math.round(ageValue * 12);
    return `${months}/12`;
  }
  return ageValue ? `${ageValue}`.toString() : ageValue;
};

export function getRoutes(form) {
  let res = routesAndDosageForms.reduce((routes, el) => {
    let input = `${form}`.toLowerCase();
    let input2 = "";
    if (`${form}`.endsWith("s")) {
      input2 = `${form}`.substring(0, `${form}`.length - 1);
    }
    if (
      [
        input2,
        input2.toLocaleLowerCase(),
        form,
        input,
        `${form}s`,
        `${input}s`,
      ].some(
        (dosageForm) =>
          el.dosageForm.includes(dosageForm) ||
          el.dosageForm.find((el) => el.toLowerCase() == input) ||
          el.dosageForm.find((el) => el.substring(input))
      )
    ) {
      routes.push(el.route);
    }
    return routes;
  }, []);

  return [...res, "Procedural", "Miscellaneous", "other"];
}
// export const FREQUENCY = {
//   IMMEDIATELY: { label: "Immediately", values: "1 Hour/s" },
//   ONCE_A_DAY: { label: "Once a day", values: "1 Day/s" },
//   THRICE_A_DAY: { label: "Thrice a day", values: "3 Day/s" },
//   FOUR_TIMES_A_DAY: { label: "four times a day", values: "4 Day/s" },
//   EVERY_2_HOURS: { label: "Every 2 hours", values: "12 Day/s" },
//   EVERY_3_HOURS: { label: "Every 3 hours", values: "8 Day/s" },
//   EVERY_4_HOURS: { label: "Every 4 hours", values: "6 Day/s" },
//   EVERY_6_HOURS: { label: "Every 6 hours", values: "4 Day/s" },
//   EVERY_8_HOURS: { label: "Every 8 hours", values: "3 Day/s" },
//   EVERY_12_HOURS: { label: "Every 12 hours", values: "2 Day/s" },
//   ON_ALTERNATIVE_DAYS: { label: "On alternative days", values: "1 Day/s" },
//   ONCE_A_WEEK: { label: "Once a week", values: "1 Day/s" },
//   TWICE_A_WEEK: { label: "Twice a week", values: "2 Day/s" },
//   THRICE_A_WEEK: { label: "Thrice a week", values: "3 Day/s" },
//   EVERY_2_WEEKS: { label: "Every 2 weeks", values: "0.50 Day/s" },
//   ONCE_A_MONTH: { label: "Once a month", values: "1 Month/s" },
//   FOUR_TIMES_A_DAY: { label: "Four times a day", values: "5 Day/s" },
//   FOUR_DAYS_A_WEEK: { label: "Four days a week", values: "4 Day/s" },
//   FIVE_DAYS_A_WEEK: { label: "Five times a day", values: "5 Day/s" },
// };

export const FREQUENCY = {
  IMMEDIATELY: { label: "Immediately", values: "1 Hour/s" }, // Take once ASAP
  ONCE_A_DAY: { label: "Once a day", values: "1 Day/s" },
  AS_A_NEEDED: { label: "Once a day", values: "1 Day/s" },
  TWICE_A_DAY: { label: "Twice a day", values: "2 Day/s" },
  THRICE_A_DAY: { label: "Thrice a day", values: "3 Day/s" },
  FOUR_TIMES_A_DAY: { label: "Four times a day", values: "4 Day/s" },
  EVERY_2_HOURS: { label: "Every 2 hours", values: "12 Day/s" }, // 24 / 2
  EVERY_3_HOURS: { label: "Every 3 hours", values: "8 Day/s" }, // 24 / 3
  EVERY_4_HOURS: { label: "Every 4 hours", values: "6 Day/s" }, // 24 / 4
  EVERY_5_HOURS: { label: "Every 5 hours", values: "5 Day/s" }, // 24 / 4
  ON_ALTERNATE_DAYS: { label: "On Alternate days", values: "0.5 Day/s" }, // every other day
  ONCE_A_WEEK: { label: "Once a week", values: "0.14 Week/s" },
  TWICE_A_WEEK: { label: "Twice a week", values: "0.28 Week/s" },
  THRICE_A_WEEK: { label: "Thrice a week", values: "0.42 Week/s" },
  FOUR_DAYS_A_WEEK: { label: "Four days a week", values: "0.56 Week/s" },
  FIVE_DAYS_A_WEEK: { label: "Five days a week", values: "0.7 Week/s" },
  EVERY_2_WEEKS: { label: "Every 2 weeks", values: "0.07 Week/s" },
  ONCE_A_MONTH: { label: "Once a month", values: "0.035 Month/s" },
};

export const instructions = [
  "Before Meals",
  "Empty stomach",
  "After meals",
  "In the morning",
  "In the evening",
  "At bedtime",
  "Immediately",
  "As directed",
];

export const Gender = {
  MALE: "Male",
  FEMALE: "Female",
};
export const hospitals = [
  "Tikur Anbessa Hospital",
  "Cadisco Hospital",
  "Ayat Hospital",
  "Lancet Hospital",
  "Biras Hospital",
];
