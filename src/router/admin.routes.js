import Dashboard from "@/features/admin/dashboard/pages/Dashboard.vue";
import Drugs from "@/features/admin/drug/pages/Drugs.vue";
import AddPrivilege from "@/features/admin/privilege/pages/AddPrivilege.mdl.vue";
import Privileges from "@/features/admin/privilege/pages/Privileges.vue";
import Report from "@/features/admin/AdminReport/pages/Report.vue";
import AddRole from "@/features/admin/role/pages/AddRole.mdl.vue";
import EditRole from "@/features/admin/role/pages/EditRole.mdl.vue";
import Role from "@/features/admin/role/pages/Role.vue";
import Users from "@/features/admin/user/pages/Users.vue";
import { compile } from "vue";

export default [
 
  // {
  //   path: "/drugs",
  //   name: "drug",
  //   component: Drugs,
  //   meta: {
  //     requiresAuth: true,
  //     // role: "admin",
  //     privileges: ["view_drugs"],
  //   },
  // },
  {
    path: "/drugs",
    name: "Admin drug",
    component: Drugs,
    meta: {
      requiresAuth: true,
      // role: "admin",
      privileges: ["view_drugs"],
    },
  },
  {
    path: "/report",
    name: "Admin Report",
    component: Report,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
  
];
