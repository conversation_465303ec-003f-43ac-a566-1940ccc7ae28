import ApprovalHistory from "@/features/caseTeamLeader/pages/ApprovalHistory.vue";
import Dashboard from "@/features/caseTeamLeader/pages/Dashboard.vue";
import LossAdjestment from "@/features/caseTeamLeader/pages/LossAdjestment.vue";

export default [
	{
		path: '/case-team-leader',
		component: Dashboard,
		name: 'CaseTeamLeader',
		meta: {
			requiresAuth: true,
			privileges: ['view_issued']
		}
	},
	{
		path: '/issued-batch',
		component: ApprovalHistory,
		name: 'ApprovalHistory',
		meta: {
			requiresAuth: true,
			privileges: ['view_issued']
		}
	},
	{
		path: '/loss-adjestment',
		component: LossAdjestment,
		name: 'Loss Adjestment',
		meta: {
			requiresAuth: true,
			privileges: ['view_issued']
		}
	}
]