import Dispensary<PERSON>oteraCrossCheck from "@/features/paper_prescription/pages/DispensaryKoteraCrossCheck.vue";
import DispensaryKoteraHistory from "@/features/paper_prescription/pages/DispensaryKoteraHistory.vue";
import Expired from "@/features/stock/pages/Expired.vue";
import StoreKoteraHistory from "@/features/StoreKeeper/components/StoreKoteraHistory.vue";
import Kotera from "@/features/StoreKeeper/Kotera.vue";
import KoteraCrosscheck from "@/features/StoreKeeper/KoteraCrosscheck.vue";
import AddItem from "@/features/StoreKeeper/pages/AddItem.vue";
import ApprovedHistory from "@/features/StoreKeeper/pages/ApprovedHistory.vue";
import DispensaryKoteraPdf from "@/features/StoreKeeper/pages/DispensaryKoteraPdf.vue";
import ReceiveFromCenteral from "@/features/StoreKeeper/pages/ReceiveFromCenteral.vue";
import Request from "@/features/StoreKeeper/pages/Request.vue";
import StockPurchases from "@/features/StoreKeeper/pages/StockPurchases.vue";
import Store from "@/features/StoreKeeper/pages/Store.vue";
import StoreIFRR from "@/features/StoreKeeper/pages/StoreIFRR.vue";
import StoreKotreraPdf from "@/features/StoreKeeper/pages/StoreKotreraPdf.vue";

export default [
  {
    path: "/store",
    name: "store",
    component: Store,
    meta: {
      requiresAuth: true,
      //  role: "storekeeper",
      privileges: ["view_store"],
    },
  },
  {
    path: "/store-ifrr",
    name: "Store IFRR",
    component: StoreIFRR,
    meta: {
      requiresAuth: true,
      //  role: "storekeeper",
      privileges: ["view_store"],
    },
  },
  {
    path: "/additem",
    name: "add item",
    component: AddItem,
    meta: {
      requiresAuth: true,
      //  role: "storekeeper",
      privileges: ["view_store"],
    },
  },
  {
    path: "/request",
    name: "request",
    component: Request,
    meta: {
      requiresAuth: true,
      // role: "storekeeper",
      privileges: ["view_request"],
    },
  },
  {
    path: "/stockpurchases",
    name: "Stock Purchases",
    component: StockPurchases,
    meta: {
      requiresAuth: true,
      // role: "storekeeper",
      privileges: ["view_stockpurchases"],
    },
  },
  {
    path: "/stockpurchasecenteral",
    name: "Stock Recieve From Centeral",
    component: ReceiveFromCenteral,
    meta: {
      requiresAuth: true,
      // role: "storekeeper",
      privileges: ["view_stockpurchases"],
    },
  },
  {
    path: "/approvedhistory",
    component: ApprovedHistory,
    meta: {
      requiresAuth: true,
      // role: "storekeeper",
      privileges: ["view_store"],
    },
  },
  {
    path: "/store/expireddrugs",
    name: "Expired Drugs",
    component: Expired,
    meta: {
      requiresAuth: true,
      // role: "storekeeper",
      privileges: ["view_store"],
    },
  },
  {
    path: "/kotera",
    name: "Kotera",
    component: Kotera,
    meta: {
      requiresAuth: true,
      privileges: ["view_kotera"],
    },
  },
  {
    path: "/dispensarykoteracrosscheck",
    name: "Dispensary-KoteraCrosscheck",
    component: KoteraCrosscheck,
    meta: {
      requiresAuth: true,
      privileges: ["view_dispensarykoteracrosscheck"],
    },
  },
  {
    path: "/koteracrosscheck",
    name: "Kotera Crosscheck",
    component: DispensaryKoteraCrossCheck,
    meta: {
      requiresAuth: true,
      privileges: ["view_kotera"],
    },
  },
  {
    path: "/dispensary-kotera-history",
    name: "Dispensary Kotera History",
    component: DispensaryKoteraHistory,
    meta: {
      requiresAuth: true,
      privileges: ["view_dispensarykoteracrosscheck"],
    },
  },
  {
    path: "/store-kotera-history",
    name: "Store Kotera History",
    component: StoreKoteraHistory,
    meta: {
      requiresAuth: true,
      privileges: ["view_store_kotera_history"],
    },
  },
  {
    path: "/store-kotera-history/:batchUuid",
    name: "Store History",
    component: StoreKotreraPdf,
    meta: {
      requiresAuth: true,
      privileges: ["view_store_kotera_history"],
    },
  },
  {
    path: "/dispensary-kotera-history/:batchUuid",
    component: DispensaryKoteraPdf,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_model20"],
    },
  },
];
