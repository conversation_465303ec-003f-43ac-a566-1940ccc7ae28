import Model19Pdf from "@/components/pdf/Model19.pdf.vue";
import EmergencyModel20 from "@/features/stock/pages/EmergencyModel20.vue";
import EmergencyModel22 from "@/features/stock/pages/EmergencyModel22.vue";
import Expired from "@/features/stock/pages/Expired.vue";
import History from "@/features/stock/pages/History.vue";
import Model19 from "@/features/stock/pages/Model19.vue";
import Model20 from "@/features/stock/pages/Model20.vue";
import Model22 from "@/features/stock/pages/Model22.vue";
import Stock from "@/features/stock/pages/Stock.vue";

export default [
  {
    path: "/stock",
    component: Stock,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_stock"],
    },
  },
  {
    path: "/history",
    component: History,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_stock"],
    },
  },
  {
    path: "/model20/:batchUuid",
    component: Model20,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_model20"],
    },
  },
  {
    path: "/emergency-model20/:batchUuid",
    component: EmergencyModel20,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_model20"],
    },
  },
  {
    path: "/model19/:batchUuid",
    component: Model19,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_model19"],
    },
  },
  {
    path: "/model22/:batchUuid",
    component: Model22,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_model22"],
    },
  },
  {
    path: "/emergency-model22/:batchUuid",
    component: EmergencyModel22,
    meta: {
      requiresAuth: true,
      privileges: ["view_model22"],
    },
  },
  {
    path: "/stock/expireddrugs",
    component: Expired,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_stock"],
    },
  },
];
