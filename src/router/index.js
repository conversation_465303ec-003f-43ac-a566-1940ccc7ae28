import { createRouter, createWebHistory } from "vue-router";
import MainLayout from "@/layouts/MainLayout.vue";
import paper_prescriptionRouter from "./paper_prescription.routes";
import refillRouter from "./refill.routes";
import Login from "@/views/Login.vue";
import { useAuth } from "@/stores/auth";
import stockRouter from "./stock.routes";
import adminRouter from "./admin.routes";
import storeRouter from "./store.routes";
import Forbidden from "@/views/Forbidden.vue";
import ForgetPassword from "@/views/ForgetPassword.vue";
import doctorRouter from "./doctorprescription.routes";
import supplierRouter from "./suppliers.routes";
import manRouter from "./manufacturers.routes";
import registerarRouter from "./registerar.routes";
import Profile from "@/features/profile/pages/Profile.mdl.vue";
import LandingPage from "@/views/LandingPage.vue";
import Model19Pdf from "@/components/pdf/Model20.pdf.vue";
import caseTeamLeaderRoutes from "./caseTeamLeader.routes";
import DispensaryStoreKotera from "@/components/pdf/DispensaryStoreKotera.pdf.vue";
import StoreKoteraPdf from "@/components/pdf/StoreKotera.pdf.vue";
import AdminLayout from "@/layouts/AdminLayout.vue";
import new_adminRoutes from "./new_admin.routes";
const routes = [
  {
    path: "/pdf",
    name: "Pdf",
    component: StoreKoteraPdf,
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: "/",
    name: "LandingPage",
    component: LandingPage,
  },
  {
    path: "/forbidden",
    name: "forbidden",
    component: Forbidden,
  },
  {
    path: "/forgetpassword",
    name: "forgetpassword",
    component: ForgetPassword,
  },
  {
    path: "/resetpassword",
    name: "resetpassword",
    component: () => import("@/views/ResetPassword.vue"),
  },
  {
    path: "/setnewpassword",
    name: "setnewpassword",
    component: () => import("@/views/SetPassword.vue"),
  },
  {
    path: "/changepassword/:userUuid",
    name: "changepassword",
    component: () => import("@/views/changePassword.vue"),
  },
  // {
  //   path: "/profile",
  //   name: "profile",
  //   component: Profile,
  // },
  {
    path: '',
    name: "admin",
    meta: {
      requiresAuth: true,
    },
    component: AdminLayout,
    children: [
      ...new_adminRoutes
    ]
  },
  {
    path: "/main",
    name: "home",
    component: MainLayout,
    meta: {
      requiresAuth: true,
    },
    children: [
      // { path: "", name: "Redirect", redirect: "/paper_prescription" },
      ...paper_prescriptionRouter,
      ...refillRouter,
      ...stockRouter,
      ...storeRouter,
      ...doctorRouter,
      ...registerarRouter,
      ...manRouter,
      ...supplierRouter,
      ...caseTeamLeaderRoutes,
    ],
  },
  // {
  //   path: "",
  //   redirect: () => (localStorage.getItem("userDetail") ? "/main" : "/login"),
  // },
  {
    path: "/login",
    name: "Login",
    component: Login,
  },
  { path: "/:path(.*)*", redirect: "/forbidden" },
];

const history = createWebHistory();
const router = createRouter({
  history,
  routes,
});

// The to parameter contains the target route being navigated to,
// from is the current route, and
// next is a function that must be called to proceed with the navigation.

router.beforeEach((to, from, next) => {
  const routes = to.matched.reduce((routes, route) => {
    if (routes.find((el) => el.name == route.name)) return routes; //This checks if a route with the same name already exists in the routes array. If it does, the current iteration is skipped.
    const routesSplit = route.path.split("/");
    const path = routesSplit.reduce((state, el, idx) => {
      const res = el.startsWith(":");
      if (res) {
        const name = el.match(/:([a-zA-Z]+)/)?.[1];
        state.push(to.params[name]);
      } else {
        state.push(el);
      }
      return state;
    }, []);
    routes.push({
      name: route.name,
      path: path.join("/paper_prescription"),
    });
    return routes;
  }, []);

  next();
});

// window.addEventListener("storage", (event) => {
//   if (event.key === "userDetail") {
//     const auth = useAuth();
//     const storedUserDetail = localStorage.getItem("userDetail");
//     if (storedUserDetail) {
//       auth.setAuth(JSON.parse(storedUserDetail));
//     } else {
//       auth.setAuth(null); // If userDetail is removed from localStorage, clear auth state
//     }
//   }
// });

function reRoute(detail) {
  let path = "/";
  console.log(detail.roleName);

  if (detail.roleName == "cashier") {
    path = "/cashier";
  } else if (detail.roleName == "case team leader") {
    path = "/issued-batch";
  } else if (detail.roleName == "pharmacist") {
    path = "/paper_prescription";
  } else if (detail.roleName == "payer") {
    path = "/institution";
  } else if (detail.roleName == "admin") {
    path = "/dashboard";
  } else if (detail.roleName == "storekeeper") {
    path = "/store";
  } else if (detail.roleName == "doctor") {
    path = "/doctor";
  } else if (detail.roleName == "superadmin") {
    path = "/institutions";
  } else if (detail.roleName == "registrar") {
    path = "/registrar";
  } else if (role == "emr") {
    router.push("/paper_prescription");
  }

  return path;
}

router.beforeEach(async (to, from) => {
  let detail = localStorage.getItem("userDetail");

  const auth = useAuth();

  if (detail) {
    detail = JSON.parse(detail);
    auth.setAuth({
      user: { ...detail, roleName: detail.roleName?.toLowerCase() },
      accessToken: detail?.token,
    });
  }

  // return true

  if ((to.path == "/login" || to.path == "/") && detail?.token) {
    const path = reRoute(detail);
    return {
      path: path != "/" ? path : from.path,
      replace: true,
    };
  }

  let privileges = auth.auth?.user?.privileges;

  const found = (to.meta?.privileges || []).find((privilage) => {
    return privileges?.includes(`ROLE_${privilage}`);
  });

  if (
    !to.meta?.requiresAuth ||
    auth.auth?.user?.privileges?.includes("All Privileges") ||
    auth.auth?.user?.roleName === "Super Admin"
  ) {
    return true;
  }

  if (!detail) {
    return {
      path: `/login`,
      query: {
        redirect: to.path,
      },
    };
  }

  if (
    (to.meta?.role && detail.roleName == to.meta?.role?.toLowerCase()) ||
    (to.meta?.role && !to.meta?.role?.toLowerCase() && to.meta?.requiresAuth)
  ) {
    if (to.path != "/") return;

    let path = reRoute(detail);
    return {
      path,
    };
  }

  if (found) return true;

  return {
    path: "/forbidden",
  };
});

export default router;
