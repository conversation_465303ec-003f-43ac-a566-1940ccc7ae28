import PaperPrescriptions from "@/features/paper_prescription/pages/PaperPrescriptions.vue";
import Claims from "@/features/claims/pages/Claims.vue";
import PrescriptionDetail from "@paper_prescription/pages/PrescriptionDetail.vue";
import PaperPrescriptionsIndex from "@paper_prescription/pages/Index.vue";
import CashierIndex from "@cashier/pages/Index.vue";
import DispenseIndex from "../features/dispense/pages/Index.vue";
import Cashier from "@cashier/pages/Cashier.vue";
import PaidOrder from "../features/dispense/pages/PaidOrder.vue";
import Payment from "@cashier/pages/Payment.vue";
import Dispense from "../features/dispense/pages/dispense.vue";
import ReturnedOrders from "../features/ReturnedOrders/pages/ReturnedOrders.vue";
import E_Prescription from "../features/ePrescription/pages/E_Prescription.vue";
import Institutions from "../features/institutions/pages/Institutions.vue";
import InstitutionsIndex from "../features/institutions/pages/InstitutionsIndex.vue";
import Members from "../features/institutions/pages/members/components/Members.vue";
import Institution from "@/features/institution/pages/Institution.vue";
import Claim from "@/features/institution/pages/Claim.vue";
import Stock from "@/features/stock/pages/Stock.vue";
import Pdf from "@/features/cashier/components/Pdf.vue";
import PaperPriscriptionPdf from "@/features/paper_prescription/pages/PaperPriscriptionPdf.vue";
import CashierPdf from "@/features/cashier/pages/CashierPdf.vue";
import Compounding from "@/features/doctorCompounding/pages/compounding.vue";
import CompundingIndex from "@/features/compounding/pages/compundingIndex.vue";
import CompoundingDetail from "@/features/doctorCompounding/pages/CompoundingDetail.vue";
import CompoundingPrescriptionDetail from "@/features/doctorCompounding/pages/compoundingPrescriptionDetail.vue";
import CashierCompoundingIndex from "@/features/compoundingCashier/pages/CashierCompoundingIndex.vue";
import CashierCompounding from "@/features/compoundingCashier/pages/CashierCompounding.vue";
import CashierPayment from "@/features/compoundingCashier/pages/CashierPayment.vue";
import CompoundingPdf from "@/features/compoundingCashier/components/CompoundingPdf.vue";
import CashierCompoundingPdf from "@/features/compoundingCashier/pages/CashierCompoundingPdf.vue";
import CompoundingPaidOrder from "@/features/dispense/pages/compoundingPaidOrder.vue";
import Compoundingdispense from "@/features/dispense/pages/compoundingdispense.vue";
import CompoundingReturnedOrders from "@/features/ReturnedOrders/pages/CompoundingReturnedOrders .vue";
import CompoundingStatus from "@/features/paper_prescription/pages/CompoundingStatus.vue";
import ReturnedOrdersToDoctor from "@/features/ReturnedOrders/pages/ReturnedOrdersToDoctor.vue";
import CashierReport from "@/features/cashier/pages/CashierReport.vue";
export default [
  {
    path: "/paper_prescription",
    component: PaperPrescriptionsIndex,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_prescription"],
    },
    children: [
      {
        path: "",
        name: "Paper Prescriptions",
        component: PaperPrescriptions,
      },
      {
        path: "pdf/:prescriptionId?",
        name: "Paper Prescription Pdf",
        component: PaperPriscriptionPdf,
      },
      {
        path: "detail",
        name: "Prescription Detail",
        component: PrescriptionDetail,
      },
    ],
  },

  {
      path: "/compounding",
      component: CompundingIndex,
      meta: {
        requiresAuth: true,
        // role: "doctor",
        privileges: ["view_compounding"],
      },
      children: [
        {
          path: "",
          name: Compounding,
          component: Compounding,
        },
        {
          path: "detail",
          name: "Compounding Detail",
          component: CompoundingDetail,
        },
        {
          path: "compoundingdetail",
          name: "Compounding PrescriptionDetail",
          component: CompoundingPrescriptionDetail,
        },
      ],
    },
  // {
  //   path: "doctor",
  //   component: PaperPrescriptionsIndex,
  //   meta: {
  //     requiresAuth: true,
  //     role: "doctor",
  //   },
  //   children: [
  //     {
  //       path: "",
  //       name: "PaperPrescriptions",
  //       component: PaperPrescriptions,
  //     },
  //     {
  //       path: "detail",
  //       name: "PrescriptionDetail",
  //       component: PrescriptionDetail,
  //     },
  //   ],
  // },
  {
    path: "/cashier",
    component: CashierIndex,
    meta: {
      requiresAuth: true,
      // role: "Cashier",
      privileges: ["view_cashier", "view_compoundingcashier"],
    },
    children: [
      {
        path: "",
        name: "Cashier",
        component: Cashier,
      },
      {
        path: "payment/:uuid",
        name: "Payment",
        component: Payment,
      },
      {
        path: "payment",
        name: "pdf",
        component: Pdf,
      },
      {
        path: "pdf/:uuid",
        name: "cashier Pdf",
        component: CashierPdf,
      },
    ],
  },
  {
    path: "/cashierreport",
    name: "Cashier Report",
    component: CashierReport,
    meta: {
      requiresAuth: true,
      privileges: ["view_cashierreport"],
    },
  },
  {
    path: "/cashiercompounding",
    component: CashierCompoundingIndex,
    meta: {
      requiresAuth: true,
      // role: "Cashier",
      privileges: ["view_cashier", "view_compoundingcashier"],
    },
    children: [
      {
        path: "",
        name: "CashierCompoundig",
        component: CashierCompounding,
      },
      {
        path: "payment/:uuid",
        name: "CashierPayment",
        component: CashierPayment,
      },
      {
        path: "payment",
        name: "Compoundingpdf",
        component: CompoundingPdf,
      },
      {
        path: "pdf/:uuid",
        name: "Compoundingcashier Pdf",
        component: CashierCompoundingPdf,
      },
    ],
  },
  {
    path: "/paidOrder",
    component: DispenseIndex,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_paidorders"],
    },
    children: [
      {
        path: "",
        name: "PaidOrder",
        component: PaidOrder,
      },
      {
        path: "compoundingpaidOrder",
        name: "compounding_PaidOrder",
        component: CompoundingPaidOrder,
      },
      {
        path: "dispense/:Uuid",
        name: "Dispense",
        component: Dispense,
      },
      {
        path: "compoundingdispense/:Uuid",
        name: "Compounding_Dispense",
        component: Compoundingdispense,
        privileges: ["view_paidorders"],
      },
    ],
  },
  {
    path: "/ePrescription",
    name: "ePrescription",
    component: E_Prescription,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_eprescription"],
    },
  },
  {
    path: "/ReturnedOrders",
    name: "Returned Orders",
    component: ReturnedOrders,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_returnedorders"],
    },
  },
  {
    path: "/compoundingReturnedOrders",
    name: "Compounding_Returned Orders",
    component: CompoundingReturnedOrders,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_returnedorders", ""],
    },
  },
  {
    path: "/returnedorderstodoctor",
    name: "Returned",
    component: ReturnedOrdersToDoctor,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_returnedorderstodoctor"],
    },
  },
  {
    path: "/compoundingstatus",
    name: "status",
    component: CompoundingStatus,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_prescription", ""],
    },
  },
  {
    path: "/institution",
    name: "Institution",
    component: Institution,
    meta: {
      requiresAuth: true,
      // role: "Payer",
      privileges: ["view_institution"],
    },
  },
  {
    path: "/claim",
    name: "Claim",
    component: Claim,
    meta: {
      requiresAuth: true,
      // role: "Payer",
      privileges: ["view_claim"],
    },
  },
  {
    path: "/institutions",
    component: InstitutionsIndex,
    meta: {
      requiresAuth: true,
      // role: "Pharmacist",
      privileges: ["view_institutions"],
    },
    children: [
      {
        path: "",
        name: "Institutions",
        component: Institutions,
      },
      {
        path: "members/:Uuid",
        name: "Members",
        component: Members,
      },
      {
        path: "claims/:institutionUuid",
        name: "Claims",
        component: Claims,
      },
    ],
  },
  // {
  //   path: "/stock",
  //   component: Stock,
  //   meta: {
  //     requiresAuth: true,
  //     role: "storekepeer",
  //   },
  //   children: [
  //     {
  //       path: "",
  //       name: "",
  //       component: "",
  //     },
  //   ],
  // },
];
