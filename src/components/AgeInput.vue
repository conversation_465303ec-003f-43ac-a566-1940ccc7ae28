<script setup>
import InputParent from "@/components/new_form_builder/InputParent.vue";
import NewInputLayout from "@/components/new_form_elements/NewInputLayout.vue";
import {
	formatAgeToFraction,
  formatDateToDDMMYY,
  getAgeFormDate,
  getDateFromAge,
} from "@/utils/utils";
import { ref, watch, computed } from "vue";

const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  value: {
    type: String,
  },
  validation: {
    type: [String, Object],
  },
});

const emit = defineEmits(["update:modelValue"]);
const value = ref(props.value || "");
const age = ref("");


const parseAge = (ageInput) => {
  const match = ageInput.match(/^(\s*\d+\s*)\/(\s*12\s*)$/);
  if (match) {
    const months = parseInt(match?.[1]);
    if (months >= 0 && months < 12) {
      return (months / 12).toFixed(2); //
    }
  }
  return ageInput;
};

if (value.value) {
  age.value = formatAgeToFraction(value.value);
}

watch(age, () => {
  const ageValue = parseAge(age.value);
	console.log(ageValue);
  value.value = ageValue;
  emit("update:modelValue", ageValue);
});

watch(
  () => props.value,
  (newVal) => {
    if (newVal && newVal !== value.value) {
      value.value = newVal;
      age.value = parseAge(ageValue);
    }
  }
);
</script>

<template>
  <InputParent
    :validation="validation"
    v-model="value"
    v-slot="{ setRef, error }"
    :name="name"
  >
    <NewInputLayout
      :validation="validation"
      :error="error"
      :label="$attrs?.label"
    >
      <div :ref="setRef" class="!pr-0 flex w-full">
        <input
          placeholder="Age"
          class="w-full h-full bg-transparent"
          v-model="age"
        />
      </div>
    </NewInputLayout>
  </InputParent>
</template>
