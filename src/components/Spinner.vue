<script setup lang="ts">
import { mdiDotsCircle, mdiLoading } from '@mdi/js';
import BaseIcon from './base/BaseIcon.vue';

</script>

<template>
    <div class="loading-overlay">
        <BaseIcon class="flex justify-center animate-spin" size="40" :path="mdiDotsCircle" />
    </div>
</template>

<style>
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(141, 141, 141, 0.8);

    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}
</style>