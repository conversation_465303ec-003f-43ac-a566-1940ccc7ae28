<script setup>
import { inject, watch } from "vue";
import Button from "./Button.vue";
import BaseIcon from "./base/BaseIcon.vue";

defineProps({
  btnText: {
    type: String,
  },
  pending: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String,
  },
});
const icon = "M12 2a10 10 0 0 0 0 20 10 10 0 0 0 0-20zm0 18a8 8 0 1 1 0-16 8 8 0 0 1 0 16z";
const pendingRequest = inject("pending", false);
</script>
<template>
  <Button :size="size" class="w-full h-8 text-sm bg-primary text-white rounded">
    <span v-if="!pending && !pendingRequest">{{ btnText }}</span>
    <BaseIcon v-else name="fa-spinner" :path="icon" :size="20" />
    <!-- <h-icon v-else name="fa-spinner" class="animate-spin" /> -->
  </Button>
</template>
