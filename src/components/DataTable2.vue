<script setup lang="ts">
import { inject, watch } from "vue";

const props = defineProps({
	headers: {
		type: Array,
		required: true,
	},
	showFooter: {
		type: Boolean,
		default: true,
	},
	firstCol: {
		type: Boolean,
		default: false,
	},
	lastCol: {
		type: Boolean,
		default: false,
	},
});

const next = inject("next", () => { });
const previous = inject("previous", () => { });

const page = inject("page", 1);
const totalPages = inject("totalPages", 1);
</script>
<template>
	<div class="rounded-lg">
		<table class="min-w-full border-separate border-spacing-y-3">
			<thead class="capitalize text-black">
				<tr class="bg-[#F1F1F1]">
					<th v-if="firstCol" class="p-4 text-left uppercase tracking-wider rounded-l-lg">
						<slot name="headerFirst"></slot>
					</th>

					<th class="p-4 text-left uppercase tracking-wider">#</th>
					<!-- Add row number column -->
					<th v-for="header in headers" :key="header" class="p-4 text-left">
						{{ header }}
					</th>
					<th v-if="lastCol" class="p-4 text-left uppercase tracking-wider rounded-r-lg">
						<slot name="headerLast"></slot>
					</th>
				</tr>
			</thead>
			<tbody class="bg-[#F1F1F1]">
				<slot />
			</tbody>
		</table>
	</div>
</template>

<style scoped>
.border-spacing-y-3 {
	border-spacing: 0 0.45rem;
}
</style>
