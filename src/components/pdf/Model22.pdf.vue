<script setup lang="ts">
import { formatCurrency, getBgbase64Url } from "@/utils/utils";
import PdfMaker from "../PdfMaker.vue";
import { ref } from "vue";
const props = defineProps({
  batch: {
    type: Array,
    required: true,
  },
});

console.log(props.batch)

const content = ref();
async function gnPdf() {
  const star = await getBgbase64Url("/start.jpg");
  const con = {
    content: [
      {
        columns: [
          {
            width: "auto",
            stack: [
              {
                style: { fontSize: 8 },
                text: "ሞዴል",
              },
              {
                width: "auto",
                style: { fontSize: 8 },
                text: "Model 22/ጤና",
              },
            ],
          },
          {
            width: "*",
            alignment: "center",
            stack: [
              {
                image: star,
                fit: [30, 30],
              },
              {
                marginTop: 10,
                text: "በኢትዮጵያ ፌደራላዊ ዲሞክራሲያዊ ሪፐብሊክ የገንዘብና የኢኮኖሚ ልማት ሚኒስቴር",
              },
              {
                marginTop: 10,
                style: { fontSize: 8 },
                text: "The Federal Democratic Republic of Ethiopia, Ministry of Finance and Economic Development",
              },
              {
                marginTop: 10,
                style: { fontSize: 9 },
                text: "የመድሀኒቶች፣ ሪኤጀንቶች፣ የህክምና መገልገያዎች እና መሳሪያዎች ወጭ ደረሰኝ",
              },
              {
                marginTop: 10,
                style: { fontSize: 8 },
                text: "Issuing Voucher for Drugs, Reagents, Medical Supplies and Equipment",
              },
            ],
          },
          {
            width: "auto",
            stack: [
              {
                style: { fontSize: 8 },
                text: "ሴሪ ቁጥር",
              },
              {
                style: { fontSize: 8 },
                text: "Seri No",
              },
            ],
          },
        ],
      },
      {
        marginTop: 10,
        style: { fontSize: 8 },
        columnGap: 5,
        columns: [
          {
            width: "auto",
            text: "እኔ",
          },
          {
            width: "auto",
            style: { fontSize: 8 },
            stack: [
              {
                text: " ",
              },
              {
                marginTop: -9,
                text: "...........................................................................",
              },
            ],
          },
          {
            style: { fontSize: 8 },
            width: "auto",
            text: " በቀን",
          },
          {
            width: "auto",
            columnGap: 0,
            columns: [
              {
                style: { fontSize: 8 },
                stack: [
                  {
                    text: " ",
                  },
                  {
                    style: { fontSize: 8 },
                    marginTop: -9,
                    text: "........./",
                  },
                ],
              },
              {
                width: "auto",
                style: { fontSize: 8 },
                stack: [
                  {
                    text: " ",
                  },
                  {
                    style: { fontSize: 8 },
                    marginTop: -9,
                    text: "........./20",
                  },
                ],
              },
              {
                width: "auto",
                style: { fontSize: 8 },
                stack: [
                  {
                    text: " ",
                  },
                  {
                    style: { fontSize: 8 },
                    marginTop: -9,
                    text: ".........ዓ.ም",
                  },
                ],
              },
            ],
          },
          {
            width: "auto",
            style: { fontSize: 8 },
            text: " በመጠየቂያ በተፈቀደው መሰረት ለ",
          },
          {
            width: "auto",
            columnGap: 0,
            columns: [
              {
                style: { fontSize: 8 },
                stack: [
                  {
                    text: " ",
                  },
                  {
                    style: { fontSize: 8 },
                    marginTop: -9,
                    text: "............................................",
                  },
                ],
              }
            ],
          },
        ],
      },
      {
        style: { fontSize: 8 },
        marginTop: 6,
        columns: [
          {
            width: "auto",
            stack: [
              {
                text: " ",
              },
              {
                marginTop: -9,
                text: "………………….......አገልግሎት ከታች የተዘረዘረውን በትክክል ቆጥሬ መረከቤን አረጋግጣለሁ፡፡",
              },
            ],
          },
        ],
      },
      {
        marginTop: 10,
        style: { fontSize: 8 },
        columnGap: 5,
        columns: [
          {
            width: "auto",
            text: "As per requisition dated",
          },
          {
            width: "auto",
            columnGap: 0,
            columns: [
              {
                style: { fontSize: 8 },
                stack: [
                  {
                    text: " ",
                  },
                  {
                    style: { fontSize: 8 },
                    marginTop: -9,
                    text: "........./",
                  },
                ],
              },
              {
                width: "auto",
                style: { fontSize: 8 },
                stack: [
                  {
                    text: " ",
                  },
                  {
                    style: { fontSize: 8 },
                    marginTop: -9,
                    text: "........./20",
                  },
                ],
              },
              {
                width: "auto",
                style: { fontSize: 8 },
                stack: [
                  {
                    text: " ",
                  },
                  {
                    style: { fontSize: 8 },
                    marginTop: -9,
                    text: ".........",
                  },
                ],
              },
            ],
          },
          {
            width: "auto",
            style: { fontSize: 8 },
            text: "hereby certify that I",
          },
          {
            width: "auto",
            style: { fontSize: 8 },
            stack: [
              {
                text: " ",
              },
              {
                style: { fontSize: 8 },
                marginTop: -9,
                text: "....................................................................",
              },
            ],
          },
          {
            width: "auto",
            style: { fontSize: 8 },
            stack: [
              {
                text: " ",
              },
              {
                style: { fontSize: 8 },
                marginTop: -9,
                text: "have counted correctly and",
              },
            ],
          },
        ],
      },
      {
        style: { fontSize: 8 },
        marginTop: 6,
        columns: [
          {
            width: "auto",
            stack: [
              {
                text: " ",
              },
              {
                marginTop: -9,
                text: "received items enumerated below for  .................…………………",
              },
            ],
          },
        ],
      },
      {
        marginTop: 10,
				headerRows: 2,
        style: { fontSize: 6 },
        table: {
          widths: [
            "auto",
            "auto",
            "*",
            "auto",
            "auto",
            "auto",
            "auto",
            "auto",
            "auto",
            "auto",
            "auto",
            "auto",
          ],
          body: [
            [
              { text: "ተራ ቁጥር", style: { fontSize: 8 }, rowSpan: 2 },
              { text: "የመድኃኒት ኮድ", style: { fontSize: 8 }, rowSpan: 2 },
              {
								rowSpan: 2,
                style: { fontSize: 8 },
                stack: [
                  {
                    text: "የመድሀኒቱ፣ የቅመሙ፣ የህክምና መገልገያው እና መሳሪያው ",
                  },
                  {
                    marginTop: 10,
                    text: "መግለጫ፡-",
                  },
                ],
              },
              { text: "መለኪያ", style: { fontSize: 8 }, rowSpan: 2 },
              { text: "የወጣው ብዛት", style: { fontSize: 8 }, rowSpan: 2 },
              {
                colSpan: 2,
                text: "የአንዱ የመግዣ ዋጋ",
                style: { fontSize: 8 },
              },
              {},
              { colSpan: 2, text: "ጠቅላላ የመግዢያ ዋጋ", style: { fontSize: 8 } },
              {},
              { colSpan: 2, text: "የአንዱ የመሸጫ ዋጋ", style: { fontSize: 8 } },
              {},
              { text: "የአንድ  ቅንጣት ዋጋ", style: { fontSize: 8 }, rowSpan: 2 },
            ],
            [
              {},
              {},
              {},
              {},
              {},
              { text: "ብር", style: { fontSize: 5 } },
              { text: "ሳ", style: { fontSize: 5 } },
							{ text: "ብር", style: { fontSize: 5 } },
              { text: "ሳ", style: { fontSize: 5 } },
              { text: "ብር", style: { fontSize: 5 } },
              { text: "ሳ", style: { fontSize: 5 } },
              {},
            ],
						...(props.batch || [])
          ],
        },
      },
      {
        marginTop: 20,
				columnGap: 5,
        style: { fontSize: 8 },
        columns: [
          {
            width: "auto",
            stack: [
              {
                text: "የአስረካቢው ስም:",
              },
              {
                columnGap: 5,
                columns: [
                  {
                    text: "Delivered by Name",
                  },
                  {
                    stack: [
                      {
                        text: " ",
                      },
                      {
												marginTop: -9,
                        text: "____________________",
                      },
                    ],
                  },
                ],
              },
            ],
          },
					{
            width: "auto",
            stack: [
              {
                text: "ፊርማ:",
              },
              {
                columnGap: 5,
                columns: [
                  {
										width: 'auto',
                    text: "Signature",
                  },
                  {
										width: 'auto',
                    stack: [
                      {
                        text: " ",
                      },
                      {
												marginTop: -9,
                        text: "_______________",
                      },
                    ],
                  },
                ],
              },
            ],
          },
					{
            width: "auto",
            stack: [
              {
                text: "የተረካበው ስም:",
              },
              {
                columnGap: 5,
                columns: [
                  {
										width: 'auto',
                    text: "Received by:  Name",
                  },
                  {
										width: 'auto',
                    stack: [
                      {
                        text: " ",
                      },
                      {
												marginTop: -9,
                        text: "____________________",
                      },
                    ],
                  },
                ],
              },
            ],
          },
					{
            width: "auto",
            stack: [
              {
                text: "ፊርማ",
              },
              {
                columnGap: 5,
                columns: [
                  {
										width: 'auto',
                    text: "Signature",
                  },
                  {
										width: 'auto',
                    stack: [
                      {
                        text: " ",
                      },
                      {
												marginTop: -9,
                        text: "_______________",
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
			{
				bold: true,
				style: {fontSize: 8},
				marginTop: 10,
				text: 'ማሳሰቢያ፡ ዋናው ለሂሳብ ክፍል፣ 2ኛው ቅጠል ለአምጭው፣ ሦሰተኛው ቅጠል ለስቶክ ካርድ ጸሀፊ እና አራተኛው ከሰነዱ ጋር ይቀመጣል፡፡'
			},
			{
				bold: true,
				style: {fontSize: 8},
				marginTop: 10,
				text: 'Note: Original to the Account, Second Copy to Deliverer, Third Copy to Stock card clerk and 4th copy to Pad.'
			},
			{
				bold: true,
				style: {fontSize: 8},
				marginTop: 10,
				text: 'ተቀባያቸው አንድ አይነት ለሆኑ መድሀኒቶችና የህክምና መገልገያዎች  በአንድ ቅጠል ይዘጋጃል፡፡ ዋጋ ያልተተመነላቸው መድሀኒቶችና የህክምና መገልገያዎች በውስጥ ኤክስፐርቶች ዋጋ ይገመትላቸዋል፡፡'
			},
			{
				bold: true,
				style: {fontSize: 8},
				marginTop: 10,
				text: 'Drugs and medical supplies with the same receiver will be prepared in a similar page of voucher. Drugs and medicals supplies which don’t have cost will be estimated by experts in the facility'
			}
    ],
  };
  content.value = con;
}

gnPdf();
</script>

<template>
  <PdfMaker v-if="content" :content="content" />
</template>
