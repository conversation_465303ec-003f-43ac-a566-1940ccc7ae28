<script setup>
import { ref } from "vue";
import PdfMaker from "../PdfMaker.vue";
import { formatCurrency } from "@/utils/utils";

const props = defineProps({
  drugs: {
    type: Array,
    required: true,
  },
});

const con = ref({});
const generatePdf = () => {
  const docDefinition = {
    pageMargins: 10,
    content: [
      { text: "የዲስፐንሳሪ/ መጋዘን ቆጠራ ቅጽ", bold: true, fontSize: 8 },
      { text: "Dispensary/ store Inventory Form", bold: true, fontSize: 8 },
      {
        columns: [
          {
            width: "auto",
            text: "Name of Health facility:  _________",
          },
          {
            width: "auto",

            text: "Section (unit) store: ________ Page number: _______",
          },
          {
            width: "auto",
            text: "Date: _",
          },
        ],
      },
      { text: " " },
      {
        style: { fontSize: 6 },
        table: {
          headerRows: 2,
          widths: [
            "auto",
            "*",
            "auto",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
          ],
          body: [
            [
              {
                text: "SN",
                rowSpan: 2,
              },
              {
                text: "To be Filled Before the Physical Inventory",
                colSpan: 7,
              },
              {},
              {},
              {},
              {},
              {},
              {},
              {
                text: "To be filled during inventory",
              },
              {
                text: "To be Fillied after Physical Inventory",
                colSpan: 4,
              },
              {},
              {},
              {},
              {
                text: "Remark",
                rowSpan: 2,
              },
            ],
            [
              {},
              {
                text: "Drug Code",
              },
              {
                text: "Description (Drug name, dosage form, strength and brand)",
              },
              { text: "Retail Unit" },
              { text: "Batch No" },
              {
                text: "Retail Price",
                colSpan: 2,
              },
              {},
              { text: "Expired Date" },
              {
                text: "Pysical Qty",
                alignment: "center",
              },
              { text: "Unit Cost", colSpan: 2 },
              {},
              {
                text: "Total Cost",
                colSpan: 2,
              },
              {},
              {},
            ],
            // Add rows for each drug in the props.drugs array
            ...props.drugs.map((drug, index) => [
              { text: (index + 1).toString() }, // SN
              { text: drug.drugCode || "" }, // Drug Code
              { text: `${drug.drugDescription}` }, // Description
              { text: drug?.unit || "" }, // Retail Unit
              { text: drug.batchNumber || "" }, // Batch No
              { text: formatCurrency(drug?.retailPrice?.toString() || ""), colSpan: 2 }, // Retail Price
              {},
              {
                text: drug.expDate
                  ? new Date(drug.expDate).toLocaleDateString()
                  : "",
              }, // Expired Date
              { text: drug?.actualDispensaryAmount?.toString() || "" }, // Physical Qty
              { text: formatCurrency(drug.unitPrice?.toString() || ""), colSpan: 2 }, // Unit Cost
              {},
              {
                text: formatCurrency(
                  drug?.actualDispensaryAmount * drug.unitPrice || ""
                ),
                colSpan: 2,
              }, // Total Cost
              {},
              { text: drug?.remark || "" }, // Remark
            ]),
            [
              {
                colSpan: 11,
                alignment: "right",
                text: "SUM Total",
              },
              {},
              {},
              {},
              {},
              {},
              {},
              {},
              {},
              {},
              {},
              {
                text: formatCurrency(
                  `${props.drugs.reduce((sum, el) => {
                    return (sum += el?.actualDispensaryAmount * el.unitPrice);
                  }, 0)}`
                ),
                colSpan: 2,
              },
              {},
              {
                text: " ",
              },
            ],
          ],
        },
        layout: {
          hLineWidth: () => 0.5,
          vLineWidth: () => 0.5,
          hLineColor: () => "black",
          vLineColor: () => "black",
          paddingTop: () => 8, // Increased padding for taller rows
          paddingBottom: () => 8, // Increased padding for taller rows
        },
      },
      {
        marginTop: 20,
        fontSize: 9,
        columns: [
          {
            text: "Inventory Registered by Name ",
          },
          {
            text: "Counted by Name ",
          },
          {
            text: "Recounted by Name ",
          },
        ],
      },
      {
        marginTop: 20,
        fontSize: 9,
        columns: [
          {
            text: "Signature",
          },
          {
            text: "Signature ",
          },
          {
            text: "Signature ",
          },
        ],
      },
      { text: " " },
      {
        text: "Responsible Name   , ,  Persons",
      },
      {
        text: "Signature's    , , ",
      },
    ],
  };

  con.value = docDefinition;
};

generatePdf();
</script>

<template>
  <PdfMaker v-if="con" :content="con" />
</template>
