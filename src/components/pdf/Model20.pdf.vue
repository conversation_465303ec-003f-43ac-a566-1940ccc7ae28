<script setup lang="ts">
import { getBgbase64Url } from "@/utils/utils";
import PdfMaker from "../PdfMaker.vue";
import { ref } from "vue";

const props = defineProps({
  batch: {
    type: Array,
    required: true,
  },
});

console.log("hehe", props.batch);

const content = ref();
async function gnPdf() {
  const star = await getBgbase64Url("/start.jpg");
  const con = {
    pageMargins: [10, 70, 10, 20],
    content: [
      {
        columns: [
          {
            width: "auto",
            stack: [
              {
                style: { fontSize: 8 },
                text: "ሞዴል",
              },
              {
                width: "auto",
                style: { fontSize: 8 },
                text: "Model 20/ጤና",
              },
            ],
          },
          {
            width: "*",
            alignment: "center",
            stack: [
              {
                image: star,
                fit: [30, 30],
              },
              {
                marginTop: 10,
                text: "በኢትዮጵያ ፌደራላዊ ዲሞክራሲያዊ ሪፐብሊክ የገንዘብና የኢኮኖሚ ልማት ሚኒስቴር",
              },
              {
                marginTop: 10,
                style: { fontSize: 8 },
                text: "The Federal Democratic Republic of Ethiopia, Ministry of Finance and Economic Development",
              },
              {
                alignmnet: "center",
                marginTop: 10,
                bold: true,
                columnGap: 5,
                style: { fontSize: 9 },
                columns: [
                  {
                    width: "auto",
                    text: "የ",
                  },
                  {
                    width: "auto",
                    stack: [
                      {
                        text: " ",
                      },
                      {
                        marginTop: -10,
                        text: "______________________",
                      },
                    ],
                  },
                  {
                    width: "auto",
                    text: "የመድሀኒትና የህክምና መገልገያ የክምችት መጠን ማሳወቂያና መጠየቂያ እና መፍቀጃ ቅጽ",
                  },
                ],
              },
              {
                fontSize: 8,
                marginTop: 10,
                text: "Internal Facility Stock Status Report and Requisition Form",
              },
            ],
          },
          {
            width: "auto",
            stack: [
              {
                style: { fontSize: 8 },
                text: "ሴሪ ቁጥር",
              },
              {
                style: { fontSize: 8 },
                text: "Seri No",
              },
            ],
          },
        ],
      },
      {
        marginTop: 10,
        headerRows: 3,
        style: { fontSize: 6 },
        table: {
          widths: [
            "auto",
            "auto",
            "*",
            "auto",
            "auto",
            "auto",
            "auto",
            "auto",
            "auto",
            "auto",
            "auto",
            "auto",
          ],
          body: [
            [
              {
                bold: true,
                text: "ተራ ቁጥር S/n 1",
                style: { fontSize: 6 },
                rowSpan: 3,
              },
              {
                bold: true,
                text: "የመድኃኒት ኮድ Internal drug Code",
                style: { fontSize: 6 },
                rowSpan: 3,
              },
              {
                bold: true,
                rowSpan: 3,
                style: { fontSize: 6 },
                stack: [
                  {
                    text: "መግለጫ፡ (የመድሀኒቱ/የቅመሙ/ ስም፣ ጥንካሬ፣ የዝግጅት አይነት፣ ንግድ ስም  ካለ ይጠቀስ)",
                  },
                  {
                    marginTop: 10,
                    text: "Description: (Drug/Reagent/ Supplies/ Name Strength, Dosage Form, (Brand, if any)-",
                  },
                ],
              },
              {
                bold: true,
                text: "Completed by Dispensing  Units",
                colSpan: 4,
                style: { fontSize: 6 },
              },
              {},
              {},
              {},
              {
                bold: true,
                colSpan: 3,
                text: "Completed By Store",
                style: { fontSize: 6 },
              },
              {},
              {},
              {
                bold: true,
                rowSpan: 2,
                text: "Qty to be Supllied",
                style: { fontSize: 6 },
              },
              { rowSpan: 2, text: "Remark", style: { fontSize: 6 } },
            ],
            [
              {},
              {},
              {},
              { bold: true, style: { fontSize: 6 }, text: "Beg Stock" },
              { bold: true, style: { fontSize: 6 }, text: "QTY Received" },
              { bold: true, text: "Loss /Adj.", style: { fontSize: 6 } },
              { bold: true, text: "End stock", style: { fontSize: 6 } },
              {
                bold: true,
                text: "Calculated Consumption E=A+BC-D",
                style: { fontSize: 6 },
              },
              { bold: true, text: "Max F=E*2", style: { fontSize: 6 } },
              { bold: true, text: "Qty to Max G=F-D", style: { fontSize: 6 } },
              {},
              {},
            ],
            [
              {},
              {},
              {},
              { bold: true, text: "A" },
              { bold: true, text: "B" },
              { bold: true, text: "C" },
              { bold: true, text: "D" },
              { bold: true, text: "E" },
              { bold: true, text: "F" },
              { bold: true, text: "G" },
              { bold: true, text: "H" },
              { bold: true, text: "I" },
            ],
            ...(props.batch || []),
          ],
        },
      },
      {
        marginTop: 20,
        columnGap: 5,
        style: { fontSize: 8 },
        columns: [
          {
            width: "auto",
            stack: [
              {
                text: "የጠያቂ ሪፖርት አቅራቢ ሥም:",
              },
              {
                columnGap: 0,
                columns: [
                  {
                    text: "Reported/Requested by",
                  },
                  {
                    stack: [
                      {
                        text: " ",
                      },
                      {
                        marginTop: -9,
                        text: "____________________",
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            width: "auto",
            stack: [
              {
                text: "ፊርማና ቀን:",
              },
              {
                columnGap: 5,
                columns: [
                  {
                    width: "auto",
                    text: "Signature & date",
                  },
                  {
                    width: "auto",
                    stack: [
                      {
                        text: " ",
                      },
                      {
                        marginTop: -9,
                        text: "_______________",
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            width: "auto",
            stack: [
              {
                text: "የአጽዳቂ ሥም:",
              },
              {
                columnGap: 5,
                columns: [
                  {
                    width: "auto",
                    text: "Approved by Name:",
                  },
                  {
                    width: "auto",
                    stack: [
                      {
                        text: " ",
                      },
                      {
                        marginTop: -9,
                        text: "____________________",
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            width: "auto",
            stack: [
              {
                text: "ፊርማና ቀን",
              },
              {
                columnGap: 5,
                columns: [
                  {
                    width: "auto",
                    text: "Signature & Date",
                  },
                  {
                    width: "auto",
                    stack: [
                      {
                        text: " ",
                      },
                      {
                        marginTop: -9,
                        text: "_______________",
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        marginTop: 20,
        bold: true,
        fontSize: 8,
        text: "ማሳሰቢያ፡ ለማንኛውም መድሃኒት መልሶ መጠየቂያ አነስተኛው መጠን መሰላትና መተከል አለበት",
      },
      {
        marginTop: 5,
        bold: true,
        fontSize: 8,
        text: "Remark: Ending stock /reorder level should be set for every drug",
      },
    ],
  };
  content.value = con;
}

gnPdf();
</script>

<template>
  <PdfMaker v-if="content" :content="content" />
</template>
