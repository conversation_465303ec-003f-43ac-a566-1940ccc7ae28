<script setup lang="ts">
import { computed, PropType, ref, watch } from "vue";
import Button from "./Button.vue";
import NestedDrawerButton from "./NestedDrawerButton.vue";
import icons from "@/utils/icons";
import { RouterLink, useRoute, useRouter } from "vue-router";
import { useAuth } from "@/stores/auth";

const authStore = useAuth();
let user = authStore.auth?.user;

if (!user) {
  let storedUser = localStorage.getItem("userDetail");
  if (storedUser) {
    user = JSON.parse(storedUser);
  }
}

export type Nav = {
  path?: string;
  icon: string;
  name: string;
  meta?: {
    requiresAuth: boolean;
    privileges: string[];
  };
  navs?: Nav[];
  childRoutes?: string[];
};

const props = defineProps({
  navs: {
    type: Object as PropType<Nav>,
    required: true,
  },
});

const router = useRouter()
const route = useRoute();
const open = ref(false);

watch(
  () => route.path,
  (newPath) => {
    if (!props.navs.path && (!props.navs.childRoutes || props.navs.childRoutes.length === 0)) {
      return;
    }
    const isRelatedToThisNav =
      (props.navs.path && newPath.startsWith(props.navs.path)) ||
      (props.navs.childRoutes && props.navs.childRoutes.some(path => newPath.startsWith(path)));

    if (!isRelatedToThisNav) {
      open.value = false;
    }
  },
  { immediate: true } // Check immediately when component mounts
);

// const hasPrivilege = !props.navs.meta?.privileges || props.navs.meta?.privileges?.find((el) => {
//   return (user?.privileges || []).includes(`ROLE_${el}`)
// })

const hasPrivilege = true

function handleRoute() {
  if (hasPrivilege && props.navs.path) {
    router.push(props.navs.path);
  }
  open.value = !open.value;
}

const isActive = computed(() => {
  // Check if current route matches this nav's path
  if (route.path === props.navs.path) return true;

  // Check if current route is a child route
  if (Array.isArray(props.navs.childRoutes)) {  // Check if it's an array first
    return props.navs.childRoutes.some(childPath => {
      const isChildRoute = route.path === childPath || route.path.startsWith(childPath);
      return isChildRoute;
    });
  }

  return false;
});

const isNestedActive = computed(() => {
  if (!props.navs?.navs?.length) return false;
  return props.navs.navs.some(nestedNav => {
    // Check if current route matches nested path or any of its children
    return route.path === nestedNav.path || (nestedNav.childRoutes && nestedNav.childRoutes.some(path => route.path.startsWith(path))) ||
      (nestedNav.navs && nestedNav.navs.some(child => route.path === child.path));
  });
});

// Auto-open the drawer if a nested route is active
watch(isNestedActive, (active) => {
  if (active) {
    open.value = true;
  }
}, { immediate: true });

</script>
<template>
  <div class="__drawer w-full" :class="{ 'parent-open': isNestedActive }">
    <a v-if="navs?.navs?.length || hasPrivilege" @click.prevent.stop="handleRoute" :class="{
      'router-link-active': isActive, 'router-link-exact-active': isActive, '!bg-red-500': !open || !navs?.navs?.length
    }" class="flex-1 rounded text-base-clr6 transition-all duration-200 ease-linear" :href="navs.path">
      <div class="!bg-transparent flex flex-col gap-2 flex-1">
        <Button
          class=" hover:bg-gray-200 hover:w-full hover:text-primary text-white max-w-full flex gap-14 !justify-start items-center">
          <div class="flex gap-2 items-center rounded">
            <i class="icon" :class="{ 'active-icon': isActive }" v-html="navs.icon" />
            <span class="!text-xs !font-normal">{{ navs.name }}</span>
          </div>
          <div v-if="navs?.navs?.length" class="pointer-events-none ml-auto grid place-items-center pl-4">
            <i class="transition-all duration-100" :class="[open ? 'rotate-180' : 'rotate-0']"
              v-html="icons.downAngle" />
          </div>
        </Button>
      </div>
    </a>
  </div>
  <div class="border-l w-[90%] flex flex-col  ml-5 pl-3 mt-1 transition-all duration-200"
    v-if="open && navs?.navs?.length">
    <div class="flex flex-col gap-2 flex-1" v-if="open && navs?.navs?.length">
      <NestedDrawerButton :navs="navs?.navs" />
    </div>
  </div>
</template>

<style scoped>
.__drawer .router-link-active {
  background-color: theme("colors.gray.500");
  color: rgb(var(--base-clr2));
}

.__drawer .router-link-exact-active button {
  background-color: white;
  color: #ED6033;
  font-weight: 600;
  font-size: 14px;
  border-radius: 4px;
  border: 0.38px solid #FFFFFF;
  padding-top: 12.18px;
  padding-bottom: 12.18px;
  padding-left: 15.22px;
  padding-right: 15.22px;
}

.icon {
  color: currentColor;
  /* Inherits from parent by default */
}

.active-icon {
  color: #ED6033 !important;
  /* Force active color */
}

.active-icon:hover {
  color: #ED6033 !important;
}

/* Alternative for SVG icons */
/* .icon :deep(svg) {
  fill: currentColor;
} */

.active-icon :deep(svg) {
  fill: #ED6033 !important;
}

.__drawer .parent-open {
  background-color: rgba(255, 255, 255, 0.16) !important;
  font-weight: 600;
  font-size: 14px;
  border-radius: 4px;
  padding: 12.18px 15.22px;
}
</style>
