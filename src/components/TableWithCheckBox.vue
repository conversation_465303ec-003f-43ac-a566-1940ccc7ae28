<script setup lang="ts" generic="T">
import { computed, ref, watch, type PropType } from "vue";
import Table from "./Table.vue";
import Table2 from "./Table2.vue";

const props = defineProps({
  modelValue: {
    type: Array as PropType<string[]>,
  },
  headers: [Array, Object],
  rows: {
    type: Array as PropType<T[]>,
    default: [],
  },
  cells: Object,
  toBeSelected: {
    type: String,
    required: true,
  },
  last: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(["update:modelValue"]);
const selected = ref<any[]>(props.modelValue ? [...props.modelValue] : []);
function toggleSelectAll(ev: Event) {
  const target = ev.target as HTMLInputElement;
  if (target.checked) {
    selected.value = [...props.rows];
    // selected.value = props.rows.map((el: any) => {
    //   return props.toBeSelected
    //     .split(".")
    //     .reduce((state: any, name: string) => {
    //       return state[name];
    //     }, el);
    // });
  } else {
    selected.value = [];
  }
}

function toggleData(data: any) {
  const idx = selected.value.findIndex((el: any) => el[props.toBeSelected] === data[props.toBeSelected]);
  if (idx == -1) {
    selected.value.push(data);
  } else {
    selected.value.splice(idx, 1);
  }
}

function isRowSelected(row: any) {
  return selected.value.some((el: any) =>
    el[props.toBeSelected] === row[props.toBeSelected]
  );
}

const allSelected = computed(() => {
  return selected.value?.length == props.rows?.length && props.rows.length > 0;
});

watch(
  selected,
  () => {
    emit("update:modelValue", selected.value);
  },
  { deep: true, flush: "post" }
);

// watch(
//   () => props.modelValue,
//   (newVal) => {
//     if (newVal) {
//       selected.value = [...newVal];
//     } else {
//       selected.value = [];
//     }
//   }
// );
</script>

<template>
  <Table :first-col="!last" :last-col="last" :headers="headers || []" :cells="cells || {}" :rows="rows || []">
    <template #headerLast v-if="last">
      <input class="custom-checkbox" :checked="allSelected" @change="toggleSelectAll" type="checkbox" />
    </template>
    <template #headerFirst v-if="!last">
      <input class="custom-checkbox" :checked="allSelected" @change="toggleSelectAll" type="checkbox" />
    </template>
    <template v-if="last" #lastCol="{ row }">
      <input class="custom-checkbox" :checked="isRowSelected(row)" @change="() => toggleData(row)" type="checkbox" />
    </template>
    <template v-if="!last" #select="{ row }">
      <input class="custom-checkbox" :checked="isRowSelected(row)" @change="() => toggleData(row)" type="checkbox" />
    </template>
    <template #actions="{ row }">
      <slot name="actions" :row="row" />
    </template>
  </Table>
</template>

<style scoped>
.custom-checkbox {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid #ED6033;
  border-radius: 4px;
  /* Changed to 50% for a circular checkbox */
  cursor: pointer;
  position: relative;
  background-color: white;
}

.custom-checkbox:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.custom-checkbox:checked {
  background-color: #ED6033;
  /* Red background when checked */
  border-color: #ED6033;
}

.custom-checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(237, 96, 51, 0.25);
}
</style>
