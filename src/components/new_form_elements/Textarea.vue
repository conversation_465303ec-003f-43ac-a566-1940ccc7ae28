<script setup>
import { ref, watch } from "vue";
import { InputParent } from "../new_form_builder";
import InputLayout from "@/components/new_form_elements/NewInputLayout.vue";

const props = defineProps({
  modelValue: {
    type: String,
  },
  validation: {
    type: [String, Object],
  },
});

const emit = defineEmits(["update:modelValue"]);
const value = ref(props.modelValue || "");

watch(value, () => {
  emit("update:modelValue", value.value);
});
</script>

<template>
  <InputParent
    v-model="value"
    :validation="validation"
    v-slot="{ setRef, error }"
  >
    <InputLayout
      :validation="validation"
      :class="$attrs?.class + ' !rounded'"
      :label="$attrs.label"
      :error="error"
    >
      <textarea :ref="setRef" />
      <slot name="right" />
    </InputLayout>
  </InputParent>
</template>
