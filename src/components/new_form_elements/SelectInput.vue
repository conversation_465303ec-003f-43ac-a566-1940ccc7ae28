<script setup>
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  item: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:modelValue']);

const isSelected = computed(() => {
  return props.modelValue.some(selected => selected.code === props.item.code);
});

function toggleSelection(checked) {
  let newSelection = [...props.modelValue];
  if (checked) {
    newSelection.push(props.item);
  } else {
    newSelection = newSelection.filter(selected => selected.code !== props.item.code);
  }
  emit('update:modelValue', newSelection);
}
</script>

<template>
  <input type="checkbox" :checked="isSelected" @change="(e) => toggleSelection(e.target.checked)"
    class="h-4 w-4 rounded border-gray-300 cursor-pointer" />
</template>
