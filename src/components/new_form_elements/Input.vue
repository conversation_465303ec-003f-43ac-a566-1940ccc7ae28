<script setup>
import InputParent from "../new_form_builder/InputParent.vue";
import InputLayout from "./NewInputLayout.vue";

const props = defineProps({
  focus: {
    type: Boolean,
    default: false
  },
  validation: {
    type: [String, Object]
  }
})
</script>
<template>
  <InputParent :validation="validation" v-slot="{ setRef, error, value, changeValue }">
    <InputLayout :validation="validation" :error="error" :label="$attrs?.label">
      <div class="flex flex-1">
        <slot class="" name="left" />
        <input
          v-focus="focus"
          :ref="setRef"
        />
        <slot name="right" />
      </div>
    </InputLayout>
  </InputParent>
</template>