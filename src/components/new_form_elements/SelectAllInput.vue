<script setup>
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  items: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['update:modelValue']);

const isAllSelected = computed(() => {
  return props.items.length > 0 && props.modelValue.length === props.items.length;
});

function toggleAll(checked) {
  emit('update:modelValue', checked ? [...props.items] : []);
}
</script>

<template>
  <input type="checkbox" :checked="isAllSelected" @change="(e) => toggleAll(e.target.checked)"
    class="h-4 w-4 rounded border-gray-300 cursor-pointer" />
</template>
