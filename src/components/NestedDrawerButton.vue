<script setup lang="ts">
import { PropType } from "vue";
import DrawerButtonParent, { Nav } from "./DrawerButtonParent.vue";

defineProps({
  navs: {
    type: Array as PropType<Nav[]>,
    required: true,
  },
});

</script>
<template>
  <DrawerButtonParent :navs="nav" v-for="nav in navs" :key="nav.name" />
</template>

<style scoped>
.nested-drawer-button::before {
  content: '';
  position: absolute;
  left: -1rem;
  top: 50%;
  width: 0.75rem;
  height: 1px;
  background-color: #FFDED7;
}
</style>