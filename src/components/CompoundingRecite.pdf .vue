<script setup>
import { formatCurrency } from '@/utils/utils';
import PdfMaker from './PdfMaker.vue';

const props = defineProps({
	patient: {
		type: Object,
		required: true
	},
	prescriptionDetail: {
		type: Array,
		required: true
	},
	total_payment_amount: {
		type: Number,
		required: true
	},
	fsNumber: {
		type: Number,
		required: true
	},
	default: {},
	alertPhoto: {
		type: String,
	}
})
console.log(props?.fsNumber);


function createEmptyRows(numRows) {
	const rows = [];
	for (let i = 0; i < numRows; i++) {
		rows.push([
			{ text: '' },
		]);
	}
	return rows;
}
function getInput(name = ' ', width = 150) {
	return {
		width: 'auto',
		stack: [
			{
				text: name,
				fontSize: 10
			},
			{
				canvas: [
					{
						type: 'line',
						x1: 0, y1: 2,
						x2: width, y2: 2,
						lineWidth: 1
					},
				]
			}
		]
	}
} const fullName = "John Do<PERSON>"; // Replace with your dynamic data source
const sex = "Male";          // Dynamic data for Sex
const age = "30";


console.log(props?.prescriptionDetail.map(el => {
	return [{
		text: el.drugCode || ''
	},
	{ text: el.drugName || '' },
	{ text: el.unit || '' },
	{ text: el.totalQuantity || '' },
	{ text: el.list_price || '' },
	{ text: ' ' },
	{ text: el.drugPrice || '' },
	{ text: ' ' },
	]
}),)
// props?.prescriptionDetail.map(detail => [{ text: detail.drugName }])

const emptyRows = createEmptyRows(8);

const docDefinition = {
	content: [
		{
			columns: [
				{
					text: 'የእጅ በእጅ ሽያጭ ደረሰኝ\nCash Sales TTicke',
					fontSize: 10,
					bold: true,
					alignment: 'center',
					margin: [30, 0, 20, 0]
				},
				{
					image: props.alertPhoto,
					fit: [50, 50],
					alignment: 'center',
					margin: [0, -10, 0, 0]
					// pageBreak: 'after'
				},
				{
					text: 'ሴሪ.ቁ\nSeri.No',
					fontSize: 10,
					bold: true,
					alignment: 'center',
					margin: [30, 0, 20, 0]
				},
			]
		},
		{
			columns: [
				{
					alignment: 'right',
					text: 'No: ',
					width: '*',
					bold: true,
					margin: [0, 0, 5, 10]
				},
				{
					alignment: 'right',
					text: props?.fsNumber,
					width: 'auto',
					margin: [0, 0, 0, 0]
				}
			]
		},
		{
			text: 'በኢትዮጵያ ፌደራላዊ ዲሞክራሲያዊ ሪፐብሊክ የገንዘብና ኢኮኖሚ ልማት ሚኒስቴር',
			style: 'header',
			margin: [0, 0, 0, 3]
		},
		{
			text: 'The Federal Democratic Rebublic Of Ethiopia Ministry Of Finance and Economic Development',
			style: 'header',
		},
		{
			text: 'የመድሃኒት ፤የላብራቶሪ ቅመሞች ፤ ሪኤጀንቶችና እና የህክምና መገልገያዎች የእጅ በእጅ ሽያጭ ደረሰኝ',
			style: 'header',
		},
		{
			text: 'Cash Sales  Ticket For Drugs, Medicals Supplies and Laboratory Reagents /X-rays',
			style: 'header',
			margin: [0, 0, 0, 20]
		},

		{
			style: 'tableExample',
			color: '#444',
			table: {
				widths: ['*', 150, 50, 50, 60, 20, 60, 20],
				headerRows: 1,
				heights: 10,
				body: [
					[
						{ text: 'የደንበኛው ስም ከነኣባት\nFull Name Of Client:     ' + props?.patient.firstName + '   ' + props?.patient.fatherName + '   ' + props?.patient.grandFatherName, colSpan: 4, style: 'subheader', alignment: 'left', border: [true, true, false, true] },
						{},
						{},
						{},
						{ text: 'ጾታ\nSex:     ' + props?.patient.gender, colSpan: 2, style: 'subheader', alignment: 'left', },
						{},
						{ text: 'እድሜ\nAge:     ' + age, style: 'subheader', colSpan: 2, alignment: 'left', },
						{}
					],
					[
						{ text: 'ኣድራሻ\nClient Address', style: 'subheader', colSpan: 2, alignment: 'left' }, {},
						{ text: 'ወረዳ\nWoreda ', style: 'subheader', colSpan: 2, alignment: 'left' },
						{},
						{ text: 'ቀበሌ\nKebele', style: 'subheader', colSpan: 2, alignment: 'left' }, {},
						{ text: 'ካርድ ቁጥር\nCard No', style: 'subheader', colSpan: 2, alignment: 'left' },
						{},
					],
					[
						{ text: 'የመድሃኒት ኮድ\nDrug Code/ Service Code', style: 'subheader', alignment: 'left', margin: [0, 0, 0, 10] },
						{ text: 'መግለጫ\n(የመድሃኒት ፤ የቅመሞች መግለጫ ስም፤ ጥንካሬ፤ ኣይነት ንግድ ስም ካለ ይጠቀስ\nDescription Drugs Reagent, Supplies, Dosage Form(Brand if any)', style: 'subheader', alignment: 'left' },
						{ text: 'የችርቻሮ መለከያ\nRetail Unit', style: 'subheader', alignment: 'left', margin: [0, 40, 0, 0], },
						{ text: 'ብዛት\nRetail QTY', style: 'subheader', alignment: 'left', margin: [0, 40, 0, 0], },
						{ text: 'የችርቻሮ ዋጋ\nRetail Price', style: 'subheader', colSpan: 2, alignment: 'left', margin: [0, 40, 0, 0], }, {},
						{ text: 'ጠቅላላ የችርቻሮ ዋጋ\nTotalR_Price', style: 'subheader', colSpan: 2, alignment: 'left', margin: [0, 40, 0, 0], }, {},

					],
					...props?.prescriptionDetail.map(el => {
						return [{
							text: el.drugCode || ''
						},
						{ text: el.drugName || '' },
						{ text: el.unit || '' },
						{ text: el.totalQuantity || '' },
						{ text: el.list_price || '' },
						{ text: ' ' },
						{ text: el.drugPrice || '' },
						{ text: ' ' },
						]
					}),
					// { text: ${props?.total_payment_amount}.split('.')?.[1] || .00 }
					//(Math.ceil(row.total_payment_amount * 100) / 100).toFixed(2);
					[
						{ text: 'የገንዘብ ልክ ብፊደል ጠቅላላ ድምር  Sum Total Amount In Word', colSpan: 6, style: 'subheader', alignment: 'left', margin: [0, 4, 0, 0] }, {}, {}, {}, {}, {}, { text: `${formatCurrency(props?.total_payment_amount)}`.split('.')?.[0] },
						{ text: `.${(Math.ceil((props?.total_payment_amount || 0) * 100) / 100).toFixed(2).split('.')[1]}` },
					],
					[
						{ text: 'የመድኃኒት ባለሙያ ፌርማና ቀን/  Signature Dispenser\'s & Date', colSpan: 6, style: 'subheader', alignment: 'left', margin: [0, 4, 0, 0] }, {}, {}, {}, {}, {}, {}, {},
					],
				]
			}
		},

		{
			marginTop: 10,
			columns: [
				{
					text: 'ማሳሰብያ፦ ይህ ካርኒ ሶስት ኮፒ ሆኖ የመጀመሪያው ለደንበኛው ፤ 2ኛው ለሂሳብ ክፍል 3ኛ ከጥራዝ ጋር ይቀመጣል\n Note ፦ The Original Shall be given to the client, 2nd copy to Cashier, the 3rd to pad. ', style: 'subheader'
				},
				{
					text: 'ይህ ካርኒ የፋርማሲ እና ላብራቶሪው ኤክስረይ ህጋዊ የሸያጭ ደረሰኝ ጭምር ሆኖ ያገለግላል\n The ticket Service also as Logic Recite The Pharmacy Laboratory X-ray', style: 'subheader'
				},
			]
		},
		{ text: 'ለሂሳብ ስራ ኣገልግሎት ብቻ /Only for Accounting purpose', margin: [40, 10, 0, 6], bold: true },

		{
			columns: [
				{
					table: {
						widths: [80, 80, 80, 80],
						headerRows: 1,
						heights: 10,
						body: [
							[
								{ text: 'የበጀት መደብ Budget Catagory', alignment: 'center', style: 'subheader' },
								{ text: 'የሂሳብ መደብ Account', alignment: 'center', style: 'subheader' },
								{ text: 'Code ደቢት Debit ', alignment: 'center', style: 'subheader' },
								{ text: 'ክረዲት Credit', alignment: 'center', style: 'subheader' },
							],
							['', '', '', ''],
							['', '', '', ''],
							['', '', '', ''],
							['', '', '', ''],

						]
					}
				},
				{ text: 'የክፍሉ ማህተም\n Unit of the health facility stamp', margin: [50, 70, 0, 0], fontSize: 8, bold: true }
			]
		},
		{ text: 'የገንዘብ ተቀባይ ፊርማ እና ቀን\nSignature of Cashier & Date', margin: [10, 5, 0, 0], fontSize: 8, bold: true }
	],
	styles: {
		header: {
			fontSize: 10,
			bold: true,
			alignment: 'center',
			decoration: '',
		},
		subheader: {
			fontSize: 8,
			bold: true,
		}
	}
};
</script>
<template>
	<PdfMaker v-if="docDefinition" :content="docDefinition" />
</template>