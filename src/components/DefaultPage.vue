<script setup>
import icons from '@/utils/icons';
import { useRoute, useRouter } from 'vue-router';

// const props = defineProps({
// 	title: {
// 		type: String,
// 		required: true
// 	},
// 	size: {
// 		type: String,
// 		default: 'xl'
// 	}
// })

const route = useRoute()
</script>

<template>
	<div class="w-full max-w-[1440px] mx-auto p-[34px] flex flex-col gap-6">
		<div class="flex items-center gap-2">
			<div @click="$router.go(-1)" class="size-6 grid place-items-center">
				<i class="text-black" v-html="icons.leftcirclearrow" />
			</div>
			<p class="capitalize" >{{ route.matched.at(-1).name }}</p>
		</div>
		<slot></slot>
	</div>
</template>