<script setup>
import icons from "@/utils/icons";
import { ref, watch } from "vue";
import { usePagination } from "@/composables/usePagination";
import Dropdown from "./Dropdown2.vue";
import InputParetnt from "@/components/new_form_builder/InputParent.vue";
import ResponseError from "@/components/new_form_elements/InputError.vue";

const props = defineProps({
  modelValue: {
    type: [String, Array, Object],
  },
  value: String,
  placeholder: String,
  searchCb: {
    type: Function,
    required: true,
  },
  selectCb: {
    type: Function,
  },
  option: {
    type: Object,
    required: true,
  },
  label: {
    type: String,
  },
  position: {
    type: String,
    default: "right-bottom",
  },
  onChange: {
    type: Function,
  },
});

const searchReq = usePagination({
  auto: false,
  cb: (data) =>
    props.searchCb({
      ...data,
    }),
});

const val = ref(props.modelValue || props.value || "");
const emit = defineEmits(["update:modelValue"]);

watch(val, () => {
  emit("update:modelValue", val.value);
});

const input = ref();
function selectRow(result) {
  props.selectCb && props.selectCb(result);
  input.value && (input.value.value = result[props.option.label]);
  emit("update:modelValue", result[props.option.value]);
  props.onChange && props.onChange(result);
}
</script>

<template>
  <div class="flex flex-col">
    <span :title="label" class="text-sm capitalize truncate" v-if="label">{{
      label
    }}</span>
    <Dropdown :position="position" v-slot="{ setRef, toggle }">
      <div
        class="focus-within:border-primary bg-gray-100 rounded-md border px-3 !pr-0 input-style flex"
      >
        <input
          ref="input"
          @click="toggle(true)"
          @input="
            (ev) => {
              searchReq.search.value = ev.target ? ev.target.value : '';
            }
          "
          class="bg-transparent flex-1 text-sm focus:shadow-none"
          :placeholder="placeholder"
        />
        <div class="size-10 grid place-items-center">
          <i
            v-if="searchReq.pending.value"
            class="animate-spin"
            v-html="icons.spinner"
          />
          <button
            class="size-6 grid place-items-center"
            v-else
            tabindex="0"
            :key="input?.value"
            @click.once.prevent="
              () => {
                input.value = '';
                selectCb && selectCb();
              }
            "
          >
            <i class="*:size-3" v-html="icons.close" />
          </button>
        </div>
      </div>
      <div :ref="setRef">
        <div
          tabindex="0"
          class="group w-80 h-max max-h-[20rem] overflow-auto show-scrollbar border flex flex-col *:flex-shrink-0 gap-2 rounded bg-white shadow-lg !p-2"
        >
          <template v-if="!searchReq.data.value?.content?.length">
            <div class="m-auto text-sm">
              {{
                !searchReq.dirty.value
                  ? "Type to Search..."
                  : "Search with Another Keyword"
              }}
            </div>
          </template>
          <slot name="searchResult" :result="searchReq.data.value?.content">
            <div
              v-for="result in searchReq.data.value?.content"
              :key="result[option.value]"
              v-ripple
              class="grid gap-2 cursor-pointer w-full items-center"
              @keydown.once.enter="
                () => {
                  selectRow(result);
                  toggle(false);
                }
              "
              @click.once="
                () => {
                  selectRow(result);
                  toggle(false);
                }
              "
            >
              <!-- <div class="grid place-items-center size-8">
                  <i class="*:size-4" v-html="icons.woreda" />
                </div> -->
              <b
                :key="input?.value"
                tabindex="0"
                class="flex-1 cursor-pointer font-normal text-sm p-1"
                >{{ result[option.label] }}</b
              >
            </div>
          </slot>
        </div>
      </div>
    </Dropdown>
  </div>
</template>
<style>
.search-select-grid {
  grid-template-columns: 2rem 1fr;
  grid-template-rows: minmax(2rem, max-content) max-content;
  grid-template-areas:
    "l m"
    "e d";
}
</style>
