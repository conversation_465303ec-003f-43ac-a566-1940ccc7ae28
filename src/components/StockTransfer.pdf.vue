<script setup>
import { formatCurrency } from '@/utils/utils';
import PdfMaker from './PdfMaker.vue';

const props = defineProps({
	patient: {
		type: Object,
		required: true
	},
	prescriptionDetail: {
		type: Array,
		required: true
	},
	totalPrice: {
		type: Number,
		required: true
	},
	fsNumber: {
		type: Number,
		required: true
	},
	default: {},
	alertPhoto: {
		type: String,
	}
})

function createEmptyRows(numRows) {
	const rows = [];
	for (let i = 0; i < numRows; i++) {
		rows.push([{}, {}, {}, {}, {}, {}, {}, {}, {}, {}]);
	}
	return rows;
}

const emptyRows = createEmptyRows(20);
const currentDate = new Date().toLocaleDateString();

const docDefinition = {
	pageSize: 'A4',
	pageOrientation: 'landscape',
	content: [
		{
			columns: [
				{
					stack: [
						{
							text: 'A- Issuing Section\nB- Receiving Section',
							fontSize: 10,
							bold: true,
							width: 'auto',
						},
						{
							text: 'መለያ ቁጥር/TIN/ 00045300439',
							fontSize: 10,
							margin: [0, 10, 0, 0]
						}
					],
					width: 'auto',
				},
				{
					stack: [
						{
							text: 'OFFICE OF KENEMA PHARMACIES INTER ORGANIZATION',
							fontSize: 12,
							bold: true,
							alignment: 'center',
						},
						{
							text: 'STOCK TRANSFER MEMO',
							fontSize: 16,
							bold: true,
							alignment: 'center',
							decoration: 'underline',
							margin: [0, 5, 0, 0]
						}
					],
					width: '*',
				},
				{
					stack: [
						{
							columns: [
								{
									text: 'Requisition No.',
									fontSize: 10,
									width: 'auto'
								},
								{
									canvas: [
										{
											type: 'line',
											x1: 0, y1: 10,
											x2: 100, y2: 10,
											lineWidth: 1
										}
									],
									width: '*'
								}
							]
						},
						{
							columns: [
								{
									text: 'Date',
									fontSize: 10,
									width: 'auto',
									margin: [0, 5, 0, 0]
								},
								{
									canvas: [
										{
											type: 'line',
											x1: 0, y1: 10,
											x2: 100, y2: 10,
											lineWidth: 1
										}
									],
									width: '*',
									margin: [5, 5, 0, 0]
								}
							]
						},
						{
							columns: [
								{
									text: 'No',
									fontSize: 10,
									width: 'auto',
									margin: [0, 5, 0, 0]
								},
								{
									text: '27020',
									fontSize: 10,
									width: '*',
									margin: [5, 5, 0, 0]
								}
							]
						}
					],
					width: 'auto',
				}
			],
			columnGap: 10,
		},

		{
			table: {
				headerRows: 2,
				widths: [25, 120, 35, 35, 50, 50, 55, 55, 50, 60, '*'],
				body: [
					[
						{ text: 'From', style: 'tableHeader', colSpan: 3, alignment: 'left', border: [false, false, false, true] }, {}, {},
						{ text: 'To', style: 'tableHeader', colSpan: 3, alignment: 'left', border: [false, false, false, true] }, {}, {},
						{ text: 'APE 003455/2013', style: 'tableHeader', colSpan: 5, alignment: 'right', border: [true, false, false, true] }, {}, {}, {}, {},
					],
					[
						{ text: 'A', style: 'tableHeader', colSpan: 6, alignment: 'center' }, {}, {}, {}, {}, {},
						{ text: 'B', style: 'tableHeader', colSpan: 5, alignment: 'center' }, {}, {}, {}, {}
					],
					[
						{ text: 'R. No.', style: 'tableHeader' },
						{ text: 'DESCRIPTION', style: 'tableHeader' },
						{ text: 'Unit', style: 'tableHeader' },
						{ text: 'Qty.', style: 'tableHeader' },
						{ text: 'Unit Cost', style: 'tableHeader' },
						{ text: 'Total Cost', style: 'tableHeader' },
						{ text: 'Unit Sell Price', style: 'tableHeader' },
						{ text: 'Total Sell Price', style: 'tableHeader' },
						{ text: 'Retail Price', style: 'tableHeader' },
						{ text: 'Exp. Date', style: 'tableHeader' },
						{ text: 'Remarks', style: 'tableHeader' }
					],


					// ...props.prescriptionDetail.map((item, index) => [
					// 	{ text: (index + 1).toString(),w alignment: 'center' },
					// 	{ text: item.drugName || '' },
					// 	{ text: item.unit || '' },
					// 	{ text: item.totalQuantity || '' },
					// 	{ text: item.list_price || '' },
					// 	{ text: item.drugPrice || '' },
					// 	{ text: '' },
					// 	{ text: '' },
					// 	{ text: '' },
					// 	{ text: item.expiryDate || '' }
					// ]),
					// ...createEmptyRows(Math.max(0, 20 - props.prescriptionDetail.length)),

				]
			},

			margin: [0, 10, 0, 0]
		},
		{
			columns: [
				{
					text: 'Reason for Transfer',
					fontSize: 10,
					bold: true,
					margin: [0, 10, 0, 0]
				},
				{
					canvas: [
						{
							type: 'line',
							x1: 0, y1: 20,
							x2: 650, y2: 20,
							lineWidth: 1
						}
					],
					margin: [-270, 5, 0, 0]
				},

			]
		},
		{
			columns: [
				{
					stack: [
						{
							canvas: [
								{
									type: 'line',
									x1: 0, y1: 10,
									x2: 80, y2: 10,
									lineWidth: 1
								}
							],
							margin: [0, 20, 20, 0]
						},
						{
							text: 'Issued by',
							fontSize: 10,
							bold: true,
							alignment: 'center',
							margin: [0, 15, 20, 0]
						},
					],
					width: 'auto'
				},
				{
					stack: [
						{
							canvas: [
								{
									type: 'line',
									x1: 0, y1: 10,
									x2: 80, y2: 10,
									lineWidth: 1
								}
							],
							margin: [0, 20, 20, 0]
						},
						{
							text: 'Received by',
							fontSize: 10,
							bold: true,
							alignment: 'center',
							margin: [0, 15, 20, 0]
						},
					],
					width: 'auto'
				},
				{
					stack: [
						{
							canvas: [
								{
									type: 'line',
									x1: 0, y1: 10,
									x2: 80, y2: 10,
									lineWidth: 1
								}
							],
							margin: [0, 20, 20, 0]
						},
						{
							text: 'Authorized by',
							fontSize: 10,
							bold: true,
							alignment: 'center',
							margin: [0, 15, 20, 0]
						},
					],
					width: 'auto'
				},
				{
					stack: [
						{
							canvas: [
								{
									type: 'line',
									x1: 0, y1: 10,
									x2: 80, y2: 10,
									lineWidth: 1
								}
							],
							margin: [0, 20, 20, 0]
						},
						{
							text: 'Delivered by',
							fontSize: 10,
							bold: true,
							alignment: 'center',
							margin: [0, 15, 20, 0]
						},
					],
					width: 'auto'
				},
				{
					stack: [
						{
							canvas: [
								{
									type: 'line',
									x1: 0, y1: 10,
									x2: 80, y2: 10,
									lineWidth: 1
								}
							],
							margin: [0, 20, 20, 0]
						},
						{
							text: 'Received by',
							fontSize: 10,
							bold: true,
							alignment: 'center',
							margin: [0, 15, 20, 0]
						},
					],
					width: 'auto'
				},
				{
					stack: [
						{
							canvas: [
								{
									type: 'line',
									x1: 0, y1: 10,
									x2: 80, y2: 10,
									lineWidth: 1
								}
							],
							margin: [0, 20, 20, 0]
						},
						{
							text: 'Checked & Computed by',
							fontSize: 10,
							bold: true,
							alignment: 'center',
							margin: [0, 15, 20, 0]
						},
					],
					width: 'auto'
				}
			],
			columnGap: 10
		},
		{
			columns: [
				{},
				{},
				{},
				{},
				{
					stack: [
						{
							canvas: [
								{
									type: 'line',
									x1: 0, y1: 10,
									x2: 80, y2: 10,
									lineWidth: 1
								}
							],
							margin: [0, 30, 0, 0]
						},
						{
							text: 'Authorized by',
							fontSize: 10,
							bold: true,
							alignment: 'center',
							margin: [0, 20, 0, 0]
						},
					],
					width: 'auto'
				},
				{}
			],
			columnGap: 5
		},
		{
			text: [
				'1. Original H.O.\n',
				'2. 1st Copy- Receiving Sec.\n',
				'3. 2nd Copy - Pad'
			],
			fontSize: 9,
			margin: [0, 20, 0, 0]
		}
	],
	styles: {
		tableHeader: {
			bold: true,
			fontSize: 9,
			color: 'black'
		}
	},
	defaultStyle: {
		fontSize: 10
	}
};
</script>
<template>
	<PdfMaker :content="docDefinition" />
</template>
