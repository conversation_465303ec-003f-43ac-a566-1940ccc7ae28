import { defineStore } from "pinia";
import { ref } from "vue";

export const useInsuredMemberStore = defineStore("insuredMember-store", () => {
  const requested = ref([]);

  function getAll() {
    return requested.value;
  }

  function reset() {
    requested.value = [];
  }

  function add(data) {
    requested.value.unshift(data);
  }

  function update(id, data) {
    const idx = requested.value.findIndex((el) => el.prescriptionUuid == id);

    if (idx == -1) return;

    requested.value.splice(idx, 1, data);
  }

  function set(data) {
    requested.value = data;
  }

  function remove(id) {
    requested.value = requested.value.filter((el) => el.prescriptionUuid != id);
  }

  return { requested, remove, set, getAll, reset, add, update };
});
