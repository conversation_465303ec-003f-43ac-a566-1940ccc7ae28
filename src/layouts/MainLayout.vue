<script setup>
import navs from "@/config/nav";
import { onBeforeUnmount, onMounted, ref, watch, computed } from "vue";
import Dropdown from "@/components/Dropdown.vue";
import {
  mdiArrowBottomLeft,
  mdiArrowLeft,
  mdiArrowLeftBox,
  mdiChevronDown,
  mdiFaceManProfile,
  mdiKeyChange,
  mdiLogout,
} from "@mdi/js";
import BaseIcon from "@/components/base/BaseIcon.vue";
import Icon from "@/components/Icon.vue";
import { useAuth } from "@/stores/auth";
import { useRouter } from "vue-router";
import { openModal } from "@customizer/modal-x";
import { getProfileById } from "@/features/profile/api/profileApi";
import { useApiRequest } from "@/composables/useApiRequest";
import { generateAvatar } from "@/utils/avatarHelper";

const show = ref(false);
const router = useRouter();
const auth = useAuth();
const getreq = useApiRequest();

function scrollHandler(currentPosition, position) {
  if (currentPosition >= position) {
    show.value = true;
  } else {
    show.value = false;
  }
}
function logout() {
  localStorage.clear();
  window.location.href = "/login";
}
const fetchedImage = ref(null);
const userName = ref("");

getreq.send(
  () => getProfileById(auth.auth?.user?.userUuid),
  (res) => {
    userName.value = res?.data?.userName;
    fetchedImage.value = res?.data?.imageData
      ? `data:image/png;base64,${res?.data?.imageData}`
      : null;
    localStorage.setItem("fetchedImage", fetchedImage.value);
  }
);

// Generate a dicebear avatar as fallback when no profile image is available
const profileImage = computed(() => {
  return generateAvatar({
    imageData: fetchedImage.value
      ? fetchedImage.value.replace("data:image/png;base64,", "")
      : null,
    userName: userName.value,
    userId: auth.auth?.user?.userUuid,
    roleName: auth.auth?.user?.roleName,
  });
});

const open = ref(false);

const menuOpen = () => {
  open.value = !open.value;
};
</script>
<template>
  <div
    v-scroll-position.40="scrollHandler"
    class="h-full flex flex-col gap-4 overflow-hidden"
  >
    <header
      :class="[show && 'shadow-lg']"
      class="max-w-full box-border flex justify-between items-center bg-white shadow-md h-[80px] px-5 lg-px-24 relative z-10"
    >
      <div class="flex items-center w-full max-w-full box-border">
        <div class="min-w-24 flex items-center">
          <img
            class="object-cover w-10 md:w-[3.5rem] pr-3 pl-5"
            src="/src/assets/img/kenema_logo.svg"
          />
          <button
            @click="$router.go(-1)"
            class="size-6 grid place-items-center"
          >
            <!-- <BaseIcon :path="mdiArrowBottomLeft" class="text-3xl rotate-45" /> -->
            <i class="text-2xl bi bi-arrow-left-short"></i>
          </button>
          <div
            class="text-4xl w-full text-black cursor-pointe mdd:hidden z-30"
            @click="menuOpen"
          >
            <i
              class="absolute left-0 text-4xl"
              :class="[open ? 'bi bi-x' : 'bi bi-list']"
            ></i>
          </div>
        </div>
        <div
          :class="[open ? 'top-0' : 'top-[-390%]']"
          class="flex flex-1 overflow-hidden flex-col md:flex-row p-5 gap-2 md:gap-4 px-10 mdd:static absolute bg-white shadow-md md:shadow-none md:w-auto my-7 md:my-0 left-0 w-full duration-500"
        >
          <nav class="border-x flex items-center px-4 overflow-auto">
            <template v-for="nav in navs" :key="nav.name">
              <RouterLink
                v-if="!nav?.hidden"
                v-privilage="nav.privileges || []"
                class="flex-shrink-0 rounded px-4 p-2"
                :to="nav.path"
                auto
              >
                {{ nav.name }}
              </RouterLink>
            </template>
          </nav>
        </div>
        <div class="flex flex-col items-center pr-5">
          <Dropdown v-slot="{ setRef, toggleDropdown }">
            <div @click="toggleDropdown" class="flex items-center gap-5">
              <img
                :src="profileImage"
                alt="profile picture"
                class="size-8 rounded-full bg-cover"
              />
              <!-- <div class="size-8 rounded-full bg-gray-400" /> -->
              <div class="pr-4">
                <p class="capitalize font-bold">{{ userName }}</p>
                <div class="flex gap-1 items-center">
                  <p class="uppercase text-xs font-bold">
                    {{ auth.auth?.user?.roleName }}
                  </p>
                  <span
                    v-if="auth.auth?.user?.branchName"
                    class="text-xs font-bold"
                    >({{ auth.auth?.user?.branchName }})</span
                  >
                </div>
              </div>
              <button>
                <BaseIcon :path="mdiChevronDown" :size="24" />
              </button>
            </div>
            <div
              class="flex shadow-lg w-48 mt-6 rounded-lg border border-gray-300 items-start text-gray-800 flex-col gap-2 bg-white"
              :ref="setRef"
            >
              <button
                @click="openModal('Profile', auth.auth?.user)"
                class="py-3 px-3 rounded-lg mt-2 hover:bg-gray-100 flex justify-start w-full duration-200"
              >
                <div class="flex gap-2 items-center rounded-lg font-bold">
                  <BaseIcon :path="mdiFaceManProfile" />
                  <span>Profile</span>
                  <!-- <Icon icon="tabler:file" class="text-sm" /> -->
                </div>
              </button>
              <button
                @click="
                  router.push('/changepassword/' + auth.auth?.user?.userUuid)
                "
                title="Edit Package"
                class="py-3 px-3 font-bold flex justify-start w-full items-center rounded-lg hover:bg-gray-100 duration-200"
              >
                <div class="flex flex-row items-center">
                  <BaseIcon :path="mdiKeyChange" />
                  <span>Change password</span>
                  <!-- <BaseIcon :path="mdiEdit" class="text-sm" /> -->
                </div>
              </button>
              <button
                @click="logout()"
                title="Edit"
                class="py-3 px-3 rounded-lg hover:bg-gray-100 flex justify-start w-full duration-200"
              >
                <div
                  class="flex flex-row items-center rounded-lg font-bold p-1"
                >
                  <BaseIcon :path="mdiLogout" />
                  <span>Logout</span>
                  <!-- <Icon icon="tabler:file" class="text-sm" /> -->
                </div>
              </button>
            </div>
          </Dropdown>
        </div>
      </div>
    </header>
    <div
      :style="{
        height: 'calc(100% - 5rem)',
      }"
      class="relative overflow-y-auto"
    >
      <RouterView />
    </div>
  </div>
</template>
