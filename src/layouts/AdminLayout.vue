<script setup lang="ts">
import BaseIcon from '@/components/base/BaseIcon.vue';
import DrawerButtonParent from '@/components/DrawerButtonParent.vue';
import Dropdown from '@/components/Dropdown.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import adminnav from '@/config/adminnav';
import { getProfileById } from '@/features/profile/api/profileApi';
import { useAuth } from '@/stores/auth';
import { openModal } from '@customizer/modal-x';
import { mdiChevronDown, mdiFaceManProfile, mdiKeyChange, mdiLogout } from '@mdi/js';
import { ref } from 'vue';
import { useRouter } from 'vue-router';


const show = ref(false);
const router = useRouter()
const auth = useAuth()
const getreq = useApiRequest()

function scrollHandler(currentPosition, position) {
	if (currentPosition >= position) {
		show.value = true;
	} else {
		show.value = false;
	}
}
function logout() {
	localStorage.clear();
	window.location.href = "/login";
}
const fetchedImage = ref(null);
const userName = ref('');
console.log(auth.auth?.user);

getreq.send(() => getProfileById(auth.auth?.user?.userUuid),
	(res) => {
		console.log(res.data);

		userName.value = res?.data?.userName
		fetchedImage.value = `data:image/png;base64,${res?.data?.imageData}`
		localStorage.setItem('fetchedImage', fetchedImage);
		console.log(res.data);

	})

const open = ref(false)

const menuOpen = () => {
	open.value = !open.value
}

</script>

<template>
	<div class="__drawer h-full w-full flex">
		<div class="w-[16.4rem] h-full overflow-hidden  flex flex-col  bg-[#ED6033] gap-8">
			<div class="max-h-12 py-8 px-6 flex flex-col gap-4">
				<div class="flex gap-3 items-center">
					<img class="object-cover w-10" src="/src/assets/img/kenema_logo.svg" />
					<p class="font-medium font-ubuntu text-lg leading-5 text-white">Kenema PMS</p>
				</div>
				<hr class=" line" />
			</div>
			<div class="overflow-auto px-3 flex-1 flex flex-col items-start gap-5 text-sm">
				<!-- <RouterLink class='group' v-for="nav in adminnav" :key="nav.name" v-privilage="nav.privileges || []" :to="nav.path"
					class="hover:bg-white hover:text-primary flex items-center gap-2 text-white rounded-md py-2">
					<i class="group-active::text-primary hover:text-primary active:color-[#ED6033] *:exact-active:text-primary" v-html="nav.icon" />
					{{ nav.name }}
				</RouterLink> -->

				<template v-for="nav in adminnav" :key="nav.name">
					<DrawerButtonParent :navs="nav" />
					<hr v-if="nav.addDivider" class="w-full border-white/15 my-1" />
				</template>
			</div>

			<Dropdown top="-470%" class="" v-slot="{ setRef, toggleDropdown }">
				<button @click="toggleDropdown"
					class="flex justify-between items-center gap-4 p-2 bg-[#FFDED7] w-full self-end">
					<div class="flex items-center gap-4 ">
						<img :src="fetchedImage" alt="profile picture" class="size-8 rounded-full  bg-cover" />
						<div class="flex flex-col">
							<p class="capitalize font-bold">{{ userName }}</p>
							<p class="uppercase text-xs font-bold">{{ auth.auth?.user?.roleName }}</p>
						</div>
					</div>
					<BaseIcon :path="mdiChevronDown" :size="24" />
				</button>

				<div class="flex shadow-lg border p-2 mt-6 rounded flex-col gap-2 w-60 bg-white" :ref="setRef">

					<button @click="openModal('Profile', auth.auth?.user)"
						class=" py-3 px-3  rounded-lg mt-2 hover:bg-gray-100 hover:w-full duration-200">
						<div class="flex gap-2 items-center rounded-lg font-bold ">
							<BaseIcon :path="mdiFaceManProfile" />
							<span>Profile</span>
							<!-- <Icon icon="tabler:file" class="text-sm" /> -->
						</div>
					</button>
					<button @click="router.push('/changepassword/' + auth.auth?.user?.userUuid)" title="Edit Package"
						class="py-3 px-3 flex font-bold hover:w-full  items-center  rounded-lg hover:bg-gray-100 duration-200">
						<div class="flex flex-row items-center">
							<BaseIcon :path="mdiKeyChange" />
							<span>Change password</span>
							<!-- <BaseIcon :path="mdiEdit" class="text-sm" /> -->
						</div>
					</button>
					<button @click="logout()" title="Edit"
						class="py-3 px-3 rounded-lg hover:bg-gray-100 hover:w-full duration-200">
						<div class="flex flex-row items-center rounded-lg font-bold p-1">
							<BaseIcon :path="mdiLogout" />
							<span>Logout</span>
							<!-- <Icon icon="tabler:file" class="text-sm" /> -->
						</div>
					</button>
				</div>
			</Dropdown>
		</div>
		<div class="w-[1197px] flex-1 min-h-full bg-gray-100 show-scrollbar overflow-auto">
			<RouterView />
		</div>
	</div>
</template>
<style>
.line {
	opacity: 24%;
	border: 1px solid white;
	linear-gradient: (to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
}

/*.__drawer .router-link-exact-active {
	@apply bg-[#FFFFFF];
	color: #ED6033;
	border-radius: 4px;
	border: 0.38px solid #FFFFFF;
	padding-top: 12.18px;
	padding-bottom: 12.18px;
	padding-left: 15.22px;
	padding-right: 15.22px;
}
	*/
</style>