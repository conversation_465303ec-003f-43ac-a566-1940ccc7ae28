<script setup>
import Button from '@components/Button.vue'
import { useRoute } from 'vue-router'
import { useApiRequest } from '@/composables/useApiRequest'
import { getInstitutionsByUuid } from '@/features/institutions/api/InstitutionApi';
import { getInstitutionCliam } from '@/features/claims/api/claimAPi';
import { allRequest, formatCurrency, secondDateFormat } from '@/utils/utils';
import { addDayToDate, formatDateToYYMMDD, FREQUENCY, instructions, routes } from "@/utils/utils";
import Table from '@components/Table.vue'
import {ref, computed} from 'vue'

const props = defineProps({
  institutionUuid: String
})

const route = useRoute()
const uuid = route.params.institutionUuid || props.institutionUuid

const from = ref(formatDateToYYMMDD(addDayToDate(new Date(), -2)))
const to = ref(formatDateToYYMMDD(addDayToDate(new Date(), 2)))
const req = useApiRequest()

req.send(
  () => allRequest({
    institution: getInstitutionsByUuid(uuid),
    claim: getInstitutionCliam({beginDate: from.value, endDate: to.value, institutionUuid: uuid})
  })
)

const totalAmount = computed(() => {
  return (req.response.value?.claim || []).reduce((sum, el) => sum += el.totalPrice ,0)
})
</script>
<template>
  <div :style="{
    height: 'calc(100% - 2rem)'
  }" class="p-4 overflow-auto show-scrollbar bg-base-clr-2 flex flex-col gap-4">
    <p v-privilage.role="'Pharmacist'" class="text-lg font-medium">Generating Claim</p>
    <div v-privilage.role="'Pharmacist'" class="flex items-center justify-between px-4 bg-base-clr-3 min-h-14">
      <p class="text-md">{{req.response.value?.institution?.institutionName}}</p>
      <div class="flex items-center gap-4">
        <p class="text-md">Outstanding Credit</p>
        <p class="text-md text-primary">ETB 345,980</p>
      </div>
      <div class="flex items-center gap-4">
        <p class="text-md">End date</p>
        <div class="text-md text-primary flex items-center gap-4">
          <div class="size-2 rounded-full bg-primary"></div>
          {{secondDateFormat(req.response.value?.claim?.[0]?.endDate)}}
        </div>
      </div>
      <p class="text-md">{{ req.response.value?.institution?.contactPersonPhone }}</p>
    </div>
    <div v-privilage.role="'Pharmacist'" class="py-4 flex items-center gap-4">
      <div class="flex-1 flex items-center gap-4">
        <div class="flex flex-col gap-1">
          <p class="text-sm">From</p>
          <input v-model="from" type="date" class="px-2 w-[15rem] h-11 rounded bg-base-clr-3"  />
        </div>
        <div class="flex flex-col gap-1">
          <p class="text-sm">To</p>
          <input v-model="to" type="date" class="px-2 w-[15rem] h-11 rounded bg-base-clr-3"  />
        </div>
        <Button type="primary" class="self-end">
          Generate
        </Button>
      </div>
      <div class="flex-1 flex gap-4 items-center">
        <div class="h-full rounded flex-1 flex flex-col gap-2 bg-base-clr-3 p-3">
          <p class="text-sm">Dispensed Drug amount</p>
          <p class="font-bold">2,345</p>
        </div>
        <div class="h-full rounded flex-1 flex flex-col gap-2 bg-base-clr-3 p-3">
          <p class="text-sm">Total Claim Amount</p>
          <p class="font-bold">{{formatCurrency(totalAmount)}}</p>
        </div>
      </div>
    </div>
    <div>
      <Table
        :headers="{
          head: ['Member Id', 'Fullname', 'Gender', 'Birtdate', 'Occupation', 'Registration Date', 'End Date', 'Status', 'Visit Date', 'Price'],
          row: ['memberId', 'fullname', 'gender', 'birthDate', 'occupation', 'registrationDate', 'endDate', 'status', 'visits', 'totalPrice']
        }"
        :cells="{
          fullname: (_, row) => {
            return `${row?.title ? row.title + ' ' : ''}${row?.firstName} ${row?.fatherName}, ${row?.grandFatherName}`
          },
          birthDate: secondDateFormat,
          registrationDate: secondDateFormat,
          endDate: secondDateFormat,
          totalPrice: formatCurrency,
          visits: (visi) => {
            return secondDateFormat(visi?.[0]?.visitDate)
          }
        }"
        :rows='req.response.value?.claim'
      />
    </div>
  </div>
</template>