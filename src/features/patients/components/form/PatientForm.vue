<script setup>
import AgeInput from "@/components/AgeInput.vue";
import Button from "@/components/Button.vue";
import DateOfBirthAgeInput from "@/features/registerar/components/DateOfBirthAgeInput.vue";
import { Gender, hospitals, Regions, Title } from "@/utils/utils";
import { Form, Input, Select, Textarea } from "@components/new_form_elements";
import { ref } from "vue";

const props = defineProps({
  petient: Object,
  addPatient: Function,
  setActive: Function,
  btnText: {
    type: String,
    require: true
  },
  pending: {
    type: Boolean,
    default: false,
  },
});

function addPatient({ values }) {
  props?.addPatient(values);
}

const Zones = [
  "Addis Ababa",
  "Amhara",
  "Benishangul-Gumuz",
  "Dire Dawa",
  "Gambela",
  "Harari",
  "Oromia",
  "Somali",
  "Southern Nations, Nationalities, and Peoples",
  "Tigray",
];

const age = ref('')
</script>
<template>
  <Form
    v-slot="{ submit }"
    id="patietn-new-form"
    class="grid grid-cols-2 gap-4 p-4"
  >
    <div class="col-span-2">
      <Input
        label="Patient Card Number / MRN"
        name="card_number"
        :value="petient?.cardNumber"
        :attributes="{
          placeholder: 'Enter Patient Card Number / MRN',
          ...(petient?.cardNumber
            ? {
                disabled: true,
              }
            : {}),
        }"
        validation="required|alpha2"
      />
      <hr class="mt-4" />
    </div>
    <Select
      label="Patient Title"
      name="title"
      :value="petient?.title"
      :options="Title"
      :attributes="{
        placeholder: 'Title',
        type: 'text',
      }"
    />
    <Input
      label="Patient first name"
      name="firstName"
      :value="petient?.firstName"
      :attributes="{
        placeholder: 'First name',
      }"
      validation="required|alpha|minmax-3,15"
    />
    <Input
      label="Patient Father's name"
      name="fatherName"
      :value="petient?.fatherName"
      :attributes="{
        placeholder: 'Father\'s name',
      }"
      validation="required|alpha|minmax-3,15"
      class="col-span-2"
    />
    <Input
      label="Patient Grand Father's name"
      name="grandFatherName"
      :value="petient?.grandFatherName"
      :attributes="{
        placeholder: 'Grand Father\s Name',
      }"
      class="col-span-2"
      validation="alpha|minmax-3,15"
    />
    <Input
      class="col-span-2"
      label="Date of Birth"
      name="birthDate"
      :value="petient?.birthDate"
      :attributes="{
        placeholder: 'Birth date',
        type: 'date',
      }"
    />
    <AgeInput
      v-model="age"
      class="col-span-2"
      label="Age (For infants use months/12 format )"
      validation="required|ageWithFraction"
      name="age"
      :value="petient?.age"
      :attributes="{
        placeholder: 'Age',
      }"
    />
    <Input
      label="Phone Number"
      validation="required|phone"
      name="mobilePhone"
      :value="petient?.mobilePhone"
      :attributes="{
        placeholder: 'Eg. **********',
      }"
    />
    <Select
      label="Gender"
      :attributes="{
        placeholder: 'Gender',
        type: 'text',
      }"
      name="gender"
      :value="petient?.gender"
      :options="Object.values(Gender)"
      validation="required"
    />
    <Select
      class=""
      label="Region / City Administration"
      name="region"
      :value="petient?.region"
      :attributes="{
        placeholder: 'Select Region',
        type: 'text',
      }"
      :options="Regions"
    />
    <Input
      label="Zone/Subcity"
      name="zone"
      :value="petient?.zone"
      :attributes="{
        placeholder: 'Zone',
      }"
    />
    <Input
      label="Woreda"
      name="woreda"
      :value="petient?.woreda"
      :attributes="{
        placeholder: 'Woreda',
      }"
    />

    <!-- <div class="grid grid-cols-2 gap-4">
      <Select
        class="flex items-center"
        label="Physician hospital"
        name="physicianHospital"
        :attributes="{
          placeholder: 'Physician hospital',
          type: 'text',
        }"
        :options="hospitals"
      />
      <Input
        label="Physician"
        name="physician"
        :attributes="{
          placeholder: 'Physician',
        }"
      />
    </div>
    <div class="grid grid-cols-5 gap-4">
      <div class="col-span-2">
        <Input
          label="Card Number"
          name="cardNumber"
          :attributes="{
            placeholder: 'Card Number',
          }"
        />
      </div>
      <div class="col-span-3">
        <Textarea
          label="Remark"
          name="remark"
          :attributes="{
            placeholder: 'Remark',
          }"
        />
      </div>
    </div> -->
    <div class="flex border-t flex-col p-2 col-span-2">
      <Button
        :pending="pending"
        @click.prevent="submit(addPatient)"
        type="primary"
      >
        {{btnText}}
      </Button>
    </div>
  </Form>
</template>
