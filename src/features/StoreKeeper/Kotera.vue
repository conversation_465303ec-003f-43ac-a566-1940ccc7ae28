<script setup>
import Input from '@/components/new_form_elements/Input.vue';
import Select from '@/components/new_form_elements/Select.vue';
import Table from '@/components/Table.vue';
import { usePagination } from '@/composables/usePagination';
import TableRowSkeleton from '@/skeletons/TableRowSkeleton.vue';
import icons from '@/utils/icons';
import { ref } from 'vue';

const pagination = usePagination({
    cb: async () => ({ data: [], success: true, error: '', status: 200 })
});

const tempData = ref([
    {
        drugName: 'Metrondazoel',
        dosageForm: 'Tablet',
        strength: 'strength',
        price: '1000',
        status: 'Available',
        quantity: '430',

    },
    {
        drugName: 'Metrondazoel',
        dosageForm: 'Tablet',
        strength: 'strength',
        price: '1000',
        status: 'Available',
        quantity: '123',


    },
    {
        drugName: 'Metrondazoel',
        dosageForm: 'Tablet',
        strength: 'strength',
        price: '1000',
        status: 'Available',
        quantity: '435',


    },
    {
        drugName: 'Metrondazoel',
        dosageForm: 'Tablet',
        strength: 'strength',
        price: '1000',
        status: 'Available',
        quantity: '234',

    },
    {
        drugName: 'Metrondazoel',
        dosageForm: 'Tablet',
        strength: 'strength',
        price: '1000',
        status: 'Available',
        quantity: '76',



    },
    {
        drugName: 'Metrondazoel',
        dosageForm: 'Tablet',
        strength: 'strength',
        price: '1000',
        status: 'Available',
        quantity: '2',


    },
    {
        drugName: 'Metrondazoel',
        dosageForm: 'Tablet',
        strength: 'strength',
        price: '1000',
        status: 'Available',
        quantity: '3',


    }
]);


</script>

<template>
    <div class="h-full w-full flex flex-col bg-white gap-9 px-[124px] mt-7 py-6">
        <div class="grid grid-cols-6 gap-9">

            <Select class="col-span-4" name="drug" label="Drug" validation="required"
                :options="['Base', 'Active', 'Category 1', 'Category 2']" :attributes="{
                    type: 'text',
                    placeholder: 'Search and Select drug',
                }">
            </Select>

            <Select name="unit" label="Unit of Measurement" validation="required"
                :options="['g', 'PAC', 'Tablet', 'PCs']" :attributes="{
                    type: 'text',
                    placeholder: 'Select Unit',
                }">
            </Select>
            <Input name="quantity" label="Quantity" validation="required" :attributes="{
                placeholder: 'Enter Quantity'
            }" />
        </div>
        <hr>
        <button type="primary"
            class=" p-4 cursor-pointer justify-center text-md flex bg-primary rounded-md text-white font-bold">
            Add Drug
            <!-- <div v-if="loginReq.pending.value">
            <Icon icon="svg-spinners:3-dots-scale" class="text-2xl" />
          </div>
          <div v-if="!loginReq.pending.value">Login</div> -->
        </button>
        <div class="rounded-lg p-4 gap-4 flex flex-1 flex-col bg-[#F1F8F7]">
            <div class="flex justify-between items-center">
                <p class="font-bold text-sm opacity-65">Added Items</p>
                <div class="w-[22rem] p-4 rounded border border-[#A4A4A4] bg-white flex items-center gap-4">
                    <i class="opacity-65" v-html="icons.search" />
                    <input v-model="pagination.search.value" class=" placeholder:opacity-65"
                        placeholder="Search Parameteres " />
                </div>
            </div>
            <div class="bg-white">
                <Table :pending="pagination.pending.value" :headers="{
                    head: [
                        'Drug Name',
                        'Dosage Form',
                        'Strength',
                        'Price',
                        'Status',
                        'Quantity',
                    ],
                    row: [
                        'drugName',
                        'dosageForm',
                        'strength',
                        'price',
                        'status',
                        'quantity',
                    ],
                }" :rows="(tempData || [])" :cells="{
                }" :Fallback="TableRowSkeleton">
                </Table>
            </div>
        </div>
    </div>
</template>