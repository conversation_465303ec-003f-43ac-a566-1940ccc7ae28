import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const api = new ApiService();
const path = "/branches";

export function getAllBranches(query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path}/all${qr}`);
}

export function addBranch(data) {
  return api.addAuthenticationHeader().post(`${path}/addBranch`, data);
}
