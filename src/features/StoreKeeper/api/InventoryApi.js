import ApiService from "@/service/ApiService";
import { getQueryFormObject, removeUndefined } from "@/utils/utils";

const api = new ApiService();
const path = "/stock/drug";
const path1 = "/stock/srv";
const path2 = "/stock/inventory";
const path3 = "/stock/store";

export function getStockReport(query) {
  return api.addAuthenticationHeader().get(`${path3}/purchase-report`, {
    params: query
  })
}

export function getLossAdjestment(query) {
  return api.addAuthenticationHeader().get(`${path2}/status/all`, {
    params: query
  })
}

export function getdrug(query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path}/all${qr}`);
}

export function getStockByBatch(id) {
  return api.addAuthenticationHeader().get(`${path3}/by-batch-number`, {
    params: {
      batchNumber: id,
    },
  });
}

export function addDrug(data) {
  return api.addAuthenticationHeader().post(`${path1}/add`, data);
}

export function getMedicineByInventryId(id) {
  return api.addAuthenticationHeader().get(`${path2}/inventory/${id}`);
}
export function updateMinandMaxById(id, query = {}, data) {
  return api
    .addAuthenticationHeader()
    .put(`${path2}/changeMinimaAndMaxima/${id}`, data, {
      params: query,
    });
}
export function updateSellingUnitPriceById(id, query = {}, data) {
  return api
    .addAuthenticationHeader()
    .put(`${path2}/changeCellingUnitPrice/${id}`, data, {
      params: query,
    });
}
export function getAllSrv(query = {}) {
  return api.addAuthenticationHeader().get(`${path1}/all`, {
    params: query,
  });
}
export function updateSrv(id, data) {
  return api.addAuthenticationHeader().put(`${path1}/edit/${id}`, data);
}

export function getAllKotera(query = {}) {
  return api.addAuthenticationHeader().get(`${path2}/allDispensaryKotera`, {
    params: query,
  });
}

export function getAllStoreDrugs(query = {}) {
  return api.addAuthenticationHeader().get(`${path3}/all`, {
    params: query,
  });
}



export function getAllDispensaryKotera(query = {}) {
  return api.addAuthenticationHeader().get(`${path3}/allStoreKotera`, {
    params: query,
  });
}

export function createNewKotera(id, query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().put(`${path2}/newKotera/${id}${qr}`);
}
export function createStoreNewKotera(id, query = {}) {
  return api.addAuthenticationHeader().put(`${path3}/new/${id}`, {}, {
    params: query
  });
}
export function getAllLowStockLevel(query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path2}/lowStockLevels${qr}`);
}
export function getAllSoonToBeExpired(query = {}) {
  const qr = getQueryFormObject(query);
  return api
    .addAuthenticationHeader()
    .get(`${path2}/soonToBeExpiredDrugs${qr}`);
}
export function getReport(query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path2}/inventory/report${qr}`);
}

export function importStockPurchase(data) {
  return api.addAuthenticationHeader().post(`${path1}/import`, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
