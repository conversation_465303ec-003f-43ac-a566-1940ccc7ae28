<script setup>
import Table from "@/components/Table.vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import { secondDateFormat } from "@/utils/utils";
import { useApprovedHistory } from "../store/ApprovedHistoryStore";
import {
  getApprovedStatus,
  getRecievedStatus,
} from "@/features/stock/api/stockApi";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { mdiMagnify } from "@mdi/js";

const approvedhistoryStore = useApprovedHistory();
const pagination = usePaginationcopy({
  store: approvedhistoryStore,
  cb: getRecievedStatus,
});
</script>

<template>
  <div class="h-full w-full flex flex-col gap-4">
    <div class="flex px-10 justify-between py-8">
      <div class="flex border rounded px-2 h-14 items-center">
        <input
          v-model="pagination.search.value"
          class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80"
          placeholder="Search Medicine "
        />
        <BaseIcon :path="mdiMagnify" :size="20" />
      </div>
    </div>
    <div class="p-10">
      <Table
        :pending="pagination.pending.value"
        :headers="{
          head: [
            'Drug Name',
            'Requested Amount',
            'Approved Amount',
            'Requested Date',
            'Approved Date',
            'Transfer Status',
            'Batch Number',
          ],
          row: [
            'drugName',
            'quantity',
            'approvedAmount',
            'requestedDate',
            'approvedDate',
            'transferStatus',
            'batchNumber',
          ],
        }"
        :rows="approvedhistoryStore.history || []"
        :cells="{
          approvedDate: secondDateFormat,
          requestedDate: secondDateFormat,
        }"
      />
    </div>
  </div>
</template>
