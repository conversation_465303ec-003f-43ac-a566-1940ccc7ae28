<script setup>
import BaseIcon from "@/components/base/BaseIcon.vue";
import Table from "@/components/Table.vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import { mdiImport, mdiMagnify, mdiPencil, mdiPlus } from "@mdi/js";
import { usePurchasesStore } from "../store/PurchasesStore";
import { getAllSrv } from "../api/InventoryApi";
import { openModal } from "@customizer/modal-x";
import { formatCurrency, secondDateFormat } from "@/utils/utils";
import Button from "@/components/Button.vue";

const purchaseStore = usePurchasesStore();
const pagination = usePaginationcopy({
  store: purchaseStore,
  cb: (params) => getAllSrv(params),
});
</script>

<template>
  <div class="h-full w-full flex flex-col gap-4 px-24 mt-5 py-6">
    <div class="flex justify-between h-14">
      <div class="flex border rounded px-2 items-center">
        <input
          v-model="pagination.search.value"
          class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80 py-10"
          placeholder="Search Medicine "
        />
        <BaseIcon :path="mdiMagnify" :size="20" />
      </div>
      <div class="flex gap-3 items-center">
        <Button
          type='edge'
          class='flex items-center gap-2'
          @click="$router.push('/additem')"
        >
          <BaseIcon :path="mdiPlus" :size="20" />
          <p class="opacity-80">Stock Receive</p>
        </Button>
        <Button @click="$router.push('/stockpurchasecenteral')" type='primary' >
          Recieve From Centeral 
        </Button>
        <Button
          type='edge'
          class='flex items-center gap-2'
          @click="openModal('ImportDrugs')"
        >
          <BaseIcon :path="mdiImport" size="lg" />
          <p>Import</p>
        </Button>
      </div>
    </div>
    <div class="">
      <Table
        :pending="pagination.pending.value"
        :headers="{
          head: [
            'Drug Name',
            'Brand Name',
            'Receiveal Date',
            'Received By',
            'Unit Price',
            'Quantity',
            'actions',
          ],
          row: [
            'drugName',
            'drugBrandName',
            'srvRecievealDate',
            'receivedBy',
            'buyingUnitPrice',
            'boughtAmount',
          ],
        }"
        :rows="purchaseStore.purchases || []"
        :cells="{
          buyingUnitPrice: (cur) => {
            return formatCurrency(cur);
          },
          srvRecievealDate: secondDateFormat,
        }"
      >
        <template #actions="{ row }">
          <div>
            <BaseIcon
              @click.prevent="openModal('EditStockPurchases', row)"
              class="text-green-500"
              :path="mdiPencil"
            />
          </div>
        </template>
      </Table>
    </div>
  </div>
</template>
