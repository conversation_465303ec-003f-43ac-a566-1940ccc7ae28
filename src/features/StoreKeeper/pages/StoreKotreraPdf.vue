<script setup>
import { useApiRequest } from "@/composables/useApiRequest";
import { useRoute } from "vue-router";
import StoreKoteraPdf from "@/components/pdf/StoreKotera.pdf.vue";
import icons from "@/utils/icons";
import { ref } from "vue";
import { getKoteraByBatch } from "../api/dispensaryKoteraApi";

const route = useRoute();
const batchUuid = route.params?.batchUuid;

const req = useApiRequest();

req.send(
  () => getKoteraByBatch(batchUuid),
  (res) => {
    if (res.success) {
      console.log("Fetched kotera data:", res.data);
    } else {
      console.error("Failed to fetch kotera data:", res.error);
    }
  }
);
</script>

<template>
  <div>
    <div v-if="req.pending.value" class="grid place-items-center h-full">
      <i v-html="icons.spinner" class="animate-spin" />
    </div>
    <StoreKoteraPdf
      v-else
      :drugs="req.response.value?.koteraList || []"
    />
  </div>
</template>
