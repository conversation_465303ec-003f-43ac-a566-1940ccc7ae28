<script setup>
import { usePagination } from "@/composables/usePagination";
import { getAllSrv } from "../api/InventoryApi";
import Table from "@/components/Table.vue";
import { formatCurrency, secondDateFormat } from "@/utils/utils";
import { openModal } from "@customizer/modal-x";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { mdiMagnify } from "@mdi/js";

const pagination = usePagination({
  cb: (params) => getAllSrv(params),
});
</script>
<template>
  <div class="h-full w-full flex flex-col gap-4 px-24 mt-5 py-6">
    <div class="flex justify-between h-14">
      <div class="flex border rounded px-2 items-center">
        <input
          v-model="pagination.search.value"
          class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80 py-10"
          placeholder="Search Medicine "
        />
        <BaseIcon :path="mdiMagnify" :size="20" />
      </div>
    </div>
		<div class="">
      <Table
        :pending="pagination.pending.value"
        :headers="{
          head: [
            'Drug Name',
            'Brand Name',
            'Receiveal Date',
            'Received By',
            'Unit Price',
            'Quantity',
            'actions',
          ],
          row: [
            'drugName',
            'drugBrandName',
            'srvRecievealDate',
            'receivedBy',
            'buyingUnitPrice',
            'boughtAmount',
          ],
        }"
        :rows="[]"
        :cells="{}"
      >
        <template #actions="{ row }">
          <div>
            <BaseIcon
              @click.prevent="openModal('EditStockPurchases', row)"
              class="text-green-500"
              :path="mdiPencil"
            />
          </div>
        </template>
      </Table>
    </div>
  </div>
</template>
