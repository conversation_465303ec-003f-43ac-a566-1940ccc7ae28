<script setup>
import Table from "@/components/Table.vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import Button from "@/components/Button.vue";
import { useRequestedStore } from "../store/RequestedStore";
import { openModal } from "@customizer/modal-x";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { mdiHistory } from "@mdi/js";
import { useRouter } from "vue-router";
import {
  BatchStatus,
  openNewTab,
  RequestOptions,
  secondDateFormat,
  toasted,
} from "@/utils/utils";
import {
  getAllRequest,
  updateRequestedBatch,
} from "@/features/stock/api/regularRequestApi";
import { ref, watch } from "vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { getAllPurchaseRequest } from "../api/purchaseRequestApi";

const props = defineProps({
  onHistoryClick: Function,
});

const reqStore = useRequestedStore();
const router = useRouter();

// Regular requests handling
const option = ref(BatchStatus.REQUESTED);
const pagination = usePaginationcopy({
  store: reqStore,
  cb: (params) =>
    getAllPurchaseRequest({
      ...params,
      status: option.value,
    }),
});

watch(option, () => {
  pagination.send();
});

const selected = ref([]);
const baseUrl = import.meta.env?.v_BASE_URL;

const issueReq = useApiRequest();
function issueBatch(row) {
  issueReq.send(
    () =>
      updateRequestedBatch(
        row?.batchUuid,
        row?.inventoryResponses?.map((el) => {
          return {
            approvedAmount: el.approvedAmount,
            toInventoryUuid: el.toInventoryUuid,
            remark: el.remark,
          };
        }),
        {
          status: BatchStatus.ISSUED,
        }
      ),
    (res) => {
      if (res.success) {
        reqStore.remove(row?.batchUuid);
      }
      toasted(res.success, "Successfully Issued", res.error);
    }
  );
}
</script>

<template>
  <div class="h-full w-full flex flex-col gap-4 pt-10 px-24">
    <div class="flex justify-end">
      <button
        v-if="props.onHistoryClick"
        @click.prevent="props.onHistoryClick()"
        class="flex gap-2 bg-[#2E7987] px-4 text-white border rounded-md h-14 items-center"
      >
        <BaseIcon :path="mdiHistory" />
        <p class="">History</p>
      </button>
    </div>
    <div>
      <Table
        @row="(row) => openNewTab(`${baseUrl}/model20/${row?.batchUuid}`)"
        v-model="selected"
        :pending="pagination.pending.value"
        :headers="{
          head: ['Request Sent Date', 'Type', 'Status', 'Actions'],
          row: ['sentDate', 'reportType', 'status'],
        }"
        :rows="reqStore.request"
        :cells="{
          status: () => option,
          sentDate: secondDateFormat,
        }"
      >
        <template #actions="{ row }">
          <div class="flex gap-2">
            <Button
              @click.stop="
                openModal(
                  option == BatchStatus.REQUESTED
                    ? 'RequestedForApproval'
                    : 'ApprovalRequest',
                  {
                    batchUuid: row?.batchUuid,
                    drugs: row?.inventoryResponses,
                    editing: option == BatchStatus.REQUESTED,
                  }
                )
              "
              type="edge"
              size="xs"
              >View</Button
            >
            <Button
              @click.stop.prevent="issueBatch(row)"
              v-if="option == BatchStatus.APPROVED"
              size="xs"
              type="secondary"
            >
              Issue
            </Button>
            <Button
              v-if="option == BatchStatus.REQUESTED"
              @click.stop="openNewTab(`${baseUrl}/model20/${row?.batchUuid}`)"
              type="secondary"
              size="xs"
              >Model20</Button
            >
            <Button
              v-if="option == BatchStatus.APPROVED"
              @click.stop="openNewTab(`${baseUrl}/model22/${row?.batchUuid}`)"
              type="secondary"
              size="xs"
              >Model22</Button
            >
          </div>
        </template>
      </Table>
    </div>
  </div>
</template>
