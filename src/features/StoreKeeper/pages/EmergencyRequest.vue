<script setup>
import Table from "@/components/Table.vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import { getRequstedTrasnsfer } from "@/features/stock/api/stockApi";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import Button from "@/components/Button.vue";
import { useRequestedStore } from "../store/RequestedStore";
import { openModal } from "@customizer/modal-x";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { mdiHistory } from "@mdi/js";
import { useRouter } from "vue-router";
import {
  BatchStatus,
  openNewTab,
  secondDateFormat,
  toasted,
} from "@/utils/utils";
import { ref } from "vue";

const props = defineProps({
  onHistoryClick: Function
});

const reqStore = useRequestedStore();
const router = useRouter();
const selected = ref([]);
const baseUrl = import.meta.env?.v_BASE_URL;

// Emergency-specific pagination
const pagination = usePaginationcopy({
  store: reqStore,
  cb: (params) => getRequstedTrasnsfer(params),
});
</script>

<template>
  <div>
    <div class="flex items-center gap-2 mb-4">
      <button
        v-if="props.onHistoryClick"
        @click.prevent="props.onHistoryClick()"
        class="flex gap-2 bg-[#2E7987] px-4 text-white border rounded-md h-14 items-center ml-auto"
      >
        <BaseIcon :path="mdiHistory" />
        <p class="">History</p>
      </button>
    </div>
    <div>
      <Table
        @row="(row) => openNewTab(`${baseUrl}/model20/${row?.batchUuid}`)"
        v-model="selected"
        :pending="pagination.pending.value"
        :headers="{
          head: ['Drug Name', 'Requested Date', 'Status', 'Quantity', 'Actions'],
          row: ['drugName', 'requestedDate', 'transferStatus', 'quantity'],
        }"
        :rows="reqStore.request"
      >
        <template #actions="{ row }">
          <div class="flex gap-2">
            <Button
              @click.stop="
                openModal('RequestedForApproval', {
                  batchUuid: row?.batchUuid,
                  drugs: row?.inventoryResponses,
                  editing: true,
                })
              "
              type="edge"
              size="xs"
              >View</Button
            >
            <Button
              @click="openNewTab(`${baseUrl}/model19/${row?.batchUuid}`)"
              type="secondary"
              size="xs"
              >Model20</Button
            >
          </div>
        </template>
      </Table>
    </div>
  </div>
</template>