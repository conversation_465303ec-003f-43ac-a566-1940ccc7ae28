import { ref } from "vue";
import { defineStore } from "pinia";

export const useTransferStore = defineStore("allTransferStores", () => {
  const stock = ref([]);

  function set(data) {
    console.log(data);
    stock.value = data;
  }

  function getAll() {
    return stock.value;
  }
  function add(data) {
    return stock.value.push(add);
  }

  function updateQuantity(id, data) {
    console.log(id, data);
    const idx = stock.value.findIndex((el) => el.batchUuid == id);

    console.log(idx);

    if (idx == -1) return;

    stock.value.splice(idx, 1, {
      ...stock.value[idx],
      ...data,
      totalAmount: (stock.value[idx].totalAmount += parseInt(
        data.approvedAmount
      )),
    });

    console.log(stock.value[idx]);
  }
  function checkExpirdity(id) {
    const idx = stock.value.findIndex((el) => el.drugUuid == id);
    if (idx === -1) return false;
    const currentDate = new Date();
    const expdate = new Date(stock.value[idx].expDate);
    if (expdate < currentDate) {
      return expdate < currentDate;
    }
  }
  function update(id, data) {
    const idx = stock.value.findIndex((el) => el.batchUuid == id);
    if (idx == -1) return;

    stock.value.splice(idx, 1, data);
  }

  function remove(id) {
    const idx = stock.value.findIndex((el) => el.batchUuid == id);
    if (idx == -1) return;

    stock.value.splice(idx, 1);
  }

  return {
    stock,
    getAll,
    updateQuantity,
    checkExpirdity,
    update,
    remove,
    set,
    add,
  };
});
