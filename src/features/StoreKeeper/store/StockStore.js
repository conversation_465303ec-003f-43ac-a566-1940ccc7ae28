import { ref } from "vue";
import { defineStore } from "pinia";
import { addMonths, isBefore } from "date-fns";
export const useStockStore = defineStore("allStocStores", () => {
  const stock = ref([]);

  function set(data) {
    stock.value = data;
  }

  function getAll() {
    return stock.value;
  }
  function add(data) {
    return stock.value.push(data);
  }

  function updateQuantity(id, data) {
    const idx = stock.value.findIndex((el) => el.inventoryUuid == id);
    if (idx == -1) return;

    console.log(idx);
    stock.value[idx] = {
      ...stock.value[idx],
      ...data,
      totalAmount: (stock.value[idx].totalAmount += parseInt(
        data.approvedAmount
      )),
    };
  }

  function updateUnitPrice(id, data) {
    console.log(data);

    const idx = stock.value.findIndex((el) => el.inventoryUuid == id);
    if (idx == -1) return;

    stock.value.splice(idx, 1, {
      ...stock.value[idx],
      ...data,
      sellingUnitPrice: data.cellingUnitPrice, // Update the selling unit price directly
    });
  }

  function checkExpirdity(id) {
    const idx = stock.value.findIndex((el) => el.drugUuid == id);
    if (idx === -1) return false;

    // Get the expiry date and current date
    const expdate = new Date(stock.value[idx].expDate);
    const currentDate = new Date();

    // Reset time parts to compare just the dates
    // Calculate date 6 months from now
    const sixMonthsFromNow = addMonths(currentDate, 6)
    
    return isBefore(expdate, sixMonthsFromNow);
  }

  function update(id, data) {
    const idx = stock.value.findIndex((el) => el.inventoryUuid == id);
    if (idx == -1) return;

    stock.value.splice(idx, 1, data);
  }

  return {
    stock,
    getAll,
    updateQuantity,
    updateUnitPrice,
    checkExpirdity,
    update,
    //remove,
    set,
    add,
  };
});
