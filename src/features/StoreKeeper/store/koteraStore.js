import { ref } from "vue";
import { defineStore } from "pinia";

export const useKoteraStore = defineStore("allKoteraStores", () => {
  const kotera = ref([]);

  function set(data) {
    console.log(data);
    kotera.value = data;
  }

  function getAll() {
    return kotera.value;
  }
  function add(data) {
    return kotera.value.push(data);
  }

  function update(id, data) {
    const idx = kotera.value.findIndex((el) => el.inventoryUuid == id);
    if (idx == -1) return;

    kotera.value.splice(idx, 1, data);
  }

  function updateAmount(id, data) {
    const idx = kotera.value.findIndex((el) => el.inventoryUuid == id);
    if (idx == -1) return;

    console.log(idx);
    kotera.value[idx] = {
      ...kotera.value[idx],
      ...data,
      countedQuantity: data.countedQuantity,
      round: data.round,
    };

    console.log(kotera.value[idx]);
  }

  // function updateReady(id,data) {
  // 	const idx = kotera.value.findIndex((el) => el.prescriptionUuid == id);
  //   if (idx == -1) return;
  // 	kotera.value[idx] = {
  // 		...kotera.value[idx],
  //     ...data,
  // 	}
  // }

  return {
    kotera,
    getAll,
    update,
    set,
    add,
    updateAmount,
  };
});
