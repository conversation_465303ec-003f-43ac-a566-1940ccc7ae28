import { ref } from "vue";
import { defineStore } from "pinia";

export const useKoteraHistoryStore = defineStore("koteraHistoryStore", () => {
  const history = ref([]);

  function set(data) {
    console.log(data);
    history.value = data;
  }

  function getAll() {
    return history.value;
  }
  
  function add(data) {
    return history.value.push(data);
  }

  function update(id, data) {
    const idx = history.value.findIndex((el) => el.koteraHistoryUuid == id);
    if (idx == -1) return;

    history.value.splice(idx, 1, data);
  }

  return {
    history,
    getAll,
    update,
    set,
    add,
  };
});
