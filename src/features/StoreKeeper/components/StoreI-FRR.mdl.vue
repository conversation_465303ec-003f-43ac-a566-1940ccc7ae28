<script setup>
import Button from "@/components/Button.vue";
import NewFormParent from "@/features/admin/role/components/NewFormParent.vue";
import { useStockStore } from "@/features/StoreKeeper/store/StockStore";
import Form from "@/new_form_builder/Form.vue";
import icons from "@/utils/icons";
import {
  BatchStatus,
  formatCurrency,
  openNewTab,
  toasted,
} from "@/utils/utils";
import { useApiRequest } from "@/composables/useApiRequest";
import { closeModal, openModal } from "@customizer/modal-x";
import { onMounted, ref, watch } from "vue";
import { usePagination } from "@/composables/usePagination";
import { updateRequestedBatch } from "@/features/stock/api/regularRequestApi";
import { getStockReport } from "../api/InventoryApi";
import { purchaseRequest } from "../api/purchaseRequestApi";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const storeReq = useApiRequest();

storeReq.send(
  () => getStockReport({ limit: 3000, page: 1 }),
  (res) => {}
);
// onMounted(() => {
//   const els = document.querySelectorAll('td:has(input[class*=bg-green])')
//   const next = document.querySelectorAll('td:has(input[class*=bg-green] + td)')

//   for(let i = 0; i < els.length; i++) {
//     drugs.value[i].approvedAmount = next[i].innerHTML
//     console.log(props.data);
//   }
// });

const submitRegularReq = useApiRequest();
function drugrequest() {
  if (submitRegularReq.pending.value) return;

  submitRegularReq.send(
    () =>
      purchaseRequest(
        storeReq.response.value?.content?.map?.((el) => {
          return {
            beginningBalance: el.storeResponse.beginningBalance,
            endingBalance: el.dispensaryAmount + el.storeResponse.totalAmount,
            quantityReceived: el.storeResponse.quantityReceived,
            lossAndAdjustment: el.storeResponse.loss,
            storeUuid: el.storeResponse.storeUuid,
          };
        })
      ),
    (res) => {
      if (res.error) {
        toasted(false, "", res.error);
      }

      if (res.success) {
        openNewTab(
          `${import.meta.env?.v_BASE_URL}/model20/${props.data}`
        );
        closeModal();

      }
    }
  );
}
</script>
<template>
  <div
    @click.stop="() => {}"
    class="bg-black/50 h-55 p-10 min-h-full w-full grid place-items-center"
  >
    <NewFormParent size="xl" title="Request Store">
      <template #left-actions>
        <i class="text-black" v-html="icons.leftArrow" />
      </template>
      <Form v-slot="{ submit }" id="quantity" class="flex flex-col gap-4">
        <div class="flex items-center justify-between">
          <p>List of Requests</p>
          <Button
            :pending="storeReq.pending.value"
            @click.prevent="submit(drugrequest)"
            type="primary"
          >
            Submit
          </Button>
        </div>
        <table class="border rounded-md">
          <thead>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th class="max-w-12" rowspan="3">Ser. No.</th>
              <th rowspan="3">Item</th>
              <th rowspan="3">Unit</th>
              <th colspan="6">Completed By Unit</th>
              <th rowspan="2" colspan="3">Ending Balance</th>
            </tr>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th colspan="4">Beginning Balance</th>
              <th>Quantity Received</th>
              <th>Loss/Adjustment</th>
            </tr>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th colspan="4">A</th>
              <th>B</th>
              <th>C</th>
              <th>D-Qty</th>
              <th>ST-QTY</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            <template v-if="storeReq.response.value?.content?.length">
              <tr
                class="*:text-center *:border text-sm *:p-1"
                v-for="(
                  { storeResponse: item, dispensaryAmount }, idx
                ) in storeReq.response.value?.content || []"
                :key="item.drugName"
              >
                <td class="max-w-12">{{ idx + 1 }}</td>
                <td>{{ item.drugName }}</td>
                <td>{{ item?.dispensingUnit }}</td>
                <td colspan="4">
                  {{ item?.beginningBalance }}
                </td>
                <td>0</td>
                <td>0</td>
                <td>
                  <input
                    key="two"
                    :value="dispensaryAmount"
                    class="text-center w-12"
                  />
                </td>
                <td>
                  <input
                    key="two"
                    :value="item.totalAmount"
                    class="text-center w-12"
                  />
                </td>
                <td>
                  <input
                    key="two"
                    :value="dispensaryAmount + item.totalAmount"
                    class="text-center w-12"
                  />
                </td>
              </tr>
            </template>
            <tr v-else>
              <td colspan="100%" clss="p-4">
                <div class="flex flex-col items-center justify-center">
                  <i class="*:size-56" v-html="icons.no_data" />
                  No Data
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </Form>
    </NewFormParent>
  </div>
</template>
