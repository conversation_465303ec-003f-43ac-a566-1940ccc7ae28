<script setup>
import Button from "@/components/Button.vue";
import NewFormParent from "@/features/admin/role/components/NewFormParent.vue";
import { useStockStore } from "@/features/StoreKeeper/store/StockStore";
import Form from "@/new_form_builder/Form.vue";
import icons from "@/utils/icons";
import {
  BatchStatus,
  formatCurrency,
  openNewTab,
  toasted,
} from "@/utils/utils";
import { useApiRequest } from "@/composables/useApiRequest";
import { closeModal, openModal } from "@customizer/modal-x";
import { ref, watch } from "vue";
import { usePagination } from "@/composables/usePagination";
import { updateRequestedBatch } from "@/features/stock/api/regularRequestApi";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const drugs = ref(
  [...props.data?.drugs].map((el) => {
    return {
      ...el,
      approvedAmount:
        (el.totalAmount + (0 + 0) - el.quantity) * 2 - el.quantity,
    };
  })
);

const approveReq = useApiRequest();
function processAmount() {
  if (approveReq.pending.value) return;

  approveReq.send(
    () =>
      updateRequestedBatch(
        props.data?.batchUuid,
        drugs.value?.map((el) => ({
          approvedAmount: el.approvedAmount,
          toInventoryUuid: el.toInventoryUuid,
          remark: el.remark,
        })),
        { status: BatchStatus.PROCESSED }
      ),
    (res) => {
      if (res.error) {
        toasted(false, "", res.error);
      }

      if (res.success) {
        closeModal();
        openNewTab(
          `${import.meta.env?.v_BASE_URL}/emergency-model20/${props.data?.batchUuid}`
        );
      }
    }
  );
}
</script>
<template>
  <div
    @click.stop="() => {}"
    class="bg-black/50 h-55 p-10 min-h-full w-full grid place-items-center"
  >
    <NewFormParent size="xl" title="Emergency Request Store">
      <template #left-actions>
        <i class="text-black" v-html="icons.leftArrow" />
      </template>
      <Form v-slot="{ submit }" id="quantity" class="flex flex-col gap-4">
        <div class="flex items-center justify-between">
          <p>List of Requests</p>
          <Button
            v-if="data?.editing ?? true"
            :pending="approveReq.pending.value"
            @click.prevent="submit(processAmount)"
            type="primary"
          >
            Submit
          </Button>
        </div>
        <table class="border rounded-md">
          <thead>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th class="max-w-12" rowspan="3">Ser. No.</th>
              <th rowspan="3">Item</th>
              <th rowspan="3">Unit</th>
              <th colspan="7">Completed By Unit</th>
              <th colspan="3">Completed By Store</th>
              <th rowspan="2" colspan="2">Qty to be Supllied</th>
              <th rowspan="2">Remark</th>
            </tr>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th colspan="4">Beginning Balance</th>
              <th>Quantity Received</th>
              <th>Loss/Adjustment</th>
              <th>Ending Balance</th>
              <th>Calculated Consumption E=A+B+-C-D</th>
              <th>Max F=E*2</th>
              <th>Qty to Max G=F-D</th>
            </tr>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th colspan="4">A</th>
              <th>B</th>
              <th>C</th>
              <th>D</th>
              <th>E</th>
              <th>F</th>
              <th>G</th>
              <th>H</th>
              <th>SOH</th>
              <th>I</th>
            </tr>
          </thead>
          <tbody>
            <template v-if="drugs.length > 0">
              <tr
                class="*:text-center *:border text-sm *:p-1"
                v-for="(item, idx) in drugs || []"
                :key="item.drugName"
              >
                <td class="max-w-12">{{ idx + 1 }}</td>
                <td>{{ item.drugName }}</td>
                <td>{{ item?.unit }}</td>
                <td colspan="4">
                  {{ item.totalAmount }}
                </td>
                <td>0</td>
                <td>0</td>
                <td>
                  {{ item.quantity }}
                </td>
                <td>0</td>
                <td>
                  0
                </td>
                <td>
                  0
                </td>
                <td>
                  <input
										disabled
                    :class="[
                      item.approvedAmount > item?.storeTotalAmount
                        ? 'bg-red-500'
                        : 'bg-green-500',
                    ]"
                    v-bind="data?.editing === false ? { disabled: true } : {}"
                    class="rounded text-white max-w-max w-12 text-center"
                    :value='item.quantity'
                  />
                </td>
                <td>{{ item?.storeTotalAmount }}</td>
                <td>
                  <input
										disabled
                    v-bind="data?.editing === false ? { disabled: true } : {}"
                    v-model="item.remark"
                    class="max-w-max w-12 text-center"
                    placeholder="remark"
                  />
                </td>
              </tr>
            </template>
            <tr v-else>
              <td colspan="17" clss="p-4">
                <div class="flex flex-col items-center justify-center">
                  <i class="*:size-56" v-html="icons.no_data" />
                  No Data
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </Form>
    </NewFormParent>
  </div>
</template>
