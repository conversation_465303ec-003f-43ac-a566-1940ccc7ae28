<script setup lang="ts">
import FormSubmitButton from "@/components/FormSubmitButton.vue";
import Input from "@/components/new_form_elements/Input.vue";
import NewFormParent from "@/features/admin/role/components/NewFormParent.vue";
import Form from "@/new_form_builder/Form.vue";
import { useForm } from "@/new_form_builder/useForm";
import { data } from "autoprefixer";
import { useMedicines } from "../store/IventoryStore";
import { useApiRequest } from "@/composables/useApiRequest";
import { addDrug } from "../api/InventoryApi";
import { toasted } from "@/utils/utils";
import { mdiAbTesting } from "@mdi/js";
import { computed, ref, toValue } from "vue";
import Select from "@/components/new_form_elements/Select.vue";
import { closeModal } from "@customizer/modal-x";
import Button from "@/components/Button.vue";
import SearchSelect from "@/components/SearchSelect.vue";
import DrugRecieveForm from "./DrugRecieveForm.vue";

const props = defineProps({
  data: Object,
});

const medicinesStore = useMedicines();
const req = useApiRequest();

function drugsAdd(values) {
  console.log("For Compounding:", values?.forCompounding);

  req.send(
    () =>
      addDrug({
        ...values,
        drugUuid: props.data.drugUuid,
        forCompounding: values?.forCompounding,
      }),
    (res) => {
      if (res.success) {
        console.log(res);
        medicinesStore.updateQuantity(props.data?.id, values);
        closeModal();
      }
      toasted(res.success, "succesfully added", res.error);
    }
  );
}

</script>

<template>
  <div class="bg-black/50 h-55 p-10 min-h-full grid place-items-center">
    <div class="h-55">
      <NewFormParent size="lg" title="Add Item">
        <DrugRecieveForm 
          :pending="req.pending.value"
          :drug="props.data"
          :onSubmit="drugsAdd"
        />
      </NewFormParent>
    </div>
  </div>
</template>
