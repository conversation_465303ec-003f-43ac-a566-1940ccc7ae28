<script setup>
import { secondDateFormat } from '@/utils/utils';
import { useMedicines } from '../store/IventoryStore';

const props = defineProps({
    row: Object
})
const medicinesStore = useMedicines()

const isExpired = medicinesStore.checkExpirdity(props.row?.drugUuid);
</script>
<template>
    <span :style="{ color: isExpired ? 'red' : 'Green' }">
        {{ secondDateFormat(row?.expDate) }}
    </span>
</template>