<script setup lang="ts">
import InputParent from "@/new_form_builder/InputParent.vue";
import { ref, watch } from "vue";

const props = defineProps({
  modelValue: {
    // this allows v-model binding
    type: [Object, Number, Array, String],
  },
  value: {
    type: [Number, String],
  },
  name: {
    type: String,
    requried: true,
  },
});

const emit = defineEmits(["update:modelValue"]);

const value = ref(props.value ?? props.modelValue ?? 0);

console.log(value.value);
// const value = ref({
// 	toInventoryUuid: props.modelValue?.inventoryUuid,
// 	quantity: 0,
// })

// Watch for changes and emit updates
watch(value, (newValue) => {
  emit("update:modelValue", newValue);
});
</script>

<template>
  <InputParent :name="name" v-model="value" v-slot="{ setRef }">
    <div>
      <input
        :ref="setRef"
        class="field w-full bg-transparent outline-none border-none placeholder-gray-400"
      />
    </div>
  </InputParent>
</template>

<style scoped>
.field ::placeholder {
  padding-left: 0.5rem;
}
</style>
