<script setup>
import Input from "@/components/new_form_elements/Input.vue";
import Select from "@/components/new_form_elements/Select.vue";
import Table from "@/components/Table.vue";
import { usePagination } from "@/composables/usePagination";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import icons from "@/utils/icons";
import { computed, ref, watch } from "vue";
import KoteraTableRow from "./components/KoteraTableRow.vue";
import ActualQuantityInput from "./components/ActualQuantityInput.vue";
import Button from "@/components/Button.vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import { useKoteraStore } from "./store/koteraStore";
import { getAllKotera } from "./api/InventoryApi";
import { useApiRequest } from "@/composables/useApiRequest";
import { removeUndefined } from "@/utils/utils";
import Form from "@/new_form_builder/Form.vue";
import { openModal } from "@customizer/modal-x";
import { useRouter } from "vue-router";

const router = useRouter();
const currentYear = new Date().getFullYear();
const search = ref("");
const selectedYear = ref(currentYear);

const koteraStore = useKoteraStore();
const pagination = usePaginationcopy({
  store: koteraStore,
  cb: (data) =>
    getAllKotera({
      ...data,
      search: search.value,
      round: 1,
      year: selectedYear.value,
    }),
});

const rounds = ref("");

const years = computed(() => {
  const startYear = 1900;
  const yearList = [];
  for (let year = currentYear; year >= startYear; year--) {
    yearList.push(year);
  }
  return yearList;
});

const req = useApiRequest();

function fetchDrugs() {
  pagination.send();
}
</script>

<template>
  <div
    class="min-h-full w-full flex flex-col bg-white gap-9 px-[124px] mt-7 py-6"
  >
    <div class="flex items-end justify-between gap-2">
      <Form id="kotera" class="grid grid-cols-6 gap-9">
        <div class="col-span-4">
          <Input
            name="search"
            v-model="search"
            label="Search"
            :attributes="{
              placeholder: 'Serach Drug',
            }"
          />
        </div>
        <Button
          size="sm"
          @click.prevent="fetchDrugs"
          type="primary"
          class="self-end"
        >
          <div v-if="req.pending.value">
            <i v-html="icons.spinner" />
          </div>
          <div v-if="!req.pending.value">Search Drug</div>
        </Button>
      </Form>
      <div class="pl-4 border-l" >
        <Button type="edge" @click="router.push('/dispensary-kotera-history')">History</Button>
      </div>
    </div>
    <hr />
    <div class="rounded-lg p-4 gap-4 flex flex-1 flex-col bg-[#F8F8F8]">
      <div class="flex justify-between items-center">
        <p class="font-bold text-sm opacity-65">Added Items</p>
        <div
          class="w-[22rem] p-4 rounded border border-[#A4A4A4] bg-white flex items-center gap-4"
        >
          <i class="opacity-65" v-html="icons.search" />
          <input
            class="placeholder:opacity-65"
            placeholder="Search Added Items "
          />
        </div>
      </div>
      <div class="bg-white">
        <Table
          v-model="rounds"
          :lastCol="true"
          :pending="pagination.pending.value"
          :headers="{
            head: [
              'Drug Name',
              'Brand Name',
              'Dosage Form',
              'Drug Unit',
              'Price',
              'Quantity',
              'Actual Count',
            ],
            row: [
              'drugName',
              'drugBrandName',
              'dosageForm',
              'drugUnit',
              'sellingUnitPrice',
              'totalAmount',
            ],
          }"
          :rows="koteraStore.kotera || []"
          :cells="{
            strength: (_, row) => {
              return row?.strength
                ? `${row.strength} ${row.unit}`
                : `${row.strength || 'No Strength'}`;
            },
            totalAmount: (_, row) => {
              return `${row?.totalAmount ?? ''} ${row?.drugUnit ?? ''}`;
            },
          }"
          :Fallback="TableRowSkeleton"
          :rowCom="KoteraTableRow"
        >
          <template #headerLast>
            <p class="capitalize text-center">Balance</p>
          </template>
          <template #lastCol="{ row }"> </template>
        </Table>
      </div>
    </div>
    <Button
      @click.prevent="openModal('DispensingGenerateReport', koteraStore.kotera)"
      type="primary"
      class="w-full"
      >Generate and Report Kotera</Button
    >
  </div>
</template>
