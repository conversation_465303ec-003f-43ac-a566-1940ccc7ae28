import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const api = new ApiService();
const path = "/stock/inventory";
const path1 = "/compounds";

export function checkMedicineAvailability(query = {}) {
  const qr = getQueryFormObject(query);
  return api
    .addAuthenticationHeader()
    .get(`${path}/pharmacyCompoundingInventory${qr}`);
}
export function addCompounding(data, config = {}) {
  return api.addAuthenticationHeader().post(`${path1}/createCompounding`, data);
}

export function changeCompoundingPrescription(id, data) {
  return api.addAuthenticationHeader().put(`${path1}/update/${id}`, data);
}

export function changeCompoundingPrescriptionBystatus(id, query = {}, data) {
  const qr = getQueryFormObject(query);
  return api
    .addAuthenticationHeader()
    .put(`${path1}/update-status/${id}${qr}`, data);
}
