<script setup>
import { useApiRequest } from "@/composables/useApiRequest";
import { ref } from "vue";
import Table from "@/components/Table.vue";
import Button from "@/components/Button.vue";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import { formatAgeToFraction, getAgeFormDate, secondDateFormat } from "@/utils/utils";
import { searchForPatient } from "@/features/paper_prescription/api/patientApi";
import { useCompoundingStore } from "../../store/compoundingStore";

const compoundingStore = useCompoundingStore();

const props = defineProps({
  setPatient: Function,
  setActive: Function,
});

const patientReq = useApiRequest();
const search = ref("");

function getPatient(ev) {
  patientReq.send(
    () => searchForPatient({ search: search.value }),
    (res) => {
      console.log(res);
    },
    true
  );
}

function patientSelected(patient) {
  props.setPatient(patient);
}
</script>
<template>
  <div class="bg-base-clr-2 px-7 py-8 flex flex-col gap-2 flex-1">
    <div
      class="flex justify-between gap-2 items-center px-2 rounded border-primary border-2 min-h-drug-search-height"
    >
      <input
        v-focus
        v-model="search"
        @keydown="getPatient"
        class="px-2 bg-transparent flex-1 h-12"
        placeholder="Search Patient (MRN, NAME, FAYDA ID, INSURANCE ID)"
      />
      <Button @click="getPatient" size="md" type="primary"> Search </Button>
    </div>
    <div
      v-if="
        !patientReq.pending.value &&
        !patientReq.response.value?.length &&
        !compoundingStore.selectedPatient
      "
      class="flex-1 p-4 bg-base-clr-1 flex flex-col gap-4 items-center"
    >
      <img class="size-52" src="/src/assets/img/search_med.svg" />
      <div class="flex flex-col gap-4 items-center">
        <p class="font-medium text-md">Please search for a Patient</p>
      </div>
    </div>
    <div v-else>
      <Table
        @row="patientSelected"
        :pending="patientReq.pending.value"
        :Fallback="TableRowSkeleton"
        :headers="{
          head: ['MRN', 'Full Name', 'Phone number', 'Age', 'Gender'],
          row: ['cardNumber', 'fullname', 'mobilePhone', 'birthDate', 'gender'],
        }"
        :rows="
          patientReq.response.value ||
          (compoundingStore.selectedPatient
            ? [compoundingStore.selectedPatient]
            : [])
        "
        :cells="{
          age: formatAgeToFraction,
          fullname: (_, row) => {
            return `${row?.title ?? ''} ${row?.firstName ?? ''} ${
              row?.fatherName ?? ''
            } ${row?.grandFatherName ?? ''}`;
          },
          birthDate: (date) => {
            return getAgeFormDate(date);
          },
        }"
      />
    </div>
  </div>
</template>
