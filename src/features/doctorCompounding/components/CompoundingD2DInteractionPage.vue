<script setup>
import Icon from '@components/Icon.vue'
import { ref, watch, computed } from 'vue'
import D2DInteraction from '@/features/dispense/components/D2DInteraction.vue';
import { useCompoundingStore } from '../store/compoundingStore';

const compoundingStore = useCompoundingStore();

const props = defineProps({
    goBack: Function,
});
</script>
<template>
    <div class="w-full h-full bg-base-clr-2 flex flex-col">
        <div class="h-drug-search-height flex items-center border-b">
            <button @click="goBack()" class="size-12 grid place-items-center">
                <Icon name="solar:round-arrow-left-outline" />
            </button>
            <p>Drug To Drug Interaction</p>
        </div>
        <div class="p-4 flex-1 overflow-auto show-scrollbar">
            <D2DInteraction v-if="compoundingStore.selectedDrugList?.length > 1" :id="compoundingStore.selectedDrugList
                .map(el => el.drugName?.trim())" />
            <p v-else class="p-4 text-center text-sm">
                You Need To Selecte More Than One Drug
            </p>
        </div>
    </div>
</template>
