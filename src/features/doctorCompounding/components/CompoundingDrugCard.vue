<script setup>
import Button from "@components/Button.vue";
import Icon from "@/components/Icon.vue";
import { formatCurrency } from "@/utils/utils";
import Input from "@/components/new_form_elements/Input.vue";
import { ref } from "vue";
import Form from "@/new_form_builder/Form.vue";
import Select from "@/components/new_form_elements/Select.vue";
const props = defineProps({
  num: Number,
  drug: Object,
  detail: Object,
  active: {
    type: [String, Boolean, Number],
    default: false,
  },
  remove: {
    type: Boolean,
    default: true,
  },
  onRemove: {
    type: Function,
    default: (f) => f,
  },
  showDetail: {
    type: Boolean,
    default: true,
  },
});
console.log(props.drug);
const value = ref(50);
</script>
<template>
  <div class="flex flex-col gap-3 w-full">
    <Button
      :class="[
        active == drug.inventoryUuid
          ? 'bg-base-clr-3 border-primary'
          : 'border-white/0 ',
      ]"
      class="flex flex-shrink-0 border-b w-full !p-2 transition-all border duration-75 justify-start gap-3 cursor-pointer bg-white"
    >
      <div class="">
        {{ num || "" }}
      </div>
      <div v-if="drug" class="w-full flex justify-between">
        <div class="flex-1 flex text-left flex-col items-start">
          <p>{{ `${drug.drugName}` }}</p>
          <p class="text-primary text-xs">{{ ` ${drug.category}` }}</p>
          <div class="px-4 mt-2">
            <ul class="list-disc text-xs flex flex-col gap-1">
              <li><strong>Quantity:</strong> {{ drug.percentage }}</li>
              <!-- <li><strong>Weight:</strong> {{ drug.quantity }}</li> -->
              <li><strong>Unit:</strong> {{ drug.unit }}</li>
              <li><strong>Unit Price:</strong> {{ drug.sellingUnitPrice }}</li>
            </ul>
          </div>
          <div class="w-full grid mt-4">
            <input
              type="range"
              class="appearance-none bg-gray-300 h-0.5 rounded-full outline-none"
              min="0"
              max="0"
            />
          </div>

          <Form
            id="list-form"
            class="w-full"
            v-if="drug.quantity || drug.percentage"
          >
            <div class="mt-2">
              <label for="weight" class="block text-sm">
                Quantity <span class="text-red-500">*</span>
              </label>
              <Input
                v-model="drug.percentage"
                name="percentage"
                validation="required"
                :attributes="{ placeholder: 'Enter Percentage' }"
              >
                <!-- <template #right> % </template> -->
              </Input>
            </div>
            <div
              v-if="drug.quantity && drug.type == 'Base'"
              class="mt-2 flex gap-3"
            >
              <div>
                <label for="quantity" class="block text-sm">
                  Weight <span class="text-red-500">*</span>
                </label>
                <Input
                  v-model="drug.quantity"
                  name="quantity"
                  validation="required"
                  :attributes="{ placeholder: 'Enter Quantity' }"
                >
                </Input>
              </div>
              <!-- <Select
                class="w-[4rem]"
                v-model="drug.unit"
                name="unit"
                label="Unit"
                validation="required"
                :options="['g', 'mg', 'ml']"
                :attributes="{
                  type: 'text',
                  placeholder: 'unit',
                }"
              ></Select> -->
            </div>
          </Form>

          <div class="w-full mt-2 mb-2">
            <hr class="bg-gray-300" />
          </div>
        </div>
        <div class="">
          <button v-if="remove" @click.stop="onRemove(drug.inventoryUuid)">
            <Icon
              :color="'rgb(var(--error))'"
              name="solar:trash-bin-trash-outline"
            />
          </button>
        </div>
      </div>
    </Button>
    <!-- <Button
            class="flex flex-col flex-shrink-0 border-b !p-2 transition-all border duration-75 justify-start gap-3 cursor-pointer font-bold border-primary">
            <div v-if="!detail && showDetail" class="italic text-xs mt-2 font-normal flex items-center">
                <p class="font-bold">Total Amount</p>
                <span>.</span> No Details Added
            </div>
            <div v-else-if="showDetail">
                <p>Total Price: {{ (drug?.sellingUnitPrice || 0) * (detail?.drug?.totalQuantity || 0) }}</p>

            </div>

        </Button> -->
  </div>
</template>
<style scoped>
input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 6px;
  height: 6px;
  background: gray;
  border-radius: 50%;
  cursor: pointer;
  margin-top: -2px;
}

/* input[type="range"]::-moz-range-thumb {
    width: 7px;
    height: 7px;
    background: gray;
    border-radius: 50%;
    cursor: pointer;
} */
</style>
