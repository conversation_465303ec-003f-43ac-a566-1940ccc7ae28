<script setup>
import <PERSON>ton from "@/components/Button.vue";
import Input from "@/components/new_form_elements/Input.vue";
import Icon from "@/components/Icon.vue";
import { ref } from "vue";
import Form from "@/new_form_builder/Form.vue";
import Select from "@/components/new_form_elements/Select.vue";
import { useCompoundingStore } from "../store/compoundingStore";

const props = defineProps({
  drug: Object,
  active: {
    type: [String, Boolean, Number],
    default: false,
  },
  remove: {
    type: Boolean,
    default: true,
  },
  onRemove: {
    type: Function,
    default: (f) => f,
  },
  num: Number,
});
const value = ref(50);
console.log(props?.drug);

const compoundingStore = useCompoundingStore();
const updateValues = () => {
  compoundingStore.addToList({ ...props.drug });
};
</script>
<template>
  <div class="flex flex-col gap-6">
    <div
      class="flex flex-shrink-0 gap-6 rounded-md shadow !p-2 transition-all duration-75 justify-start cursor-pointer bg-white"
    >
      <div class="">
        {{ num || "" }}
      </div>
      <div class="w-full flex justify-between items-center gap-4">
        <!-- Left Side: Ingredient Details & Inputs -->
        <div class="flex-1 flex flex-col gap-2">
          <!-- Name and Category in a Row -->
          <div class="flex justify-between items-center w-full">
            <div class="text-left">
              <p class="font-medium">{{ props.drug?.drugName }}</p>
              <p class="text-xs text-primary">{{ props.drug?.category }}</p>
            </div>

            <!-- Delete Button: Aligned to the right of ingredient name -->
            <button v-if="remove" @click.stop="onRemove(drug.inventoryUuid)">
              <Icon
                :color="'rgb(var(--error))'"
                name="solar:trash-bin-trash-outline"
              />
            </button>
          </div>

          <div class="grid place-items-center w-3/4">
            <input
              type="range"
              class="appearance-none bg-gray-300 h-0.5 rounded-full outline-none"
              min="0"
              max="0"
              v-model="value"
            />
          </div>

          <div @click.stop="() => {}" class="w-full flex gap-3 mt-2">
            <template v-if="props.drug?.type === 'Active'">
              <div>
                <label for="percentage" class="text-left block text-sm">
                  Qunatity <span class="text-red-500">*</span>
                </label>
                <Input
                  v-model="drug.percentage"
                  @input="updateValues"
                  validation="required"
                  name="percentage"
                  :attributes="{
                    placeholder: 'Enter Qunatity ',
                  }"
                >
                </Input>
              </div>
            </template>
            <template v-else-if="props.drug?.type === 'Base'">
              <div>
                <label for="quantity" class="text-left block text-sm">
                  Quantity <span class="text-red-500">*</span>
                </label>
                <Input
                  v-model="drug.percentage"
                  @input="updateValues"
                  validation="required"
                  name="quantity"
                  :attributes="{
                    placeholder: 'Enter weight',
                  }"
                >
                </Input>
              </div>
              <!-- <Select
                class="w-[4rem]"
                v-model="drug.unit"
                @input="updateValues"
                name="unit"
                label="Unit"
                validation="required"
                :options="['g', 'mg', 'ml']"
                :attributes="{
                  type: 'text',
                  placeholder: 'unit',
                }"
              ></Select> -->
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 6px;
  height: 6px;
  background: gray;
  border-radius: 50%;
  cursor: pointer;
  margin-top: -2px;
}

/* input[type="range"]::-moz-range-thumb {
    width: 7px;
    height: 7px;
    background: gray;
    border-radius: 50%;
    cursor: pointer;
} */
</style>
