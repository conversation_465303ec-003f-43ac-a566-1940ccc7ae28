<script setup>
import Table from "@components/Table.vue";
import { usePagination } from "@/composables/usePagination";
import Button from "@components/Button.vue";
import { getRefillOrders } from "@features/refill/api/refillApi";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import { formatCurrency, allRequest } from "@/utils/utils";
import { usePaperPrescriptionStore } from "@paper_prescription/store/paperPrescriptionStore";
import { useApiRequest } from "@/composables/useApiRequest";
import { useRoute, useRouter } from "vue-router";
import { useAuth } from "@/stores/auth";
import {
  checkAvailability,
  checkMedicineAvailability,
} from "@paper_prescription/api/paperPrescriptionApi";

const router = useRouter();
const route = useRoute();
const auth = useAuth();

const refillReq = usePagination({
  cb: getRefillOrders,
});

function fetchDrugs(ev) {
  refillReq.search.value = ev.target.value;
}

const paperPrescriptionStore = usePaperPrescriptionStore();

const req = useApiRequest();
function refill(data) {
  // data.drugPrescriptions.forEach(el => {
  //   paperPrescriptionStore.setPrescriptionDetail(res.data[idx], el.inventoryUuid)
  // })

  // const uuids = data.drugPrescriptions.map((el) => el.inventoryUuid);

  // console.log(uuids);
  
  paperPrescriptionStore.setData(data, true);
  router.push("/paper_prescription/detail");

  // req.send(
  //   () =>
  //     allRequest(
  //       uuids.reduce((state, el) => {
  //         state[el] = checkAvailability(el);

  //         // checkMedicineAvailability({
  //         //   drugName: el.drugName,
  //         //   stockLocationName: auth?.auth?.user?.stockLocationName,
  //         // })
  //         return state;
  //       }, {})
  //     ),
  //   (res) => {
  //     console.log(res);
  //     if (res.success) {
  //       uuids.forEach((id, idx) => {
  //         paperPrescriptionStore.setPrescriptionDetail(res.data[idx], id)
  //       })
  //       router.push("/paper_prescription/detail");
  //     }
  //   }
  // );
}
</script>
<template>
  <div class="flex justify-center pt-8 overflow-auto show-scrollbar">
    <div class="flex flex-col gap-4 w-[90%] h-full">
      <div class="flex justify-end">
        <Button
          type="primary"
          v-if="router.currentRoute.value.path !== '/refill/compoundingrefill'"
          @click="router.push('/refill/compoundingrefill')"
          class="text-sm bg-dark text-white py-3"
          size="md"
        >
          Compounding Refil
        </Button>
      </div>
      <div
        class="flex justify-between gap-2 items-center px-2 rounded border-primary border-2 min-h-drug-search-height"
      >
        <input
          @input="fetchDrugs"
          v-focus
          class="px-2 bg-transparent flex-1 h-12"
          placeholder="Search Patient Name"
        />
        <Button sizerefillReq="md" type="primary"> Search </Button>
      </div>
      <div
        v-if="!refillReq.pending.value && !refillReq.data.value?.length"
        class="flex-1 flex flex-col gap-12 items-center"
      >
        <img src="/src/assets/img/search_med.svg" />
        <div class="flex flex-col gap-4 items-center">
          <p class="font-medium text-md">Please search for refill order</p>
          <p class="text-xs px-20">
            Note: you can search using a patient name.
          </p>
        </div>
      </div>
      <Table
        class=""
        v-else
        :pending="refillReq.pending.value"
        :Fallback="TableRowSkeleton"
        :headers="{
          head: [
            'Drug Name',
            'Patient Name',
            'Phone Number',
            'Dosage Form',
            'Total Order',
            'Drug Price',
            'actions',
          ],
          row: [
            'drugName',
            'fullName',
            'patient.mobilePhone',
            'dosageForm',
            'totalOrder',
            'drugPrice',
          ],
        }"
        :rows="refillReq.data.value || []"
        :cells="{
          fullName: (_, row) => {
            return `${row?.patient?.title ?? ''} ${
              row?.patient?.firstName ?? ''
            } ${row?.patient?.fatherName ?? ''}`;
          },
          drugName: (_, row) => {
            return row.drugPrescriptions.map((el) => el?.drugName).join(',');
          },
          dosageForm: (_, row) => {
            return row.drugPrescriptions.map((el) => el?.dosageForm).join(',');
          },
          drugPrice: (_, row) => {
            return formatCurrency(
              row.drugPrescriptions.reduce(
                (accumulator, currentValue) =>
                  accumulator + currentValue.drugPrice,
                0
              )
            );
          },
        }"
      >
        <template #actions="{ row }">
          <div class="flex gap-4">
            <Button
              :pending="req.pending.value"
              @click.stop="refill(row)"
              class="text-sm bg-dark text-white"
              size="xs"
            >
              Refil
            </Button>
          </div>
        </template>
      </Table>
    </div>
  </div>
</template>
