<script setup>
import Table from "@/components/Table.vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import DashboardCell from "@/features/paper_prescription/components/DashboardCell.vue";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import icons from "@/utils/icons";
import { computed, onMounted, ref } from "vue";
import ProgressBar from "../../component/progressBar.vue";
import Quantity from "../../component/quantity.vue";
import BarChart from "../../component/BarChart.vue";
import LineChart from "../../component/LineChart.vue";
import ExpiredQuentity from "../../component/ExpiredQuentity.vue";
import AreaChart from "../../component/AreaChart.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import dayjs from "dayjs";
import {
  getDailyCompoundingPrescription,
  getDailyPrescription,
} from "../Api/DashboardApi";
import {
  addDay,
  formatCurrency,
  formatDateToYYMMDD,
  secondDateFormat,
  toasted,
} from "@/utils/utils";
import {
  getAllLowStockLevel,
  getAllSoonToBeExpired,
} from "@/features/StoreKeeper/api/InventoryApi";
import { usePaginationTemp } from "@/composables/usePaginaionTemp";
import { getAllPatient } from "../../user/Api/UserApi";
import Button from "@/components/Button.vue";
import { getSystemStatus, updateInventroyStatus } from "../Api/adminSettingsApi";
import { openModal } from "@customizer/modal-x";

const mockData = ref([
  {
    drugName: "Metrondazoel",
    comparativeToLastMonth: "45",
    quantity: "340",
  },
  {
    drugName: "AstraZeneca",
    comparativeToLastMonth: "327",
    quantity: "327",
  },
  {
    drugName: "FullStop",
    comparativeToLastMonth: "2133",
    quantity: "2133",
  },
  {
    drugName: "Metroendazone",
    comparativeToLastMonth: "340",
    quantity: "340",
  },
]);

const tempData = ref([
  {
    drugName: "Metrondazoel",
    expiredDate: "22-04-2-2034",
    noOfDaysLeft: "340 days",
  },
  {
    drugName: "AstraZeneca",
    expiredDate: "22-04-2-2010",
    noOfDaysLeft: "327 days",
  },
  {
    drugName: "FullStop",
    expiredDate: "22-04-2-2034",
    noOfDaysLeft: "2133 days",
  },
  {
    drugName: "Metroendazone",
    expiredDate: "22-04-2-2019",
    noOfDaysLeft: "340 days",
  },
]);

const pagination = usePaginationTemp({
  cb: (data) => getAllLowStockLevel({ ...data, page: 1, limit: 25 }),
});

const pagination1 = usePaginationcopy({
  cb: (data) => getAllSoonToBeExpired({ ...data, page: 1, limit: 25 }),
});

// const fromDate = ref(dayjs().format("YYYY-MM-DD"));
const fromDate = ref(formatDateToYYMMDD(new Date()));
console.log("h", fromDate);

// const toDate = ref(dayjs().format("YYYY-MM-DD"));
const toDate = ref(formatDateToYYMMDD(addDay(new Date(), 1)));
console.log("to", toDate);

const todaytotalAmount = ref("");
const yesterdaytotalAmount = ref("");
const todayCompoundingtotalAmount = ref("");
const yesterdayCompoundingtotalAmount = ref("");

const totalDailyPrescriptionAmount = computed(() => {
  return todaytotalAmount.value + todayCompoundingtotalAmount.value;
});

const percentageDifference = computed(() => {
  if (!yesterdaytotalAmount.value || yesterdaytotalAmount.value === 0) {
    return 0;
  }

  const difference = todaytotalAmount.value - yesterdaytotalAmount.value;
  return (difference / yesterdaytotalAmount.value) * 100;
});

// Computed property to format the percentage display
const formattedPercentage = computed(() => {
  const percentage = percentageDifference.value;
  if (percentage > 0) {
    return `+${percentage.toFixed(0)}% from yesterday`;
  } else if (percentage < 0) {
    return `${percentage.toFixed(0)}% from yesterday`;
  } else {
    return "There is no data yesterday";
  }
});

// Computed property to determine text color
const percentageColor = computed(() => {
  return percentageDifference.value > 0 ? "text-green-500" : "text-red-500";
});

const compoundingpercentageDifference = computed(() => {
  if (
    !yesterdayCompoundingtotalAmount.value ||
    yesterdayCompoundingtotalAmount.value === 0
  ) {
    return 0;
  }

  const difference =
    todayCompoundingtotalAmount.value - yesterdayCompoundingtotalAmount.value;
  return (difference / yesterdayCompoundingtotalAmount.value) * 100;
});

// Computed property to format the percentage display
const compoundingformattedPercentage = computed(() => {
  const percentage = compoundingpercentageDifference.value;
  if (percentage > 0) {
    return `+${percentage.toFixed(0)}% from yesterday`;
  } else if (percentage < 0) {
    return `${percentage.toFixed(0)}% from yesterday`;
  } else {
    return "There is no data yesterday";
  }
});

// Computed property to determine text color
const compoundingpercentageColor = computed(() => {
  return compoundingpercentageDifference.value > 0
    ? "text-green-500"
    : "text-red-500";
});

const req = useApiRequest();
const comreq = useApiRequest();

const fetchPrescriptionSales = () => {
  req.send(
    () => getDailyPrescription(),
    (res) => {
      if (res.success) {
        console.log("Data:", res.data);
        // Extract numeric value from the response
        // DailysalesAmount.value = parseFloat(res.data.replace(/[^0-9.]/g, "")) || 0;
        todaytotalAmount.value = res.data?.todayTotalAmount;
        yesterdaytotalAmount.value = res.data?.yesterdayTotalAmount;
      }
    }
  );
};

const fetchCompoundingPrescriptionSales = () => {
  comreq.send(
    () => getDailyCompoundingPrescription(),
    (res) => {
      if (res.success) {
        console.log("Data:", res.data);
        todayCompoundingtotalAmount.value = res.data?.todayTotalAmount;
        yesterdayCompoundingtotalAmount.value = res.data?.yesterdayTotalAmount;
      }
    }
  );
};

const patientReq = useApiRequest();

const totalPatient = ref("");
const returnedPatient = ref("");
const newPatient = ref("");

patientReq.send(
  () => getAllPatient(),
  (res) => {
    if (res.success) {
      totalPatient.value = res.data?.totalPatients;
      returnedPatient.value = res.data?.returningPatients;
      newPatient.value = res.data?.newPatients;
    }
  }
);

//fetch data authomaticallly when the component loads
onMounted(() => {
  fetchPrescriptionSales();
  fetchCompoundingPrescriptionSales();
});

const systemReq = useApiRequest();

systemReq.send(
  () => getSystemStatus(),
  (res) => {
    if (!res.success) {
      // toasted(false, "", res.error);
    }
  }
);

const lookReq = useApiRequest();
function lokInv(status = false) {
  openModal(
    "Confirmation",
    {
      title: "Update Inventory Status",
      message: `Are you sure you want to ${status ? 'lock' : 'open'} the inventory?	`,
    },
    (res) => {
      if(res) {
        lookReq.send(
          () => updateInventroyStatus(systemReq.response.value?.systemUuid, status),
          (res) => {
            if (res.success) {
              systemReq.send(
                () => getSystemStatus(),
                (res) => {
                  if (!res.success) {
                    toasted(false, "", res.error);
                  }
                }
              );
            }
          }
        );
      }
    }
  );
}
</script>

<template>
  <div class="h-full w-full px-16 py-16s">
    {{ console.log("ggggg") }}
    <div class="grid grid-cols-5 gap-4">
      <div class="col-span-2 p-6 rounded-lg gap-6 bg-[#FFFFFF]">
        <div class="flex justify-between">
          <div class="flex flex-col items-center">
            <p class="font-bold text-[20.12px] leading-[32.19px]">
              Sales & Revenue
            </p>
            <p class="font-normal text-[16.09px] leading-[30.17px] opacity-50">
              Total Sales (Today)
            </p>
          </div>
          <div
            class="flex items-center rounded-[8.38px] p-4 gap-[15.7px] bg-[#CDF1F8]"
          >
            <p
              class="font-medium text-[14.08px] leading-[20.12px] text-[#0F3659]"
            >
              Total Sales
            </p>
            <p
              v-if="req.pending.value"
              class="font-bold text-[20.12px] leading-[32.19px]"
            >
              ETB ...
            </p>
            <p v-else class="font-bold text-[20.12px] leading-[32.19px]">
              {{ formatCurrency(totalDailyPrescriptionAmount) }}
            </p>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-6 mt-5">
          <div
            class="flex flex-col rounded-[8.38px] p-[25.13px] gap-[15.7px] bg-[#FFE5DC] hover:bg-red-200"
          >
            <i v-html="icons.drugs" />
            <p
              v-if="req.pending.value"
              class="font-bold text-[16.75px] leading-[32.19px]"
            >
              ETB ...
            </p>
            <p v-else class="font-bold text-[16.75px] leading-[32.19px]">
              {{ formatCurrency(todaytotalAmount) }}
            </p>
            <p class="font-normal text-[16.75px] leading-[24.14px] opacity-50">
              Drugs
            </p>
            <p
              class="font-medium text-[12.07px] leading-[16.09px] text-[#2E7987]"
              :class="percentageColor"
            >
              {{ formattedPercentage }}
            </p>
          </div>

          <div
            class="flex flex-col rounded-[8.38px] p-[25.13px] gap-[15.7px] bg-[#FFF4DE] hover:bg-yellow-100"
          >
            <i v-html="icons.compounding" />
            <p
              v-if="comreq.pending.value"
              class="font-bold text-[16.75px] leading-[32.19px]"
            >
              ETB ...
            </p>
            <p v-else class="font-bold text-[16.75px] leading-[32.19px]">
              {{ formatCurrency(todayCompoundingtotalAmount) }}
            </p>
            <p class="font-normal text-[16.75px] leading-[24.14px] opacity-50">
              Compounding
            </p>
            <p
              class="font-medium text-[12.07px] leading-[16.09px] text-[#2E7987]"
              :class="compoundingpercentageColor"
            >
              {{ compoundingformattedPercentage }}
            </p>
          </div>
        </div>
      </div>
      <div class="col-span-3 rounded-lg p-[25.13px] gap-[25.13px] bg-[#FFFFFF]">
        <div class="flex flex-col gap-[10px]">
          <div class="flex justify-between place-items-center pb-4">
            <p class="font-bold text-[19.21px] leading-[30.74px]">
              Low Stock Levels
            </p>
            <button
              class="flex gap-1 items-center rounded border w-24 h-10 place-content-center font-medium text-[14.08px]"
            >
              <i v-html="icons.export" />
              Export
            </button>
            <Button
              v-privilage='["lock_inventory"]'
							@click="!systemReq.response.value?.value ? lokInv(true) : lokInv(false)"
              :pending="systemReq.pending.value"
              type="edge"
              class="flex items-center truncate gap-2"
              size="xs"
            >
              <i
                v-html="
                  !systemReq.response.value?.value ? icons.lock : icons.openLock
                "
              />
              {{
                !systemReq.response.value?.value
                  ? "Lock Inventory"
                  : "Open Inventory"
              }}
            </Button>
          </div>
          <div class="px-5">
            <Table
              class="overflow-auto show-scrollbar"
              :pending="pagination.pending.value"
              :headers="{
                head: [
                  'Drug Name',
                  'Comparatively to stock Limit ',
                  'Quantity',
                ],
                row: ['drugName', 'minima', 'totalAmount'],
              }"
              :Fallback="TableRowSkeleton"
              :rows="pagination.data.value?.content || []"
              :cells="{
                minima: ProgressBar,
                totalAmount: Quantity,
              }"
            >
            </Table>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-5 gap-6 mt-6">
      <div class="col-span-3 rounded-lg p-6 gap-6 bg-[#FFFFFF]">
        <div class="flex justify-between place-items-center">
          <div class="flex flex-col">
            <p class="font-bold text-[20.12px] leading-[32.19px]">
              Total Revenue
            </p>
            <p class="font-normal text-base leading-[30.17px] opacity-50">
              Total Sales (This Week)
            </p>
          </div>
          <button
            class="flex gap-1 items-center rounded border w-24 h-10 place-content-center font-medium text-[14.08px]"
          >
            <i v-html="icons.export" />
            Export
          </button>
        </div>
        <BarChart />
      </div>
      <div class="col-span-2 rounded-lg p-[25.13px] gap-[25.13px] bg-[#FFFFFF]">
        <div class="flex justify-between place-items-center">
          <p class="font-bold text-[19.21px] leading-[30.74px]">Dispensary</p>
          <p class="font-normal text-base leading-[30.17px] opacity-50">
            Items Sold (This Months)
          </p>
        </div>
        <LineChart />
      </div>
    </div>

    <div class="grid grid-cols-4 gap-4 mt-6">
      <div class="col-span-2 bg-white rounded-lg p-[25.13px] gap-[25.13px]">
        <div class="flex flex-col gap-[10px]">
          <div class="flex">
            <p class="font-bold text-[19.21px] leading-[30.74px]">
              Soon to be Expired
            </p>
          </div>
          <div class="">
            <Table
              class="overflow-auto show-scrollbar"
              :pending="pagination1.pending.value"
              :headers="{
                head: [
                  'Drug Name',
                  'Brand Name',
                  'Expired Date',
                  'Quantity',
                  'Days Left',
                ],
                row: [
                  'drugName',
                  'drugBrandName',
                  'expDate',
                  'totalAmount',
                  'daysLeft',
                ],
              }"
              :Fallback="TableRowSkeleton"
              :rows="pagination1.data.value?.content || []"
              :cells="{
                daysLeft: ExpiredQuentity,
                expDate: (date) => {
                  return secondDateFormat(date);
                },
              }"
            >
            </Table>
          </div>
        </div>
      </div>
      <div class="col-span-2 bg-white rounded-lg p-6 gap-6">
        <div class="flex justify-between place-items-center">
          <div class="flex flex-col">
            <p class="font-bold text-[20.12px] leading-[32.19px]">Customers</p>
          </div>
          <button
            class="flex gap-1 items-center rounded border w-24 h-10 place-content-center font-medium text-[14.08px]"
          >
            <i v-html="icons.export" />
            Export
          </button>
        </div>
        <div class="grid grid-rows-2 gap-6 mt-6">
          <div
            class="flex items-center gap-4 p-[25.13px] rounded-lg bg-[#FFF4DE]"
          >
            <i
              class="p-3 bg-[#FFCF00] rounded-full"
              v-html="icons.allPatient"
            />
            <div>
              <h3 class="font-bold text-2xl leading-[32.19px] text-[#381408]">
                {{ totalPatient }}
              </h3>
              <p class="font-normal text-base leading-6 text-[#381408]">
                All Patients
              </p>
            </div>
          </div>
          <div class="grid grid-cols-2 gap-6">
            <div
              class="flex items-center gap-4 p-[25.13px] rounded-lg bg-[#DCDDFF]"
            >
              <i
                class="p-3 bg-[#4E53FF] rounded-full"
                v-html="icons.newPatient"
              />
              <div>
                <h3 class="font-bold text-2xl leading-[32.19px] text-[#0D1063]">
                  {{ newPatient }}
                </h3>
                <p class="font-normal text-base leading-6 text-[#0D1063]">
                  New Patients
                </p>
              </div>
            </div>

            <div
              class="flex items-center gap-4 p-[25.13px] rounded-lg bg-[#DFFFDE]"
            >
              <i
                class="p-3 bg-[#23E61D] rounded-full"
                v-html="icons.returningPatient"
              />
              <div>
                <h3 class="font-bold text-2xl leading-[32.19px] text-[#052226]">
                  {{ returnedPatient }}
                </h3>
                <p class="font-normal text-base leading-6 text-[#052226]">
                  Returning Patients
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-5 gap-6 mt-6">
      <div class="col-span-5 p-[25.13px] rounded-lg bg-white">
        <div class="flex flex-col gap-3">
          <div class="flex justify-between place-items-center">
            <div class="">
              <p
                class="font-bold text-[19.21px] leading-[30.74px] text-[#05004E]"
              >
                Stock Ins and Out
              </p>
            </div>
            <div class="">
              <p class="font-normal text-base leading-[30.17px] text-[#737791]">
                Last Month & This Month
              </p>
            </div>
          </div>
          <AreaChart />
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
/* .bg-gradient {
    background: linear-gradient(to right, #fbc2eb, #a18cd1);
} */
.action-icon {
  cursor: pointer;
  color: #007bff;
}
</style>
