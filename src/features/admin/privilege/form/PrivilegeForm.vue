<script setup>
import { Form, Input } from "@/components/new_form_elements";
import Textarea from "@/components/new_form_elements/Textarea.vue";

const props = defineProps({
  privilege: Object,
  onSubmit: {
    type: Function,
  },
});

</script>
<template>
  <Form
    class="grid grid-cols-2 gap-4"
    :inner="false"
    id="privilegeForm"
  >
    <Input
      name="privilegeName"
      validation="required"
      label="Privilege Name"
      :value="privilege?.privilegeName || ''"
      :attributes="{
        placeholder: 'Enter Privilege',
      }"
    />
    <Input
      :value="privilege?.privilegeCategory || ''"
      name="privilegeCategory"
      label="Privilege Category"
      validation="required|alpha"
      :attributes="{
        placeholder: 'Enter Privilege category',
      }"
    />
    <Textarea
      validation="required|alpha2"
      class="col-span-2"
      name="privilegeDescription"
      :value="privilege?.privilegeDescription || ''"
      label="Privilege Description"
      :attributes="{
        placeholder: 'Enter Privilege description',
      }"
    />
  </Form>
</template>
