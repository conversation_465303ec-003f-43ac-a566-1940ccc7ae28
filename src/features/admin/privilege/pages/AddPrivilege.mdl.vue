<script setup>
import PrivilegeForm from "../form/PrivilegeForm.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { usePrivilege } from "../store/privilegeStore";
import { toasted } from "@/utils/utils.js";
import { createPrivilege } from "../Api/PrivilegeApi";
import NewFormParent from "../../role/components/NewFormParent.vue";
import { useRouter } from "vue-router";
import { useForm } from "@/new_form_builder/useForm";
import Button from "@/components/Button.vue";
import privileges from "@/privilege.json";
import { ref } from "vue";
import ModalParent from "@/components/ModalParent.vue";

const req = useApiRequest();
const privilegeStore = usePrivilege();

// function sendPrivilegesToBackend() {
//     for (const privilege of privileges.privileges) {
//         try {
//             const privilegesReq = useApiRequest();
//             privilegesReq.send(() => createPrivilege(privilege),
//                 (res) => {
//                     if (res.success) {
//                         privilegeStore.add(res.data);
//                         toasted(true, `Privilege "${privilege.privilegeName}" created successfully!`);
//                     }
//                     else {
//                         toasted(false, `Failed to create privilege "${privilege.privilegeName}".`, res.error);
//                     }
//                 }
//             )
//         } catch (error) {
//             console.error(`Error creating privilege "${privilege.privilegeName}":`, error);
//             toasted(false, `Error creating privilege "${privilege.privilegeName}".`);
//         }

//     }
// }

const { submit } = useForm("privilegeForm");
function create({ values, reset }) {
  req.send(
    () => createPrivilege(values),
    (res) => {
      if (res.success) {
        privilegeStore.add(res.data);
        reset();
      }
      toasted(res.success, "Privilege Created Successfully", res.error);
    }
  );
}
const router = useRouter();

const goBack = () => {
  router.go(-1);
};
console.log(privileges);
// sendPrivilegesToBackend();
</script>
<template>
  <ModalParent>
    <NewFormParent title="Add Privileges" class="flex flex-col h-96">
      <PrivilegeForm />
      <template #bottom>
        <Button
          size="xl"
          type="primary"
          class="flex w-full justify-center  gap-3 p-2 bg-primary"
          :pending="req.pending.value"
          @click.prevent="submit(create)"
        >
          <!-- <i class="pb-[3px]" v-html="icons.plus" /> -->
          Add Privilege
        </Button>
      </template>
    </NewFormParent>
  </ModalParent>
</template>
