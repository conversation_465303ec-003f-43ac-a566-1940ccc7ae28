<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { useApiRequest } from "@/composables/useApiRequest";
import { getPrivilegeById, updatePrivilege } from "../Api/PrivilegeApi";
import NewFormParent from "../../role/components/NewFormParent.vue";
import PrivilegeForm from "../form/PrivilegeForm.vue";
import { usePrivilege } from "../store/privilegeStore";
import { ref } from "vue";
import { toasted } from "@/utils/utils.js";
import Button from "@/components/Button.vue";
import { useForm } from "@/new_form_builder/useForm";
import NewFormParent2 from "../../role/components/NewFormParent2.vue";
import ModalParent from "@/components/ModalParent.vue";

const { submit } = useForm("privilegeForm");
const privilegeStore = usePrivilege();
const route = useRoute();
const privilegeUuid = route.params.privilegeUuid;
const req = useApiRequest();
const updateReq = useApiRequest();

const privilege = ref(
  privilegeStore.privilege.find((el) => el.privilegeUuid == privilegeUuid) || {}
);

if (!Object.keys(privilege.value).length) {
  req.send(
    () => getPrivilegeById(privilegeUuid),
    (res) => {
      if (res.success) {
        privilege.value = res.data;
      }
    }
  );
}

function update({ values }) {
  updateReq.send(
    () => updatePrivilege(privilegeUuid, values),
    (res) => {
      if (res.success) {
        privilegeStore.update(privilegeUuid, { ...privilege, ...values });
      }
      toasted(res.success, "Successfully Updated", res.error);
    }
  );
}

const router = useRouter();

const goBack = () => {
  router.go(-1);
};
</script>
<template>
  <ModalParent>
    <NewFormParent title="Update Privileges" class="flex flex-col">
      <PrivilegeForm :privilege="privilege" />
      <template #bottom>
        <Button
          size="xl"
          type="primary"
          class="flex w-full justify-center items-center gap-3 p-2 bg-primary"
          :pending="req.pending.value"
          @click.prevent="submit(update)"
        >
          Update Privilege
        </Button>
      </template>
    </NewFormParent>
  </ModalParent>
</template>
