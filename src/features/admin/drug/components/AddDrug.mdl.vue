<script setup>
import FormSubmitButton from "@/components/FormSubmitButton.vue";
import Input from "@/components/new_form_elements/Input.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import Form from "@/new_form_builder/Form.vue";
import { useRouter } from "vue-router";
import { craeteDrug } from "../Api/drugApi";
import { useDrugs } from "../store/drugStore";
import { toasted } from "@/utils/utils";
import { useForm } from "@/new_form_builder/useForm";
import Textarea from "@/components/new_form_elements/Textarea.vue";
import { closeModal } from "@customizer/modal-x";
import DrugForm from '../form/DrugForm.vue'
import NewFormParent from "../../role/components/NewFormParent.vue";
const { submit } = useForm("addform");
const drugs = useApiRequest();
const router = useRouter();
const drug = useDrugs();

function drugsAdd(values) {
  drugs.send(
    () => craeteDrug(values),
    (res) => {
      if (res.success) {
        drug.add(res.data);
      }
      toasted(res.success, "Drug Created", res.error);
      closeModal();
    }
  );
}
</script>

<template>
  <div class="bg-black/50 min-h-full w-full p-10 grid place-items-center">
    <NewFormParent size="lg" title="Add Drug">
			<DrugForm 
				:onSubmit="drugsAdd"
			/>
		</NewFormParent>
  </div>
</template>
