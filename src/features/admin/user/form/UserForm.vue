<script setup>
import Input from "@/components/new_form_elements/Input.vue";
import InputPassword from "@/components/new_form_elements/InputPassword.vue";
import Select from "@/components/new_form_elements/Select.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { getAllRole } from "../../role/Api/RoleApi";
import Form from "@/components/new_form_builder/Form.vue";
import { ref } from "vue";
import { addBranch } from "@/features/StoreKeeper/api/BranchesApi";
import { useUsers } from "../store/userStore";
import Button from "@/components/Button.vue";
import { watch } from "vue";
import { UserRole } from "@/utils/utils";

const props = defineProps({
  roles: {
    type: Array,
    required: true,
  },
  password: {
    type: Boolean,
    default: true,
  },
  pharmacy: {
    type: Array,
    required: true,
  },
  user: {
    type: Object,
  },
});

const userType = ref(props.user?.roleUuid);
const userStore = useUsers();

//  validation="required-(Username must start with '@' and be 3 to 20 characters long, allowing letters, numbers, '.' and '_')|username"
const selectedBranch = ref(
  props.user?.branchUuid || "ba01dc5a-4c16-4383-8956-9d93e768f2c3"
); // Stores the selected branch
const newBranch = ref(""); // Stores the new branch name
const branchOptions = ref([]); // Stores branch options

const branchreq = useApiRequest();
function addNewBranch() {
  branchreq.send(
    () => addBranch({ branchName: newBranch.value }),
    (res) => {
      if (res.success) {
        userStore.addBranch(res.data);
        selectedBranch.value = res.data.branchUuid;
      }
      toasted(res.success, "Branch Added Succesfully.", res.error);
    }
  );
}

const branchables = [
  UserRole.Pharmacist,
  UserRole.StoreKeeper,
  UserRole.Cashier,
  UserRole.Case_Team_Leader
];
</script>
<template>
  <Form
    :inner="false"
    class="grid grid-cols-3 gap-5 mt-3 p-6"
    id="add-user-form"
  >
    <Input
      name="userName"
      label="User Name"
      :value="user?.userName"
      :attributes="{
        placeholder: 'Enter User Name',
      }"
    />
    <Input
      name="email"
      validation="required|email"
      label="Email"
      :value="user?.email"
      :attributes="{
        placeholder: 'Enter User Email',
      }"
    />
    <InputPassword
      v-if="password"
      name="password"
      label="Password"
      validation="required|pass"
      :attributes="{
        placeholder: 'Password',
        ...(user ? { autocomplete: 'new-password' } : {}),
      }"
    />
    <Select
      name="title"
      label="Title"
      :value="user?.title"
      :options="['Mr.', 'Ms.', 'Dr.', 'Prof']"
      :attributes="{
        type: 'text',
        placeholder: 'Title',
      }"
    >
    </Select>
    <Input
      name="firstName"
      validation="required|alpha|minmax-3,15"
      label="First Name"
      :value="user?.firstName"
      :attributes="{
        placeholder: 'Enter firstname',
      }"
    />
    <Input
      name="fatherName"
      validation="required|alpha|minmax-3,15"
      label="Father Name"
      :value="user?.fatherName"
      :attributes="{
        placeholder: 'Enter fathername',
      }"
    />
    <Input
      name="grandFatherName"
      validation="alpha|minmax-3,15"
      label="Grand father Name"
      :value="user?.grandFatherName"
      :attributes="{
        placeholder: 'Enter grand father name',
      }"
    />
    <Select
      name="gender"
      label="Gender"
      validation="required"
      :value="user?.gender"
      :options="['Female', 'Male']"
      :attributes="{
        type: 'text',
        placeholder: 'Gender',
      }"
    >
    </Select>
    <Input
      name="mobilePhone"
      label="Mobile Phone"
      validation="required|phone"
      :value="user?.mobilePhone"
      :attributes="{
        placeholder: 'Mobile phone',
      }"
    />
    <Select
      :obj="true"
      v-model="userType"
      name="roleUuid"
      label="Role"
      validation="required"
      :options="
        (roles || []).map((role) => ({
          label: role.roleName,
          value: role.roleUuid,
        }))
      "
      :attributes="{
        placeholder: 'Select Role',
      }"
    >
    </Select>
    <Select
      :value="user?.userStatus || 'ACTIVE'"
      name="userStatus"
      label="User Status"
      validation="required"
      :options="['ACTIVE', 'HISTORY', 'SUSPENDED', 'PENDING']"
      :attributes="{
        type: 'text',
        placeholder: 'Status',
      }"
    >
    </Select>

    <div class="flex flex-col gap-2">
      <Select
        v-if="selectedBranch != 'Other' && roles.find(el => el.roleUuid == user?.roleUuid)?.roleName"
        :obj="true"
        name="branchUuid"
        label="Branch"
        validation="required"
        v-model="selectedBranch"
        :options="[
          ...(pharmacy || []).map((pharma) => ({
            label: pharma.branchName,
            value: pharma.branchUuid,
          })),
        ]"
        :attributes="{
          placeholder: 'Select Branch',
        }"
      >
        <template v-slot:option="{ option }">
          <span :class="{ 'font-bold': option.value === 'Other' }">{{
            option.label
          }}</span>
        </template>
      </Select>

      <!-- Show input field when 'Other' is selected -->
      <Input
        v-else
        v-model="newBranch"
        name="branchName"
        label="New Branch Name"
        :attributes="{ placeholder: 'Enter branch name' }"
      />
      <!-- Button to add the new branch -->
      <Button
        v-if="selectedBranch === 'Other'"
        @click.prevent="addNewBranch"
        class="bg-blue-500 text-white px-4 py-2 rounded"
      >
        Add Branch
      </Button>
    </div>
  </Form>
</template>
