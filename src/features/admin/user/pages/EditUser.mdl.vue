<script setup>
import { useRoute, useRouter } from "vue-router";
import { useApiRequest } from "@/composables/useApiRequest";
import { getUserById, updateUserById } from "../Api/UserApi";
import { getAllRole } from "../../role/Api/RoleApi";
import { allRequest, toasted } from "@/utils/utils.js";
import { useUsers } from "../store/userStore";
import NewFormParent from "../../role/components/NewFormParent.vue";
import { ref } from "vue";
import Button from "@/components/Button.vue";
import { useForm } from "@/new_form_builder/useForm";
import UserForm from "../form/UserForm.vue";
import { getAllBranches } from "@/features/StoreKeeper/api/BranchesApi";
import NewFormParent2 from "../../role/components/NewFormParent2.vue";
import PendingRequest from "@/components/PendingRequest.vue";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const route = useRoute();
const userStore = useUsers();
const userUuid = route.params.userUuid;
// const user = ref(userStore.users.find((el) => el.userUuid == userUuid) || {});
const { submit } = useForm("add-user-form");

const userReq = useApiRequest();
const updateReq = useApiRequest();

userReq.send(
  () =>
    allRequest({
      user: getUserById(props.data?.userUuid),
      roles: getAllRole({ page: 1, limit: 500 }),
      pharmacy: getAllBranches({ page: 1, limit: 500 }),
    }),
  (res) => {}
);

function update({ values }) {
  updateReq.send(
    () => updateUserById(props.data?.userUuid, values),
    (res) => {
      toasted(res.success, "Successfully Updated", res.error);
      if (res.success) {
        userStore.update(userUuid, { ...values, ...props.data });
      }
    }
  );
}
const router = useRouter();

const goBack = () => {
  router.go(-1);
};
</script>
<template>
  <div class="bg-black/50 min-h-full p-10 grid place-items-center">
    <NewFormParent title="Edit User" size="lg">
      <UserForm
        v-if="!userReq.pending.value"
        :password="false"
        :roles="userReq.response.value?.roles?.content"
        :user="data"
        :pharmacy="userReq.response.value?.pharmacy?.content"
      />
      <PendingRequest v-else />

      <template #bottom>
        <div class="p-2">
          <Button
            type="primary"
            class="w-full"
            :pending="updateReq.pending.value"
            @click.prevent="submit(update)"
          >
            Update User
          </Button>
        </div>
      </template>
    </NewFormParent>
  </div>
</template>
