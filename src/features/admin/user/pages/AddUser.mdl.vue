<script setup lang="ts">
import Button from "@/components/Button.vue";
import Input from "@/components/new_form_elements/Input.vue";
import InputPassword from "@/components/new_form_elements/InputPassword.vue";
import Select from "@/components/new_form_elements/Select.vue";
import Form from "@/new_form_builder/Form.vue";
import NewFormParent from "../../role/components/NewFormParent.vue";
import { closeModal } from "@customizer/modal-x";
import { useApiRequest } from "@/composables/useApiRequest";
import { CreateUser } from "../Api/UserApi";
import { useUsers } from "../store/userStore";
import { allRequest, toasted } from "@/utils/utils";
import { useForm } from "@/new_form_builder/useForm";
import { getAllRole } from "../../role/Api/RoleApi";
import {
  addBranch,
  getAllBranches,
} from "@/features/StoreKeeper/api/BranchesApi";
import { ref } from "vue";
import UserForm from "../form/UserForm.vue";
import PendingRequest from "@/components/PendingRequest.vue";

const { submit } = useForm("add-user-form");

const req = useApiRequest();
const rolereq = useApiRequest();

rolereq.send(() =>
  allRequest({
    roles: getAllRole({ page: 1, limit: 500 }),
    pharmacy: getAllBranches({ page: 1, limit: 500 }),
  })
);

function create({ values }) {
  req.send(
    () => CreateUser(values),
    (res) => {
      if (res.success) {
        // user.add(res.data);
				closeModal();
      }
      toasted(res.success, "User Created", res.error);
    }
  );
}
</script>

<template>
  <div class="bg-black/50 min-h-full p-10 grid place-items-center">
    <NewFormParent title="Add User" size="lg">
      <UserForm
        v-if="!rolereq.pending.value"
        :roles="rolereq.response.value?.roles?.content"
        :pharmacy="rolereq.response.value?.pharmacy?.content"
      />
      <PendingRequest v-else />
      <template #bottom>
        <div class="p-2">
          <Button
            type="primary"
            :pending="req.pending.value"
            @click.prevent="submit(create)"
            class="w-full"
          >
            Add User
          </Button>
        </div>
      </template>
    </NewFormParent>
  </div>
</template>
