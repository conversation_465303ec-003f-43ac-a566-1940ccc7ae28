<script setup lang="ts">
import Form from "@/new_form_builder/Form.vue";
import NewFormParent from "../../role/components/NewFormParent.vue";
import Button from "@/components/Button.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { toasted } from "@/utils/utils";
import { changeSellingPrice } from "../api/changeSellingPrice";
import Input from "@/components/new_form_elements/Input.vue";
import { useForm } from "@/new_form_builder/useForm";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const req = useApiRequest();
function update({ values }) {
  if (req.pending.value) return;

  req.send(
    () =>
      changeSellingPrice(props.data?.inventoryUuid, values),
    (res) => {
      toasted(res.success, "Price Updated.", res.error);
    }
  );
}

const {submit} = useForm('update-price')
</script>

<template>
	<div class="grid place-items-center p-4 bg-black/50 min-h-full" >
		<NewFormParent size="sm" :title="``">
			<template #title >
				<p class="p-2 font-bold" >Update Retail Price for <br /> {{data?.drugName}}</p>
			</template>
			<Form :inner="false" id="update-price">
				<Input
					:value="data.sellingUnitPrice"
					name="sellingUnitPrice"
					label="Retail Price"
					validation="required|price"
					:attributes="{
						placeholder: 'Enter New Selling Price',
					}"
				/>
			</Form>
			<template #bottom >
				<div class="p-2 grid" >
					<Button :pending="req.pending.value" type="secondary" @click.prevent="submit(update)"> Update </Button>
				</div>
			</template>
		</NewFormParent>
	</div>
</template>
