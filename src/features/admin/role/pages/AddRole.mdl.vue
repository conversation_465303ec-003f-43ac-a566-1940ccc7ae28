<script setup>
import PrivilegesDataProvider from "../../privilege/components/PrivilegesDataProvider.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { craeteRole } from "../Api/RoleApi";
import { toasted } from "@/utils/utils.js";
import { useRoles } from "../store/roleStore";
import RoleForm from "../form/RoleForm.vue";
import { useRouter } from "vue-router";
import NewFormParent from "../components/NewFormParent.vue";
import Button from "@/components/Button.vue";
import { useForm } from "@/new_form_builder/useForm";
import NewFormParent2 from "../components/NewFormParent2.vue";
import ModalParent from "@/components/ModalParent.vue";

const { submit } = useForm("roleForm");
const roleStore = useRoles();
const req = useApiRequest();

function create({ values }) {
  req.send(
    () => craeteRole(values),
    (res) => {
      if (res.success) {
        roleStore.add(res.data);
      }
      toasted(res.success, "Role Created", res.error);
    }
  );
}
const router = useRouter();
const goBack = () => {
  router.go(-1);
};
</script>
<template>
  <ModalParent>
    <NewFormParent class="!p-0" title="Add Role">
      <PrivilegesDataProvider :pre-page="500" v-slot="{ privileges, pending }">
        <RoleForm v-if="!pending" :privileges="privileges" :roles="roleStore" />
        <p v-else>Loading...</p>
      </PrivilegesDataProvider>
			<template #bottom >
				<Button
					size="sm"
					type="primary"
					class="flex justify-center items-center gap-3 p-2 bg-primary"
					:pending="req.pending.value"
					@click.prevent="submit(create)"
				>
					<!-- <i class="pb-[3px]" v-html="icons.plus" /> -->
					Add Role
				</Button>
			</template>
    </NewFormParent>
  </ModalParent>
</template>
