<script setup>
import InputError from "@/components/new_form_elements/InputError.vue";
import InputParent from "@/new_form_builder/InputParent.vue";
import icons from "@/utils/icons";
import { computed, ref, watch } from "vue";

const props = defineProps({
  label: String,
  value: String,
  options: {
    type: Array,
    required: true,
  },
  selectedPrivilege: {
    type: Array,
  },
  validation: String,
});

const selected = ref(props?.selectedPrivilege || []);

const obj = {};

const group = computed(() => {
  return props?.options.reduce((state, el) => {
    if (state[el.privilegeCategory]) {
      state[el.privilegeCategory].push(el);
    } else {
      state[el.privilegeCategory] = [el];
    }
    return state;
  }, {});
});

watch(
  () => props.selectedPrivilege,
  (newPrivileges) => {
    if (!Array.isArray(newPrivileges)) {
      selected.value = [];
      return;
    }
    newPrivileges.forEach((el) => {
      if (el.privilegeUuid) {
        selected.value.push(el.privilegeUuid);
      }
    });
  },
  { immediate: true }
);

function toggle(id) {
  if (!selected.value.includes(id)) {
    selected.value.push(id);
  } else {
    const idx = selected.value.findIndex((el) => el == id);
    idx > -1 && selected.value.splice(idx, 1);
  }
  console.log(selected.value);
}

const isChecked = computed(() => {
  return (id) => selected.value.includes(id);
});

const isAllChecked = computed(() => {
  return (category) =>
    group.value[category].every((el) =>
      selected.value.includes(el.privilegeUuid)
    );
});

function selectAll(checked, category) {
  const ids = group.value[category].map((el) => el.privilegeUuid);
  selected.value = selected.value.filter((el) => !ids.includes(el));
  if (checked) {
    selected.value = [...selected.value, ...ids];
  }
}

const selectedPrivileges = computed(() => {
  return props.options.filter((el) =>
    selected.value.includes(el.privilegeUuid)
  );
});

const copied = ref(false);
function copyPrivileges() {
  if (navigator?.clipboard && !copied.value) {
    console.log(selected.value);

    navigator.clipboard.writeText(JSON.stringify(selected.value));
    copied.value = true;
    setTimeout(() => {
      copied.value = false;
    }, 800);
  }
}

function pastedPrivileges(ev) {
  let value = ev.target.value
  try {
    selected.value = JSON.parse(value)
    ev.target.value = ''
  } catch(err) {

  }
}
</script>
<template>
  <InputParent
    :validation="validation"
    v-model="selected"
    v-slot="{ setRef, error }"
  >
    <div>
      <div class="w-full" v-if="selected.length">
        <div class="flex items-center justify-between gap-2">
          <div class="flex items-center gap-2" >
            Selected Privileges
            <button @click.prevent="copyPrivileges">
              <i :class="[copied && 'text-green-400']" v-html="icons.copy" />
            </button>
          </div>
          <div class="flex h-10 bg-gray-200 border border-gray-300 rounded-md items-center" >
            <div class="size-10 grid place-items-center" >
              <i class="text-gray-600" v-html="icons.paste" />
            </div>
            <input @input="pastedPrivileges" placeholder="Paste Your Privileges" class='text-sm px-3 h-full bg-transparent' />
          </div>
        </div>
        <div
          class="mt-2 border rounded p-2 max-h-28 gap-2 overflow-y-auto grid grid-cols-4"
        >
          <div
            :key="privi.privilegeUuid"
            v-for="privi in selectedPrivileges"
            class="flex items-center gap-2"
          >
            <input
              :checked="isChecked(privi?.privilegeUuid)"
              @click="toggle(privi?.privilegeUuid)"
              class="size-3"
              type="checkbox"
            />
            <div class="flex flex-col">
              <span>{{ privi?.privilegeName }}</span>
              <span class="text-xs">{{ privi?.privilegeDescription }}</span>
            </div>
          </div>
        </div>
      </div>
      <div :ref="setRef" class="flex flex-col gap-2">
        <div class="flex items-center gap-2 border-b py-2">
          <span
            :data-required="
              `${validation}`.includes('required') ? 'true' : 'false'
            "
            :title="label"
            class="text-sm capitalize px-1 truncate"
            v-if="label"
            >{{ label }}</span
          >
          <InputError :error="error" />
        </div>
        <div class="grid grid-cols-4 gap-y-4">
          <div class :key="category" v-for="category in Object.keys(group)">
            <div class="border-b py-2 flex items-center gap-2">
              <input
                :checked="isAllChecked(category)"
                @click="(ev) => selectAll(ev.target.checked, category)"
                class="size-5"
                type="checkbox"
              />
              <p class="font-bold">{{ category }}</p>
            </div>
            <div
              class="flex items-center py-2 gap-2"
              :key="privilege.privilegeName"
              v-for="privilege in group[category]"
            >
              <input
                :checked="isChecked(privilege.privilegeUuid)"
                @click="toggle(privilege.privilegeUuid)"
                class="size-4"
                type="checkbox"
              />
              <p class="text-xs truncate">{{ privilege.privilegeName }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </InputParent>
</template>
