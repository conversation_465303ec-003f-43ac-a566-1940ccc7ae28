import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const api = new ApiService();

const path = "/prescriptions";
const path2 = "/compounds";

// export function getExpresctions(query = {}, config) {
//   const qr = getQueryFormObject(query)
//   return api.addAuthenticationHeader().get(`${path}/prescriptions${qr}`, config)
// }
// export function getPrescription(query) {
//   return api.addAuthenticationHeader().post(
//     `${path}/getPrescriptions,{
//     params: {
//       status: " PRESCRIBED",
//       ...query,
//     }
// }`,
//     {}
//   );
// }

export function getPrescription(query) {
  const params = new URLSearchParams({
    status: "PRESCRIBED",
    ...query,
  }).toString();

  return api
    .addAuthenticationHeader()
    .get(`${path}/getPrescriptions?${params}`, {});
}

// export function getPrescription(query = {}, config = {}) {
//   const qr = getQueryFormObject(query);
//   return api
//     .addAuthenticationHeader()
//     .get(`${path}/getPrescriptions${qr}`, config);
// }
export function getCompoundingPrescription(query) {
  const params = new URLSearchParams({
    status: "PRESCRIBED",
    ...query,
  }).toString();
  return api
    .addAuthenticationHeader()
    .get(`${path2}/all-compounding-prescription?${params}`, {});
}
