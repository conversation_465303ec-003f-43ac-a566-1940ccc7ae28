<script setup>
import Button from "@/components/Button.vue";
import Table from "@/components/Table.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { usePaginationTemp } from "@/composables/usePaginaionTemp";
import { getPrescription } from "@/features/ePrescription/api/ePrscriptionAPi";
import { checkMedicineAvailability } from "@/features/paper_prescription/api/paperPrescriptionApi";
import { usePaperPrescriptionStore } from "@/features/paper_prescription/store/paperPrescriptionStore";
import { getMedicineByInventryId } from "@/features/StoreKeeper/api/InventoryApi";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import { useAuth } from "@/stores/auth";
import { allRequest, formatCurrency, genId, toasted } from "@/utils/utils";
import { Icon } from "@iconify/vue";
import { computed, ref, watch } from "vue";
import { useRouter } from "vue-router";
import CompoundingE_Prescription from "./CompoundingE_Prescription.vue";
import Select from "@/components/new_form_elements/Select.vue";
import { openModal } from "@customizer/modal-x";

const auth = useAuth();
const paperPrescriptionStore = usePaperPrescriptionStore();

const pagination = usePaginationTemp({
  cb: getPrescription,
});
console.log(paperPrescriptionStore);

const checklist = ref([]);
const req = useApiRequest();

const drug = ref();
const router = useRouter();

const selected = ref("prescription");

// function checkAvailabilty(name, id) {
//   checklist.value.push(name)
//   drug.value = null
//   console.log(name)

//   req.send(
//     () => checkMedicineAvailability({
//       drugName: id,
//       stockLocationName: auth?.auth?.user?.stockLocationName,
//     }),
//     res => {
//       if (res.success) {
//         drug.value = {
//           name,
//           drug: res.data?.[0]
//         }
//       }
//       checklist.value = checklist.value.filter(el => el != name)
//     }
//   )
// }

// const isPending = computed(() => name => {
//   return checklist.value.includes(name)
// })

// watch(pagination.data, () => {
//   if(pagination.data.value?.length) {
//     pagination.data.value.unshift({
//       patientUuid: "9a5f0123-1041-4cb6-b25b-1c2e81604fd0",
//       firstName: 'Birhane',
//       fatherName: 'Araya',
//       mobilePhone: '*********',
//       drugsName: 'Gliclazide',
//       insuranceName: 'Dashen Bank',
//       drugsFrequency: '2 Day/s',
//       frequencyRate: '2',
//       frequencyRateUnit: 'Day/s',
//       drugsRoute: 'nassal',
//       drugsDuration: '4',
//       totalQty: '40',
//       chiefCompliant: "new complaint",
//       status: 'Prescribed',
//       drugsDose: 2
//     })
//   }
// })

function fetchDrugs(ev) {
  pagination.search.value = ev.target.value;
}

watch(pagination.data, () => {
  if (pagination.data.value?.length) {
    pagination.data.value.forEach((el) => {
      el.id = genId.next().value;
    });
  }
});

// function sendToPayemt(row) {
//   req.send(
//     () =>
//       getPrescription({}),
//     (res) => {
//       if (!res.success) {
//         paperPrescriptionStore.setDone(false)
//       }
//     }
//   );
// }
const getDataReq = useApiRequest();
function goToDetail(data) {
  const drugs = data.drugPrescriptions.reduce((req, drug) => {
    req[drug.inventoryUuid] = getMedicineByInventryId(drug.inventoryUuid);
    return req;
  }, {});

  getDataReq.send(
    () => allRequest(drugs),
    (res) => {
      if (res.success) {
        paperPrescriptionStore.setData(
          {
            ...data,
            drugPrescriptions: Object.values(res.data).map((el, idx) => {
              console.log(idx, data.drugPrescriptions[idx]);

              return { ...el, ...data.drugPrescriptions[idx] };
            }),
          },
          false,
          true
        );
        router.push("/paper_prescription/detail");
      }
    }
  );
}
</script>
<template>
  <div class="flex h-full pt-8">
    <div class="flex justify-center flex-1">
      <div class="flex flex-col gap-4 w-[90%] h-full">
        <div class="flex gap-1 items-center">
          <label for="options">Choose an option:</label>
          <select
            class="p-3 rounded border-primary bg-primary"
            v-model="selected"
            name="options"
            id="options"
          >
            <option value="compounding">Compounding</option>
            <option value="prescription">Prescription</option>
          </select>
        </div>
        <div v-if="selected === 'prescription'">
          <div
            class="flex justify-between gap-2 items-center px-2 rounded border-primary border-2 min-h-drug-search-height"
          >
            <input
              @input="fetchDrugs"
              autofocus
              class="px-2 bg-transparent flex-1 h-12"
              placeholder="Search Patient"
            />
            <Button size="md" type="primary"> Search </Button>
          </div>

          <Table
            class="mt-7 overflow-auto show-scrollbar"
            :Fallback="TableRowSkeleton"
            :pending="pagination.pending.value"
            :headers="{
              head: [
                'Physician Name',
                'Patient Name',
                'Phone',
                'Gender',
                'Medicine Name',
                'Price',
                'Quantity',
                'Total Order',
                'Status',
                'actions',
              ],
              row: [
                'physicianname',
                'fullname',
                'patient.mobilePhone',
                'patient.gender',
                'drugName',
                'totalPrice',
                'totalQuantity',
                'totalOrder',
                'status',
              ],
            }"
            :rows="pagination.data.value || []"
            :cells="{
              fullname: (_, row) => {
                return (
                  `${row?.patient?.title ?? ''} ${
                    row?.patient?.firstName ?? ''
                  } ${row?.patient?.fatherName ?? ''} ${
                    row?.patient?.grandFatherName ?? ''
                  }` ?? ''
                );
              },
              physicianname: (_, row) => {
                return `${row?.physician?.physicianFirstName ?? ''} ${
                  row?.physician?.physicianLastName ?? ''
                }`;
              },
              drugName: (_, row) => {
                return (row.drugPrescriptions || [])
                  .map((el) => el?.drugName)
                  .join(', ');
              },
              drugPrice: (_, row) => {
                return formatCurrency(
                  (row.drugPrescriptions || []).reduce(
                    (acc, currentvalue) => acc + currentvalue.list_price,
                    0
                  )
                );
              },
              totalQuantity: (_, row) => {
                return (row.drugPrescriptions || [])
                  .map(
                    (prescription) =>
                      `${prescription.totalQuantity} ${prescription.unit || ''}`
                  )
                  .join(', ');
              },
              totalPrice: (_, row) => {
                return formatCurrency(row?.totalPrice);
              },
            }"
          >
            <template #actions="{ row }">
              <div class="flex gap-4">
                <button
                  @click="goToDetail(row)"
                  class="italic underline text-sm"
                >
                  <div v-if="req.pending.value">
                    <Icon icon="svg-spinners:3-dots-scale" class="text-2xl" />
                  </div>
                  <div class="text-primary text-md" v-if="!req.pending.value">
                    Detail
                  </div>
                  <!-- <p v-if='!drug || row?.id != drug?.name'>
                    {{ !isPending(row?.id) ? ' Check Availability' : 'Checking...' }}
                  </p> -->
                </button>
                <Button
                  @click.stop="
                    openModal('ReturnToDoctor', {
                      prescriptionUuid: row?.prescriptionUuid,
                      patientName:
                        `${row?.patient?.title ?? ''} ${
                          row?.patient?.firstName ?? ''
                        } ${row?.patient?.fatherName ?? ''} ${
                          row?.patient?.grandFatherName
                        }` ?? '',
                      drugName: (row.drugPrescriptions || [])
                        .map((el) => el?.drugName)
                        .join(', '),
                    })
                  "
                  class="text-sm border border-error text-error"
                  size="xs"
                >
                  Return Order
                </Button>
              </div>
            </template>
          </Table>
        </div>
        <CompoundingE_Prescription v-if="selected === 'compounding'" />
      </div>
    </div>
  </div>
</template>
const filteredDrugs =
paperPrescriptionStore.paper_prescription.prescriptionDetail.filter(drug =>
(drug?.drug?.totalQuantity < drug?.drug?.totalAmount) );
