<template>
  <DefaultPage>
    <div class="flex justify-between items-center mb-4">
      <h1 class="text-xl font-bold">Customers</h1>
      <Button 
        @click="openModal('AddPatient')" 
        type="primary" 
        class="flex items-center gap-2"
      >
        <BaseIcon :path="mdiPlus" :size="20" />
        Add Customer
      </Button>
    </div>
    
    <input 
      class="px-3 h-10 rounded-md bg-gray-200 border mb-4" 
      v-model="searchTerm" 
      placeholder="Search customers" 
    />
    
    <Table 
      :pending="pagination.pending.value" 
      :headers="{
        head: [
          'Full Name',
          'Email',
          'Phone Number',
          'Address',
          'Date of Birth',
					'Gender',
          'Status',
          'Actions'
        ],
        row: [
          'fullName',
          'email',
          'mobilePhone',
          'address',
					'dob',
					'gender',
          'registrationDate',
          'status'
        ]
      }" 
      :rows="pagination.data.value || []" 
      :cells="{
				dob: (_, {birthDate}) => secondDateFormat(birthDate),
        fullName: (_, row) => {
          return `${row?.title || ''} ${row?.firstName || ''} ${row?.fatherName || ''}`;
        },
				address: (_, row) => `${row?.region || ''} ${row?.woreda || ''}`,
        registrationDate: (date) => {
          return secondDateFormat(date);
        }
      }"
      :Fallback="TableRowSkeleton"
    >
      <template #actions="{ row }">
        <div class="flex gap-2">
          <Button 
            @click="openModal('EditPatient', row)" 
            size="xs" 
            type="edge"
            class="flex items-center gap-1"
          >
            <BaseIcon :path="mdiPencil" :size="16" />
            Edit
          </Button>
          <!-- <Button 
            @click="deleteCustomer(row.id)" 
            size="xs" 
            type="danger"
            class="flex items-center gap-1"
          >
            <BaseIcon :path="mdiDelete" :size="16" />
            Delete
          </Button> -->
        </div>
      </template>
    </Table>
  </DefaultPage>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import Table from '@/components/Table.vue';
import Button from '@/components/Button.vue';
import BaseIcon from '@/components/base/BaseIcon.vue';
import TableRowSkeleton from '@/skeletons/TableRowSkeleton.vue';
import { mdiPlus, mdiPencil, mdiDelete } from '@mdi/js';
import { usePagination } from '@/composables/usePagination';
import { formatAgeToFraction, secondDateFormat } from '@/utils/utils';
import DefaultPage from '@/components/DefaultPage.vue';
import { getPatients } from '../../doctor/api/patientApi';
import { openModal } from '@customizer/modal-x';

const searchTerm = ref('');

const mockCustomers = ref([
  {
    id: '1',
    title: 'Mr',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phoneNumber: '+1 ************',
    address: '123 Main St, New York, NY',
    registrationDate: '2023-01-15',
    status: 'Active'
  },
  {
    id: '2',
    title: 'Mrs',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    phoneNumber: '+1 ************',
    address: '456 Oak Ave, San Francisco, CA',
    registrationDate: '2023-02-20',
    status: 'Active'
  },
  {
    id: '3',
    title: 'Ms',
    firstName: 'Emily',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phoneNumber: '+1 ************',
    address: '789 Pine Rd, Boston, MA',
    registrationDate: '2023-03-10',
    status: 'Inactive'
  },
  {
    id: '4',
    title: 'Dr',
    firstName: 'Michael',
    lastName: 'Brown',
    email: '<EMAIL>',
    phoneNumber: '+1 ************',
    address: '101 Cedar Ln, Chicago, IL',
    registrationDate: '2023-04-05',
    status: 'Active'
  },
  {
    id: '5',
    title: '',
    firstName: 'Sarah',
    lastName: 'Wilson',
    email: '<EMAIL>',
    phoneNumber: '****** 567 8905',
    address: '202 Maple Dr, Seattle, WA',
    registrationDate: '2023-05-12',
    status: 'Active'
  }
]);

const filteredCustomers = computed(() => {
  if (!searchTerm.value) return mockCustomers.value;
  
  const search = searchTerm.value.toLowerCase();
  return mockCustomers.value.filter(customer => 
    `${customer.firstName} ${customer.lastName}`.toLowerCase().includes(search) ||
    customer.email.toLowerCase().includes(search) ||
    customer.phoneNumber.includes(search) ||
    customer.address.toLowerCase().includes(search)
  );
});

// Mock pagination using the usePagination composable
const pagination = usePagination({
  cb: getPatients
});

const editCustomer = (customer) => {
  openModal('EditCustomer', customer);
};

const deleteCustomer = (id) => {
  console.log(`Deleting customer with ID: ${id}`);
  mockCustomers.value = mockCustomers.value.filter(customer => customer.id !== id);
};

</script>
