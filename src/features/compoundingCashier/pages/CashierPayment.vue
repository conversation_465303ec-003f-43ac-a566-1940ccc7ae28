<script setup>
import { useRoute } from "vue-router";
import { useApiRequest } from "@/composables/useApiRequest";
import Icon from '@components/Icon.vue'
import LayeredPages from "@/components/LayeredPages.vue";
import CompoundingPayment from "../components/CompoundingPayment.vue";
import CompoundingSelectedDrugListPAge from "@/features/compounding/components/CompoundingSelectedDrugListPAge.vue";
import CompoundingSearchSelectedDrug from "@/features/compounding/components/CompoundingSearchSelectedDrug.vue";
import CompoundingD2DInteractionPage from "@/features/compounding/components/CompoundingD2DInteractionPage.vue";
import { useCompoundingStore } from "@/features/compounding/store/compoundingStore";
import CompoundingPatientDetail from "@/features/compounding/components/CompoundingPatientDetail.vue";

const compoundingStore = useCompoundingStore();
const route = useRoute();
const paymentReq = useApiRequest();

if (!compoundingStore.selectedPatient) {
  paymentReq.send(
    () => getPrescriptionByUuid(route.params.uuid),
    (res) => {
      if (res.success) {
        compoundingStore.setData({ ...res.data, prescriptionUuid: route.params.uuid });
      }
    }
  );
}
</script>
<template>
  <div class="flex gap-1 h-full bg-base-clr-3 overflow-hidden __with-patient-grid">
    <div class="relative gap-2 flex h-full overflow-hidden">
      <CompoundingPatientDetail :actions="false" class="w-1/2" v-if="compoundingStore.selectedPatient" />
      <LayeredPages :class="[compoundingStore.selectedPatient && '!w-1/2']" transitionName="search"
        :baseAnimation="false" :pages="[
          {
            name: 'list',
            component: CompoundingSelectedDrugListPAge,
            props: {
              showDetail: false,
              class: 'rounded-tl',
              actions: false
            }
          },
          {
            name: 'search',
            component: CompoundingSearchSelectedDrug,
          },
          {
            name: 'd2d',
            component: CompoundingD2DInteractionPage,
          }
        ]" />
      <!-- <SelectedDrugsList :remove="false" :actions="false" class="!w-1/2" v-if="compoundingStore.selectedPatient"
        @drug:selected="(drug) => compoundingStore.setActiveDrug(drug)"
        :active="compoundingStore.activeDrug?.product_id" /> -->
      <div v-if="paymentReq.pending.value" class="inset-0 z-20 bg-base-clr-2 grid place-items-center absolute">
        <Icon :width="50" :height="50" icon="svg-spinners:3-dots-scale" class="text-2xl" />
      </div>
    </div>
    <div class="flex w-full justify-center flex-1 h-full overflow-hidden">
      {{ console.log(compoundingStore.compounding) }}
      <CompoundingPayment class="w-full" :data="{
        total_payment_amount: compoundingStore.compounding.total_payment_amount,
        drugPrescriptions: compoundingStore.selectedDrugList,
        patient: compoundingStore.selectedPatient
      }" :prescriptionUuid="route.params.uuid" />
    </div>
  </div>
</template>
