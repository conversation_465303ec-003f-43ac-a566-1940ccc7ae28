<script setup>
import Button from "@/components/Button.vue";
import Table from "@/components/Table.vue";
import { usePagination } from "@/composables/usePagination";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import { formatCurrency, removeUndefined } from "@/utils/utils";
import { useRouter } from 'vue-router'
import { openModal } from "@customizer/modal-x";
import { searchInvoice } from "../api/CompoundinggetInvoice";
import { useCompoundingStore } from "@/features/compounding/store/compoundingStore";

const router = useRouter()
const compoundingStore = useCompoundingStore();
const pagination = usePagination({
  cb: (data) =>
    searchInvoice(
      removeUndefined({
        search: data.search,
        status: "REQUESTED",
      })
    ),
});

function fetchDrugs(ev) {
  pagination.search.value = ev.target.value;
}

function setPatien(data) {
  compoundingStore.setData(data);
  router.push({
    name: "CashierPayment",
    params: {
      uuid: data?.prescriptionUuid,
    },
  });
}
</script>
<template>
  <div class="flex h-full pt-8">
    <div class="flex justify-center flex-1">
      <div class="flex flex-col gap-4 w-[90%] h-full">
        <div
          class="flex justify-between gap-2 items-center px-2 rounded border-primary border-2 min-h-drug-search-height">
          <input @input="fetchDrugs" autofocus class="px-2 bg-transparent flex-1 h-12" placeholder="Search Patient" />
          <Button size="md" type="primary"> Search </Button>
        </div>
        <div v-if="!pagination.pending.value && !pagination.data.value?.length"
          class="flex-1 flex flex-col gap-12 items-center">
          <img src="/src/assets/img/search_med.svg" />
          <div class="flex flex-col gap-4 items-center">
            <p class="font-medium text-md">
              Please search medicine availability and price.
            </p>
            <p class="text-xs px-20">
              Note: you can search a medicine using medicine or generic name.
            </p>
          </div>
        </div>
        <Table v-else :pending="pagination.pending.value" :Fallback="TableRowSkeleton" :headers="{
          head: [
            'Patient Name',
            'Phone Number',
            'Active Ingredient',
            'Base Ingredient',
            'Total Quantity',
            'Total Price',
            'Status',
            'actions',
          ],
          row: [
            'fullName',
            'patient.mobilePhone',
            'activeIngredient',
            'bases.name',
            'totquantity',
            'total_payment_amount',
            'status'
          ],
        }" :rows="pagination.data.value || []" :cells="{
          fullName: (_, row) => {
            return `${row?.patient?.firstName} ${row?.patient?.fatherName}`
          },
          activeIngredient: (_, row) => {
            return row?.ingredients.map((el) => el?.name).join(',');
          },
          drugPrice: (_, row) => {
            return formatCurrency((row.drugPrescriptions || []).reduce((accumulator, currentValue) =>
              accumulator + currentValue.drugPrice, 0))
          },
          total_payment_amount: (_, row) => {
            return formatCurrency((Math.ceil(row.total_payment_amount * 100) / 100).toFixed(2));
          },
          quantity: (_, row) => {
            return `${row?.bases?.quantity} ${row?.bases.unit}`
          },
          totquantity: (_, row) => {
            const baseQuantity = row?.bases ? row?.bases?.quantity : 0;
            const baseUnit = row?.bases?.unit

            const ingredientQuantities = row?.ingredients?.reduce((sum, el) => sum += el?.quantity, 0)

            return `${baseQuantity + ingredientQuantities} ${baseUnit}`
          }
        }">
          <template #actions="{ row }">
            <div class="flex gap-4">
              <Button @click.stop="openModal('CompoundingCancelOrder', {
                prescriptionUuid: row?.prescriptionUuid
              })" class="text-sm border border-error text-error" size="xs">
                Return Order
              </Button>
              <Button @click.stop="setPatien(row)" class="text-sm bg-dark text-white" size="xs">
                Payment
              </Button>
            </div>
          </template>
        </Table>
      </div>
    </div>

  </div>
</template>
<style scoped></style>
