<script setup>
import Icon from "@/components/Icon.vue";
import { ref, watch } from "vue";
import { Input, Form, Select, Textarea } from "@components/new_form_elements";
import Button from "@/components/Button.vue";
import { useAuth } from "@/stores/auth";
import { formatCurrency, toasted } from "@/utils/utils";
import { useApiRequest } from "@/composables/useApiRequest";
import { openModal } from '@customizer/modal-x'
import { useRouter } from "vue-router";
import { updatePrescription } from "../api/CompoundinggetInvoice";
import { useCompoundingStore } from "@/features/compounding/store/compoundingStore";

const props = defineProps({
  prescriptionUuid: {
    type: String,
  },
  data: {
    type: Object,
  },
});
const compoundingStore = useCompoundingStore();
const router = useRouter();
const cash = ref(!props.data?.patient?.institutionName);
const toggle = ref(false);
const auth = useAuth();
const prescription = useApiRequest();

function send(data) {
  prescription.send(
    () =>
      updatePrescription(
        props?.prescriptionUuid,
        { status: "PAID", ...data, paymentType: data.type }
      ),
    (res) => {
      toasted(res.success, "Successfully paid", res.error);
      if (res.success) {
        toggle.value = true;
        router.push({
          name: "Compoundingcashier Pdf",
          params: {
            uuid: props?.prescriptionUuid,
          },
        });
        // router.push("/cashier/pdf/" + props?.prescriptionUuid)
      }
    }
  );
}

function pay({ values }) {
  compoundingStore.compounding.fsNumber = values.fsNumber
  if (cash.value) {
    openModal('PaymentType', {}, type => {
      console.log(type)
      if (type) {
        send({type, ...values})
      }
    })
    return
  }

  send({type: 'Credit', ...values})
}

function togglePayment() {
  if (!props.data?.patient?.institutionName) {
    cash.value = true
    toasted(false, '', 'No Credit Available')
  } else {
    cash.value = !cash.value
  }
}

console.log(props.data);

</script>
<template>
  <div class="max-w-full h-full flex flex-col overflow-hidden">
    <div
      class="text-base-clr-2 flex justify-between px-7 items-center min-h-drug-search-height bg-secondary rounded-md">
      <p class="font-medium">Payment</p>
      <p class="font-medium">
        From: {{ auth?.auth?.user?.firstName }}
        {{ auth?.auth?.user?.fatherName }}
      </p>
    </div>

    <div v-if="!toggle"
      class="bg-white h-full px-8 pt-6 gap-6 flex flex-col flex-1 show-scrollbar overflow-y-auto overflow-x-hidden">
      <div @click="togglePayment"
        class="select-none relative cursor-pointer isolate flex min-h-12 rounded border-2 border-primary">
        <div :class="[cash ? 'text-white' : '']" class="w-1/2 flex items-center justify-center">
          Cash
        </div>
        <div :class="[!cash ? 'text-white' : '']" class="w-1/2 flex-1 flex items-center justify-center">
          Credit
        </div>
        <div :class="[cash ? 'left-0' : 'left-[49%]']"
          class="absolute transition-all duration-150 ease-in-out w-full -z-10 px-1 flex justify-start items-center top-0 h-full">
          <div class="w-1/2 h-10 bg-primary rounded"></div>
        </div>
      </div>
      <div v-if="!cash" class="bg-base-clr-3 flex gap-4 rounded-lg min-h-12 items-center px-4">
        <p class="border-r border-dark px-4 opacity-60">Institution</p>
        <p>{{ data?.patient?.institutionName }}</p>
      </div>
      <div class="bg-base-clr-3 flex flex-col rounded-lg min-h-20 justify-center px-4">
        <p class="opacity-60 font-bold">Total</p>
        <p class="font-bold">
          {{
            formatCurrency(data.total_payment_amount)
          }}
        </p>
      </div>
      <Form class="flex flex-col gap-4" v-slot="{ submit }" id="payment-form">
        <Input label="Serial Number *" :attributes="{ placeholder: 'Serial Number' }" name="fsNumber"
          validation="required|num" />
        <Textarea label="Remark" :attributes="{ placeholder: 'Type your remark here' }" name="remark" />

        <div class="py-4 border-t mt-2 flex items-center gap-2 justify-end">
          <Button :pending="prescription.pending.value" size="sm" @click.prevent="submit(pay)" type="secondary">
            Confirm Payment
          </Button>
          <!-- <Button size="sm" @click.prevent="$router.push('/cashier/payment/')" type="primary">Generate PDF</Button> -->
        </div>
      </Form>
    </div>
    <div v-else class="flex-1 flex flex-col text-center items-center gap-4">
      <img src="/src/assets/img/Group.svg" />
      <div class="p-4 px-14 flex flex-col gap-4">
        <p class="font-bold text-md">Payment Confirmed Successfully!</p>
        <p class="text-xs w-72">
          Click on “Another Order” to process another Prescription
        </p>
        <Button @click.stop="$router.push('/cashier')" type="secondary">Another Order</Button>
      </div>
    </div>
  </div>
</template>
