<script setup>
import Button from "@/components/Button.vue";
import Icon from "@/components/Icon.vue";
import { ref, watch } from "vue";
import { formatCurrency } from "@/utils/utils";
import { useAuth } from "@/stores/auth";
const auth = useAuth();
const props = defineProps({
  action: {
    type: String,
  },
  data: {
    type: Object,
  },
});

const data = ref([]);
watch(props, () => {
  data.value.push(props.data.drugPrescriptions);
});
</script>
<template>
  <div class="w-full h-full pb-4 overflow-hidden flex flex-col justify-between bg-white">
    <div class="flex flex-col gap-6">
      <div class="text-base-clr-2 flex justify-between px-7 items-center min-h-drug-search-height bg-secondary rounded">
        <p class="font-medium">Drug List</p>
        <Icon color="rgb(var(--base-clr-2))" name="mynaui:search" />
      </div>
      <div v-for="(drug, index) in props.data?.drugPrescriptions" :key="drug.value" class="flex flex-col gap-4 px-8">
        <div v-if="drug.instruction && drug.drugName && drug.frequency"
          class="border-error bg-base-clr-3 border rounded-md h-full flex flex-col p-4">
          <div class="flex gap-2">
            {{ index + 1 }}
            <p>{{ drug.drugName }}</p>
          </div>
          {{ drug }}
          <div class="flex flex-col gap-2 px-4">
            <p class="text-primary">{{ formatCurrency(drug.sellingUnitPrice) }}</p>
          </div>
          <ul class="list-disc px-8">
            <li>{{ drug.instruction }}</li>
            <li>{{ drug.dose }}</li>
            <li>
              {{ drug.frequency.frequencyRate }}
              {{ drug.frequency.frequencyUnit }}
            </li>
          </ul>
        </div>

        <div v-else class="flex flex-col">
          <div class="flex gap-2">
            {{ index + 1 }}
            <p>{{ drug.drugName }}</p>
          </div>
          <p class="text-error">{{ drug.price }}</p>
          <ul class="list-disc px-4">
            <li class="opacity-80">No Details Added</li>
          </ul>
        </div>
      </div>
    </div>
    <div v-if="props.action == 'Dispense'" class="flex justify-center">
      <Button class="border border-dark">Return to Pharmacist </Button>
    </div>
    <div v-else class="bg-base-clr-3 mx-8 rounded-md px-3 py-2 flex flex-col gap-2">
      <p>Cashier: Girma Lemma</p>
      <p>Pharmacist: Meles Lemma</p>
    </div>
  </div>
</template>
