<script setup>
import BaseIcon from '@/components/base/BaseIcon.vue';
import Button from '@/components/Button.vue';
import { usePaperPrescriptionStore } from '@/features/paper_prescription/store/paperPrescriptionStore';
import { formatAgeToFraction } from '@/utils/utils';
import { mdiDownload } from '@mdi/js';
import html2pdf from 'html2pdf.js';
import { ref } from 'vue';

const prescription = usePaperPrescriptionStore()
const patient = prescription.selectedPatient;
const drugs = prescription.selectedDrugList;
const totalPrice = prescription.paper_prescription.totalPrice;

const invoiceRef = ref(null); // Reference for the invoice section

console.log(patient);
console.log(drugs);
console.log(totalPrice);

const downloadPDF = () => {
    const element = invoiceRef.value;
    const opt = {
        filename: 'invoice.pdf',
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 1.5 },
        jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
    };

    // Use html2pdf to generate the PDF
    html2pdf().from(element).set(opt).save();
};





</script>

<template>
    <div ref="invoiceRef" class="grid grid-cols-2 gap-4 items-center px-24 top-0">
        <div class="flex flex-col gap-2 ">
            <img src="/src/assets/img/loginalert.png" alt="alert" class="pb-10 object-cover w-[8rem]" />
            <p>Alert Hospital</p>
            <p>Address</p>
            <p>phone number</p>
        </div>
        <div class="flex flex-col gap-2 items-center">
            <h1 class="opacity-50 pb-20 p-10">INVOICE</h1>
            <p class="underline underline-offset-[3px] pb-8">{{ new Date().toLocaleDateString() }}</p>
            <p class="underline pb-20">INVOICE NO.</p>
            <p class="opacity-50">payment due upon receipt.</p>
        </div>
        <div class="flex flex-col gap-2 pt-10">
            <p>Title: {{ patient?.title }}</p>
            <p>Patient: {{ patient?.firstName }} {{ patient?.fatherName }} {{ patient?.grandFatherName }}</p>
            <p>Age: {{ formatAgeToFraction(patient?.age) }}</p>
            <p>Gender: {{ patient.gender }}</p>
            <p>Mobile Phone: {{ patient?.mobilePhone }}</p>
        </div>
        <div class="col-span-2 pt-0">
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(drug, index) in drugs" :key="drug.inventoryUuid">
                        <td>
                            <strong>Drug Name:</strong>{{ drug?.drugName }}<br />
                            <strong>Dose:</strong> {{ drug?.dose }}<br />
                            <strong>Frequency:</strong> {{ drug?.frequency }}<br />
                        </td>
                        <td>
                            <strong>Drug Price:</strong> {{ drug?.drugPrice }}
                        </td>
                    </tr>
                    <tr class="font-bold">
                        <td>Remarks/Instructions</td>
                        <td>Total: {{ totalPrice }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div>
            <p class="font-bold"></p>
        </div>
        <div class="flex items-center">
            <p class="font-bold"></p>
        </div>
    </div>
    <div class="flex justify-end gap-5 p-10">
        <Button @click="downloadPDF" size="xs" class="text-sm text-white rounded bg-secondary pt-0">
            <BaseIcon :path="mdiDownload" class="text-white" />
            Download PDF
        </Button>

    </div>


</template>
<style scoped>
.invoice-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.invoice-table th,
.invoice-table td {
    border: 1px solid #ccc;
    padding: 4px;
    text-align: left;
    /* Smaller font size */
}

/* Add a print-specific style */
@media print {

    .invoice-table th,
    .invoice-table td {
        font-size: 9px;
        /* Smaller font size for printing */
        padding: 1px;
        /* Further reduce padding */
    }

    .grid {
        padding: 10px;
        /* Reduce padding of the grid */
    }
}
</style>