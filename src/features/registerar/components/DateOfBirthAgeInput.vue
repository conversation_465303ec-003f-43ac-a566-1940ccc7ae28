<script setup>
import InputParent from '@/components/new_form_builder/InputParent.vue';
import NewInputLayout from '@/components/new_form_elements/NewInputLayout.vue';
import { formatDateToDDMMYY, getAgeFormDate, getDateFromAge } from '@/utils/utils';
import { ref, watch } from 'vue';

const props = defineProps({
	name: {
		type: String,
		required: true
	},
	value: {
		type: String,
	},
	validation: {
    type: [String, Object]
  }
})

const value = ref(props.value || '')
const age = ref('')

watch(age, () => {
	const date = formatDateToDDMMYY(getDateFromAge(age.value))
	value.value = date || formatDateToDDMMYY(new Date())
})
</script>

<template>
	<InputParent :validation="validation" v-model="value" v-slot="{ setRef, error }" :name="name" >
		<NewInputLayout :validation="validation" :error="error" :label="$attrs?.label">
			<div :ref="setRef" class="!pr-0 flex w-full" >
				<input class="flex-1 appearance-none" disabled v-model="value" type="date" />
				<input placeholder="Age" class="w-12 h-full text-center" @input="(ev) => {
					const num = parseInt(ev.target.value)
					if(num > -1 || num <= 120) {
						age = num
					} else {
						ev.target.value = ''
					}
				}" />
			</div>
		</NewInputLayout>
	</InputParent>
</template>