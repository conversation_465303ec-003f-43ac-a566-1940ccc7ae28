<script setup>
import ModalParent from '@/components/ModalParent.vue';
import NewFormModal from '@/components/NewFormModal.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import NewFormParent from '@/features/admin/role/components/NewFormParent.vue';
import { addPatient } from '@/features/paper_prescription/api/patientApi';
import AddPatient from '@/features/paper_prescription/components/prescription_progress/AddPatient.vue';
import PatientForm from '@/features/patients/components/form/PatientForm.vue';
import { toasted } from '@/utils/utils';
import { closeModal } from '@customizer/modal-x';

const req = useApiRequest()
function add(values) {
	if(req.pending.value) return
	
	
	console.log(values)
	
	req.send(
		() => addPatient(values),
		res => {
			toasted(res.success, 'Succesfully Added a Patient', res.error)
			if(res.success) {
				closeModal()
			}
		}
	)
}
</script>

<template> 
	<ModalParent class="grid place-items-center p-4 bg-black/50" >
		<NewFormParent size='lg' title="Add Patient" >
			<PatientForm
				btnText="Add"
				:pending="req.pending.value"
				:addPatient="add"
			/>
		</NewFormParent>
	</ModalParent>
</template>