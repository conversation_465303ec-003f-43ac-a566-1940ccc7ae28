<script setup>
import { useStockStore } from "@/features/StoreKeeper/store/StockStore";
import { secondDateFormat } from "@/utils/utils";
import { computed } from "vue";

const props = defineProps({
  row: Object,
});
const stockStore = useStockStore();

const isExpired = stockStore.checkExpirdity(props.row?.drugUuid);
</script>
<template>
  <div>
    <span :style="{ color: isExpired ? 'red' : 'Green' }">
      {{ secondDateFormat(row.expDate) }}
    </span>
  </div>
</template>

<!-- <span :style="{ color: isExpired ? 'red' : 'Green' }">
    {{ secondDateFormat(row?.expDate) }}
</span> -->
