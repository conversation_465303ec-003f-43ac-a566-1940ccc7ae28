<script setup>
import Button from "@/components/Button.vue";
import NewFormParent from "@/features/admin/role/components/NewFormParent.vue";
import { useStockStore } from "@/features/StoreKeeper/store/StockStore";
import Form from "@/new_form_builder/Form.vue";
import icons from "@/utils/icons";
import { formatCurrency, openNewTab, toasted } from "@/utils/utils";
import { useApiRequest } from "@/composables/useApiRequest";
import { closeModal, openModal } from "@customizer/modal-x";
import { ref, watch } from "vue";
import { useRegularRequest } from "../store/regularRequest";
import { regularRequest } from "../api/regularRequestApi";
import { usePagination } from "@/composables/usePagination";
import { checkMedicineAvailability } from "../api/stockApi";
import { RequestOptions } from "../../../utils/utils";

const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
});

const stockStore = useStockStore();
const request = useApiRequest();

const pagination = usePagination({
  auto: false,
  cb: (data) =>
    checkMedicineAvailability({
      ...data,
      limit: 2000,
    }),
});

const drugs = ref([]);
if (!props.data?.length) {
  pagination.send();
} else {
  drugs.value = props.data.map((el) => {
    return {
      ...el,
      quantityReceived: 0,
      lossAdjustment: 0,
      beginningBalance: el.totalAmount,
      endingBalance: el.totalAmount,
    };
  });
}

watch(pagination.data, () => {
  drugs.value = [...pagination.data.value?.content].map((el) => {
    return {
      ...el,
      quantityReceived: 0,
      lossAdjustment: 0,
      beginningBalance: el.totalAmount,
      endingBalance: el.totalAmount,
    };
  });
});

const regularRequestStore = useRegularRequest();
const baseURL = import.meta.env?.v_BASE_URL;

const regularReq = useApiRequest();
function drugrequest() {
  if (regularReq.pending.value) return;
  openModal(
    "Confirmation",
    {
      title: "Request",
      message: "Are you sure you want to request?",
    },
    (res) => {
      if (res) {
        regularReq.send(
          () =>
            regularRequest(
              {
                status: !props.data?.length
                  ? RequestOptions.Regular
                  : RequestOptions.Emergency,
              },
              drugs.value.map((el) => {
                return {
                  quantity: el.endingBalance,
                  toInventoryUuid: el.inventoryUuid,
                };
              })
            ),
          (res) => {
            toasted(res.success, "Request successfully", res.error);
            if (res.success) {
              regularRequestStore.request = drugs.value;

              openNewTab(`${baseURL}/model20/${res.data}`);
            }
          }
        );
      }
    }
  );
}

console.log(props.data);
</script>
<template>
  <div
    @click.stop="() => {}"
    class="bg-black/50 h-55 p-10 min-h-full w-full grid place-items-center"
  >
    <NewFormParent size="xl" title="Request Store">
      <template #left-actions>
        <i class="text-black" v-html="icons.leftArrow" />
      </template>
      <Form v-slot="{ submit }" id="quantity" class="flex flex-col gap-4">
        <div class="flex items-center justify-between">
          <p>List of Requests</p>
          <Button
            :pending="request.pending.value"
            @click.prevent="submit(drugrequest)"
            type="primary"
          >
            Submit
          </Button>
        </div>
        <table class="border rounded-md">
          <thead>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th class="max-w-12" rowspan="3">Ser. No.</th>
              <th rowspan="3">Item</th>
              <th rowspan="3">Unit</th>
              <th colspan="7">Completed By Unit</th>
            </tr>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th colspan="4">Beginning Balance</th>
              <th>Quantity Received</th>
              <th>Loss/Adjustment</th>
              <th>Ending Balance</th>
            </tr>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th colspan="4">A</th>
              <th>B</th>
              <th>C</th>
              <th>D</th>
            </tr>
          </thead>
          <tbody>
            <template v-if="drugs.length > 0">
              <tr
                class="*:text-center *:border text-sm *:p-1"
                v-for="(item, idx) in drugs || []"
                :key="item.drugName"
              >
                <td class="max-w-12">{{ idx + 1 }}</td>
                <td>{{ item.drugName }}</td>
                <td>{{ item?.drugUnit }}</td>
                <td colspan="4">
                  <input
                    key="one"
                    v-model="item.beginningBalance"
                    class="text-center w-12"
                  />
                </td>
                <td>0</td>
                <td>0</td>
                <td>
                  <input
                    key="two"
                    v-model="item.endingBalance"
                    class="text-center w-12"
                  />
                </td>
              </tr>
            </template>
            <tr v-else>
              <td colspan="10" clss="p-4">
                <div class="flex flex-col items-center justify-center">
                  <i class="*:size-56" v-html="icons.no_data" />
                  No Data
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </Form>
    </NewFormParent>
  </div>
</template>
