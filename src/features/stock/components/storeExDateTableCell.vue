<script setup>
import { useExpiredStock } from '@/features/StoreKeeper/store/ExpiredStore';
import { secondDateFormat } from '@/utils/utils';

const props = defineProps({
    row: Object
})
const expiredstore = useExpiredStock()

const isExpired = expiredstore.checkExpirdity(props.row?.drugUuid);
</script>
<template>
    <div>
        <span :style="{ color: isExpired ? 'red' : 'Green' }">
            {{ secondDateFormat(row.expDate) }}
        </span>
    </div>
</template>

<!-- <span :style="{ color: isExpired ? 'red' : 'Green' }">
    {{ secondDateFormat(row?.expDate) }}
</span> -->