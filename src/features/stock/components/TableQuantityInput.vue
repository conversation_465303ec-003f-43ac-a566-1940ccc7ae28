<script setup lang="ts">
import InputParent from '@/new_form_builder/InputParent.vue';
import { ref } from 'vue';

const props = defineProps({
	value: {
		type: Object,
		required: true
	},
	name: {
		type: String,
		requried: true
	}
})

const value = ref({
	toInventoryUuid: props.value?.inventoryUuid,
	quantity: 0,
})

</script>

<template>
	<InputParent :name="name" v-model="value" v-slot="{ setRef }">
		<div :ref="setRef">
			<input v-model="value.quantity" />
		</div>
	</InputParent>
</template>