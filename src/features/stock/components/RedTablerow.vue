<script setup>
import { ref, watch, computed } from "vue";
import Button from "@components/Button.vue";
import { genId } from "@/utils/utils";
import { usePaperPrescriptionStore } from "@paper_prescription/store/paperPrescriptionStore";
import { useRouter } from "vue-router";
import CompoundingD2DInteractionPage from "@/features/compounding/components/CompoundingD2DInteractionPage.vue";
import D2DInteractionPage from "@/features/paper_prescription/components/D2DInteractionPage.vue";
import { mdiArrowDown } from "@mdi/js";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { openModal } from "@customizer/modal-x";
import TableNumberCell from '@/components/TableNumberCell.vue'
import { isVueComponent } from "@/utils/componentUtils";

const props = defineProps({
  modelValue: {
    required: false,
  },
  headKeys: {
    type: Array,
    required: true,
  },
  rowData: {
    type: Array,
    required: true,
  },
  rowKeys: {
    type: Array,
    required: true,
  },
  cells: Object,
  page: {
    type: Number,
    default: 1,
  },
  perPage: {
    type: Number,
    default: 25,
  },
});

const emit = defineEmits(["row", "update:modelValue"]);

const router = useRouter();
const paperPrescriptionStore = usePaperPrescriptionStore();

const rows = ref(
  (props.rowData || []).map((el) => ({ ...el, id: genId.next().value }))
);

watch(props, () => {
  rows.value = (props.rowData || []).map((el) => ({
    ...el,
    id: genId.next().value,
  }));
});

const selected = ref(props.modelValue || []);
function toggle(checked, data) {
  const idx = selected.value.findIndex(
    (el) => el.inventoryUuid == data.inventoryUuid
  );
  if (idx == -1 && checked) {
    selected.value.push(data);
  } else {
    selected.value.splice(idx, 1);
  }
}

function valueIncluded(data) {
  return selected.value.find((el) => el.inventoryUuid == data.inventoryUuid);
}

watch(
  selected,
  () => {
    console.log(selected.value, "here");
    emit("update:modelValue", selected.value);
  },
  { deep: true }
);

watch(
  () => props.modelValue,
  () => {
    selected.value = props.modelValue;
  }
);
</script>
<template>
  <tbody>
    <template :key="row.id" v-for="(row, index) in rows">
      <tr
        @click="emit('row', row)"
        :class="[row.totalAmount > row.minima ? 'bg-white ' : 'bg-[#FFEAEA]']"
        class="cursor-pointer hover:bg-gray-300 border-x-[0.2px] border-b-[0.2px] border-t-[0.2px]"
      >
        <TableNumberCell :page="page" :perPage="perPage" :index="index" />

        <td class="p-2 max-w-40" :key="key" v-for="key in rowKeys">
          <span v-if="!Object.keys(cells || {}) || !cells?.[key]">
            {{
              key.split(".").reduce((all, el) => {
                return all?.[el];
              }, row)
            }}
          </span>
          <component
            :key="key"
            v-else-if="Object.keys(cells || {}) && isVueComponent(cells[key])"
            :row="row"
            :is="cells[key]"
          />
          <span v-else-if="typeof cells[key] == 'function'">
            {{ cells[key](row?.[key], row) }}
          </span>
        </td>
        <td class="p-3 flex items-center gap-3">
          <!-- <Button size="xs" @click.prevent="openModal('AddMaxandMin', row)"
                        class="text-sm truncate text-white rounded bg-primary flex items-center">
                        <BaseIcon :path="mdiArrowDown" class="text-white" />
                        Stock Limit
                    </Button> -->
          <input
            class="size-6"
            :checked="valueIncluded(row)"
            type="checkbox"
            @change="toggle($event.target.checked, row)"
          />
          <div class="flex items-center gap-2">
            <Button type="edge" size="xs">Bin Card</Button>
            <Button
              @click="openModal('LossAdjustment', row)"
              type="edge"
              size="xs"
              >L / Ad</Button
            >
          </div>
        </td>
      </tr>
    </template>
  </tbody>
</template>
