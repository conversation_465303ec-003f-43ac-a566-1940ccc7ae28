<script setup>
import Button from "@/components/Button.vue";
import Input from "@/components/new_form_elements/Input.vue";
import Select from "@/components/new_form_elements/Select.vue";
import Textarea from "@/components/new_form_elements/Textarea.vue";
import Form from "@/new_form_builder/Form.vue";
import { useForm } from "@/new_form_builder/useForm";
import { LOSS_ADJEstment_STATUS } from "@/utils/utils";
import { ref } from "vue";

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  onSubmit: {
    type: Function,
    default: () => {},
  },
});

const { submit } = useForm("lossAdjustmentForm");

const adjustmentTypes = ref([
  {
    label: 'LOSS',
    value: LOSS_ADJEstment_STATUS.REQUESTED_LOSS
  },
  {
    label: 'ADJUSTMENT',
    value: LOSS_ADJEstment_STATUS.POSITIVE_ADJUSTMENT
  }
]);
const lossOptions = {
  "REQUESTED_LOSS": ['Expired', 'Damaged', 'Stolen', 'Transfer'],
  "POSITIVE_ADJUSTMENT": ['Found', 'Recieve Transfer',] 
}

// Default to REQUESTED_LOSS for new adjustments
const selectedType = ref(props.data?.inventoryStatus || "REQUESTED_LOSS");
function submitForm({ values, reset }) {
  props.onSubmit(values, reset);
}
</script>

<template>
  <Form class="grid grid-cols-2 gap-4" id="lossAdjustmentForm">
    <Select
      :obj="true"
      name="inventoryStatus"
      label="Type"
      validation="required"
      v-model="selectedType"
      :options="adjustmentTypes"
      :attributes="{
        placeholder: 'Select adjustment type',
      }"
    />
    <Input
      name="amount"
      label="Quantity"
      validation="required|num"
      :value="data?.amount"
      :attributes="{
        placeholder: 'Enter adjustment amount',
        min: '1',
        max: data?.totalAmount || 999999,
      }"
    />
    <div class="col-span-2">
      <Select
        name="remark"
        label="Reason"
        :options="lossOptions[selectedType]"
        validation="required|alpha2"
        :value="data?.remark"
        :attributes="{
          placeholder: 'Enter reason for adjustment',
        }"
      />
    </div>
  </Form>
</template>
