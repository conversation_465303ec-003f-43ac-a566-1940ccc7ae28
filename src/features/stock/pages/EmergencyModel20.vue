<script setup>
import { useApiRequest } from "@/composables/useApiRequest";
import { useRoute } from "vue-router";
import { getRequestByBatch } from "../api/regularRequestApi";
import Model20Pdf from "@/components/pdf/Model20.pdf.vue";

const route = useRoute();
const batchUuid = route.params?.batchUuid;

const req = useApiRequest();

console.log(batchUuid);

req.send(
  () => getRequestByBatch(batchUuid),
  (res) => {
    console.log(res.data);
    if (res.success) {
      req.response.value = (req.response.value?.inventoryResponses || []).reduce(
        (drugs, drug, idx) => {
          const row = [idx + 1];
          let A, B, C, D, E, F, G, H;
          A = drug?.totalAmount;
          B = 0;
          C = 0;
          D = drug?.quantity;
          E = 0;
          F = 0;
          G = 0;
          H = drug?.quantity;
          row.push(drug?.drugCode || " ");
          row.push(
            `${drug?.drugName}, ${drug?.dosageForm}, ${drug?.unit}, ${drug?.drugBrandName}`
          );
          row.push(A);
          row.push(B);
          row.push(C);
          row.push(D);
          row.push(E);
          row.push(F);
          row.push(G);
          row.push(H);
          row.push(" ");
          drugs.push(row);
          return drugs;
        },
        []
      );
    }
  }
);
</script>
<template>
  <Model20Pdf v-if="!req.pending.value" :batch="req.response.value" />
</template>
