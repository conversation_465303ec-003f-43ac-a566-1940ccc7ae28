<script setup>
import { useApiRequest } from "@/composables/useApiRequest";
import { useRoute } from "vue-router";
import { getRequestByBatch } from "../api/regularRequestApi";
import Model22Pdf from "@/components/pdf/Model22.pdf.vue";

const route = useRoute();
const batchUuid = route.params?.batchUuid;

const req = useApiRequest();

req.send(
  () => getRequestByBatch(batchUuid),
  (res) => {
    console.log(res.data);
    if (res.success) {
      req.response.value = (req.response.value?.inventoryResponses || []).reduce(
        (drugs, drug, idx) => {
          let A, B, C, D, E, F, G, H;
          A = drug?.totalAmount;
          B = 0;
          C = 0;
          D = drug?.quantity;
          E = A + (B + C) - D;
          F = E * 2;
          G = F - D;
          H = G;

          const price = drug?.unitPrice;
          const totalPrice = price * D;
          const retailPrice = drug?.retailPrice;

          const row = [idx + 1];
          row.push(drug?.drugCode || " ");
          row.push(`${drug?.drugName}, ${drug?.unit}, ${drug?.drugBrandName}`);
          row.push(drug?.unit);
          row.push(D);
          row.push(`${price}`.split(".")?.[0] ?? "0");
          row.push(`${price}`.split(".")?.[1] ?? "0");
          row.push(`${totalPrice}`.split(".")?.[0] ?? "0");
          row.push(`${totalPrice}`.split(".")?.[1] ?? "0");
          row.push(`${retailPrice}`.split(".")?.[0] ?? "0");
          row.push(`${retailPrice}`.split(".")?.[1] ?? "0");
          row.push(retailPrice);
          drugs.push(row);
          return drugs;
        },
        []
      );
    }
  }
);
</script>
<template>
  <Model22Pdf v-if="!req.pending.value" :batch="req.response.value" />
</template>
