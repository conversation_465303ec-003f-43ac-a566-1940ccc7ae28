<script setup>
import BaseIcon from '@/components/base/BaseIcon.vue';
import Button from '@/components/Button.vue';
import Table from '@/components/Table.vue';
import { usePagination } from '@/composables/usePagination';
import { useApiRequest } from '@/composables/useApiRequest';
import { mdiMagnify, mdiCheck, mdiClose } from '@mdi/js';
import { onMounted } from 'vue';
import { getAllLossAdjustments, approveLossAdjustment, rejectLossAdjustment } from '../api/lossAdjustmentApi';
import { useLossAdjustmentStore } from '../store/lossAdjustmentStore';
import { formatCurrency, secondDateFormat, toasted } from '@/utils/utils';
import { openModal } from '@customizer/modal-x';
import TableRowSkeleton from '@/skeletons/TableRowSkeleton.vue';

const lossAdjustmentStore = useLossAdjustmentStore();
const approveRequest = useApiRequest();
const rejectRequest = useApiRequest();

// Set up pagination with the getAllLossAdjustments API
const pagination = usePagination({
  cb: (params) => getAllLossAdjustments({ 
    ...params, 
    type: 'REQUESTED_LOSS' // Filter to only show requested losses
  }),
  onSuccess: (data) => {
    lossAdjustmentStore.set(data.content);
  }
});

// Function to approve a loss adjustment
function approveAdjustment(id) {
  openModal('Confirmation', {
    title: 'Approve Loss Adjustment',
    message: 'Are you sure you want to approve this loss adjustment?'
  }, (res) => {
    if (res) {
      approveRequest.send(
        () => approveLossAdjustment(id),
        (res) => {
          if (res.success) {
            // Refresh the data after approval
            pagination.refresh();
          }
          toasted(res.success, "Loss adjustment approved successfully", res.error);
        }
      );
    }
  });
}

// Function to reject a loss adjustment
function rejectAdjustment(id) {
  openModal('Confirmation', {
    title: 'Reject Loss Adjustment',
    message: 'Are you sure you want to reject this loss adjustment?'
  }, (res) => {
    if (res) {
      rejectRequest.send(
        () => rejectLossAdjustment(id),
        (res) => {
          if (res.success) {
            // Refresh the data after rejection
            pagination.refresh();
          }
          toasted(res.success, "Loss adjustment rejected", res.error);
        }
      );
    }
  });
}

// Function to view details of a loss adjustment
function viewDetails(row) {
  openModal('EditLossAdjustment', row, () => {
    pagination.refresh();
  });
}

// Load data when component is mounted
onMounted(() => {
  pagination.refresh();
});
</script>

<template>
  <div class="h-full w-full flex flex-col gap-4 p-6">
    <!-- Header with search and title -->
    <div class="flex justify-between items-center py-4">
      <h1 class="text-2xl font-semibold">Requested Loss Adjustments</h1>
      
      <div class="flex gap-4">
        <!-- Search input -->
        <div class="flex border rounded px-2 h-12 items-center">
          <input 
            v-model="pagination.search.value"
            class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80"
            placeholder="Search by medicine name or batch number" 
          />
          <BaseIcon :path="mdiMagnify" :size="20" />
        </div>
      </div>
    </div>

    <!-- Table of loss adjustments -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <Table 
        :pending="pagination.pending.value" 
        :headers="{
          head: [
            'Medicine Name',
            'Batch Number',
            'Adjustment Amount',
            'Requested By',
            'Request Date',
            'Remark',
            'Actions'
          ],
          row: [
            'drugName',
            'batchNumber',
            'amount',
            'requestedBy',
            'requestDate',
            'remark'
          ]
        }" 
        :rows="lossAdjustmentStore.adjustments || []" 
        :cells="{
          amount: (amount) => {
            return formatCurrency(amount);
          },
          requestDate: secondDateFormat
        }"
        :Fallback="TableRowSkeleton"
      >
        <template #actions="{ row }">
          <div class="flex gap-2">
            <Button 
              @click="approveAdjustment(row.adjustmentUuid)" 
              :pending="approveRequest.pending.value" 
              size="xs" 
              type="primary"
              class="flex items-center gap-1"
            >
              <BaseIcon :path="mdiCheck" :size="16" />
              Approve
            </Button>
            <Button 
              @click="rejectAdjustment(row.adjustmentUuid)" 
              :pending="rejectRequest.pending.value" 
              size="xs" 
              type="danger"
              class="flex items-center gap-1"
            >
              <BaseIcon :path="mdiClose" :size="16" />
              Reject
            </Button>
            <Button 
              @click="viewDetails(row)" 
              size="xs" 
              type="secondary"
            >
              View
            </Button>
          </div>
        </template>
      </Table>
    </div>

    <!-- Pagination controls -->
    <div class="flex justify-between items-center py-4">
      <div class="text-sm text-gray-600">
        Showing {{ pagination.data.value?.pageable?.offset + 1 || 0 }} to 
        {{ Math.min(
          (pagination.data.value?.pageable?.offset || 0) + (pagination.data.value?.pageable?.pageSize || 0),
          pagination.data.value?.totalElements || 0
        ) }} 
        of {{ pagination.data.value?.totalElements || 0 }} entries
      </div>
      
      <div class="flex gap-2">
        <Button 
          @click="pagination.prev()" 
          :disabled="pagination.data.value?.first" 
          size="sm" 
          type="edge"
        >
          Previous
        </Button>
        
        <Button 
          @click="pagination.next()" 
          :disabled="pagination.data.value?.last" 
          size="sm" 
          type="edge"
        >
          Next
        </Button>
      </div>
    </div>
  </div>
</template>
