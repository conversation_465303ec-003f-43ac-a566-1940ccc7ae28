import ApiService from "@/service/ApiService";
import { getQueryFormObject, LOSS_ADJEstment_STATUS } from "@/utils/utils";

const api = new ApiService();
const path = "/stock/inventory/update-loss";

/**
 * Create a new loss adjustment
 * @param {Object} data - The loss adjustment data
 * @returns {Promise} - API response
 */
export function createLossAdjustment(id, data) {
  return api.addAuthenticationHeader().put(`${path}/${id}`, data);
}

/**
 * Get all loss adjustments
 * @param {Object} query - Query parameters
 * @returns {Promise} - API response
 */
export function getAllLossAdjustments(query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path}/all${qr}`);
}

/**
 * Get loss adjustment by ID
 * @param {string} id - The loss adjustment ID
 * @returns {Promise} - API response
 */
export function getLossAdjustmentById(id) {
  return api.addAuthenticationHeader().get(`${path}/${id}`);
}

/**
 * Update loss adjustment
 * @param {string} id - The loss adjustment ID
 * @param {Object} data - The updated data
 * @returns {Promise} - API response
 */
export function updateLossAdjustment(id, data) {
  return api.addAuthenticationHeader().put(`${path}/${id}`, data);
}

/**
 * Delete loss adjustment
 * @param {string} id - The loss adjustment ID
 * @returns {Promise} - API response
 */
export function deleteLossAdjustment(id) {
  return api.addAuthenticationHeader().delete(`${path}/${id}`);
}

/**
 * Approve a requested loss adjustment
 * @param {string} id - The loss adjustment ID
 * @returns {Promise} - API response
 */
export function approveLossAdjustment(id, data) {
  return api.addAuthenticationHeader().put(`${path}/${id}`, {
    ...data,
    inventoryStatus: LOSS_ADJEstment_STATUS.LOSS,
  })
}

/**
 * Reject a requested loss adjustment
 * @param {string} id - The loss adjustment ID
 * @returns {Promise} - API response
 */
export function rejectLossAdjustment(id, data) {
  return api.addAuthenticationHeader().put(`${path}/${id}`, {
    ...data,
    inventoryStatus: LOSS_ADJEstment_STATUS.REQUESTED_LOSS,
  })
}
