import ApiService from "@/service/ApiService";

const api = new ApiService();

const path = '/batch'

export function regularRequest(query = {}, data) {
	return api.addAuthenticationHeader().post(`${path}`, data, {
		params: query
	})
}

export function getRequestByBatch(id) {
	return api.addAuthenticationHeader().get(`${path}/${id}`)
}

export function updateRequestedBatch(id, data, query = {}) {
	return api.addAuthenticationHeader().put(`${path}/${id}`, data, {
		params: query
	})
}

export function getAllRequest(query = {}) {
	return api.addAuthenticationHeader().get(`${path}/all`, {
		params: query
	})
}

