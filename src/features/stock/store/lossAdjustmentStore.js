import { ref } from "vue";
import { defineStore } from "pinia";

export const useLossAdjustmentStore = defineStore("lossAdjustmentStore", () => {
  const adjustments = ref([]);

  /**
   * Set the adjustments data
   * @param {Array} data - The adjustments data
   */
  function set(data) {
    console.log(data);
    adjustments.value = data;
  }

  /**
   * Get all adjustments
   * @returns {Array} - All adjustments
   */
  function getAll() {
    return adjustments.value;
  }

  /**
   * Add a new adjustment
   * @param {Object} data - The adjustment data
   */
  function add(data) {
    adjustments.value.push(data);
  }

  /**
   * Update an adjustment
   * @param {string} id - The adjustment ID
   * @param {Object} data - The updated data
   */
  function update(id, data) {
    const idx = adjustments.value.findIndex((el) => el.inventoryUuid == id);
    if (idx == -1) return;

    adjustments.value.splice(idx, 1, data);
  }

  /**
   * Remove an adjustment
   * @param {string} id - The adjustment ID
   */
  function remove(id) {
    const idx = adjustments.value.findIndex((el) => el.inventoryUuid == id);
    if (idx == -1) return;

    adjustments.value.splice(idx, 1);
  }

  return {
    adjustments,
    getAll,
    update,
    remove,
    set,
    add,
  };
});
