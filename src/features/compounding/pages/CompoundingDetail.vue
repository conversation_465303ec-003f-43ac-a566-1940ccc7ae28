<script setup>
import icons from "@/utils/icons";
import { useCompoundingStore } from "../store/compoundingStore";
import { computed } from "vue";
import IngredientCard from "../components/IngredientCard.vue";
import Button from "@/components/Button.vue";
import { useRouter } from "vue-router";
import { useForm } from "@/new_form_builder/useForm";
import Form from "@/new_form_builder/Form.vue";

const router = useRouter();
const compoundingStore = useCompoundingStore();
const selectedDrugs = computed(() => compoundingStore.selectedDrugList);
console.log(selectedDrugs);

// Filter drugs into two categories
const activeIngredients = computed(() =>
  selectedDrugs.value.filter((drug) => drug.type === "Active")
);
const baseIngredients = computed(() =>
  selectedDrugs.value.filter((drug) => drug.type === "Base")
);

console.log("Active Ingredients:", activeIngredients.value);
console.log("Base Ingredients:", baseIngredients.value);

function goToDetail() {
  if (compoundingStore.selectedDrugList.length) {
    router.push("/compounding/compoundingdetail");
  }
}
</script>

<template>
  <div class="w-full min-h-screen flex justify-center py-10">
    <div class="w-[70%] bg-white shadow-lg rounded-lg">
      <div
        class="bg-secondary text-white p-4 rounded-t-lg flex justify-between items-center"
      >
        <h2 class="text-lg font-semibold">Ingredients List</h2>
        <button class="text-white">
          <i v-html="icons.edit" />
        </button>
      </div>
      <Form
        v-slot="{ submit }"
        id="compounding-form"
        class="grid grid-cols-2 box-border gap-6 mt-14 p-8"
      >
        <div class="box-border bg-gray-200 rounded-md p-3">
          <div class="">
            <template
              v-for="(drug, idx) in activeIngredients"
              :key="drug.inventoryUuid"
            >
              <IngredientCard
                :drug="drug"
                :num="idx + 1"
                :on-remove="compoundingStore.removeDrug"
              />

              <!-- Add separator between items, but not after the last one -->
              <div
                v-if="idx < activeIngredients.length - 1"
                class="w-full grid p-4"
              >
                <input
                  type="range"
                  class="appearance-none bg-gray-300 h-0.5 rounded-full outline-none"
                  min="0"
                  max="0"
                />
              </div>
            </template>
          </div>
        </div>

        <div class="box-border bg-gray-200 rounded-md p-3">
          <div class="">
            <template
              v-for="(drug, idx) in baseIngredients"
              :key="drug.inventoryUuid"
            >
              <IngredientCard
                :drug="drug"
                :num="idx + 1"
                :on-remove="compoundingStore.removeDrug"
              />

              <!-- Add separator between items, but not after the last one -->
              <div v-if="idx < baseIngredients.length - 1" class="w-full grid">
                <input
                  type="range"
                  class="appearance-none bg-gray-300 h-0.5 rounded-full outline-none"
                  min="0"
                  max="0"
                />
              </div>
            </template>
          </div>
        </div>

        <div class="col-span-2 flex gap-3 w-full justify-end">
          <!-- <Button @click="() => { compoundingStore.clearDrugList(); router.go(-1); }"
                        class="border border-red-500 text-red-500 w-full">
                        Clear and Back
                    </Button> -->
          <Button
            @click="router.go(-1)"
            class="border border-red-500 text-red-500 w-full"
          >
            Back
          </Button>
          <Button
            @click.prevent="submit(goToDetail)"
            class="border bg-[#2E7987] text-white w-full"
          >
            Continue to add Patient
          </Button>
        </div>
      </Form>
    </div>
  </div>
</template>

<style scoped>
input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 6px;
  height: 6px;
  background: gray;
  border-radius: 50%;
  cursor: pointer;
  margin-top: -1px;
}

/* input[type="range"]::-moz-range-thumb {
    width: 7px;
    height: 7px;
    background: gray;
    border-radius: 50%;
    cursor: pointer;
} */
</style>
