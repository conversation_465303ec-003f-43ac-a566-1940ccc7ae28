<script setup>
import Button from "@/components/Button.vue";
import { useRouter } from "vue-router";
import { useApiRequest } from "@/composables/useApiRequest";
import { toasted } from "@/utils/utils";
import { useCompoundingStore } from "@/features/compounding/store/compoundingStore";
import {
  addCompounding,
  changeCompoundingPrescription,
  changeCompoundingPrescriptionBystatus,
} from "../../api/compoundingApi";

const router = useRouter();
const compoundingStore = useCompoundingStore();
const prescriptions = useApiRequest();

function reset() {
  compoundingStore.reset();
  router.replace("/compounding");
}

function send() {
  if (
    !prescriptions.dirty.value &&
    !compoundingStore.done &&
    prescriptions.pending.value
  )
    return;

  compoundingStore.setDone(true);

  prescriptions.send(
    () =>
      addCompounding({
        patientUuid: compoundingStore.selectedPatient.userUuid,
        ingredients: compoundingStore.selectedDrugList
          .filter((el) => el.type == "Active")
          .map((el) => ({
            // const { frequency, type, drug, dosageForm, pharmacyUuid, pharmacyBranchName, location_id, location_name, quantity, ...rest } = el
            name: el.drugName,
            percentage: el.percentage,
            unit: el.unit,
            inventoryUuid: el.inventoryUuid,
          })),
        base: (() => {
          const baseItem = compoundingStore.selectedDrugList.find(
            (el) => el.type === "Base"
          );
          return baseItem
            ? {
                name: baseItem.drugName,
                quantity: baseItem.quantity,
                unit: baseItem.unit,
                inventoryUuid: baseItem.inventoryUuid,
              }
            : null;
        })(),
        route: compoundingStore.priscripprescriptionDetail?.route,
        frequencyRate: parseFloat(
          compoundingStore.priscripprescriptionDetail?.frequencyRate
        ),
        frequencyUnit:
          compoundingStore.priscripprescriptionDetail?.frequencyUnit,
        duration: parseInt(
          compoundingStore.priscripprescriptionDetail?.duration
        ),
        // instruction: compoundingStore.activeDrug?.instruction,
        dose: compoundingStore.priscripprescriptionDetail?.dose,
        instruction: compoundingStore.priscripprescriptionDetail?.instruction,
        activeFor: compoundingStore.priscripprescriptionDetail?.activeFor,
        numberOfRefills:
          compoundingStore.priscripprescriptionDetail?.numberOfRefills,
        refillEndDate:
          compoundingStore.priscripprescriptionDetail?.refillEndDate,
        diagnosisDetail:
          compoundingStore.priscripprescriptionDetail?.diagnosisDetail,
        chiefComplaint:
          compoundingStore.priscripprescriptionDetail?.chiefComplaint,
        containerUuid:
          compoundingStore.priscripprescriptionDetail?.containerUuid,

        status: "REQUESTED",
      }),
    (res) => {
      toasted(res.success, "Invoice created successfully", res.error);
      if (!res.success) {
        compoundingStore.setDone(false);
      }
    }
  );
}

function update() {
  if (
    !prescriptions.dirty.value &&
    !compoundingStore.done &&
    prescriptions.pending.value
  )
    return;

  compoundingStore.setDone(true);

  prescriptions.send(
    () =>
      changeCompoundingPrescription(
        compoundingStore.compounding.prescriptionUuid,
        {
          patientUuid: compoundingStore.selectedPatient.userUuid,
          ingredients: compoundingStore.selectedDrugList
            .filter((el) => el.type == "Active")
            .map((el) => ({
              // const { frequency, type, drug, dosageForm, pharmacyUuid, pharmacyBranchName, location_id, location_name, quantity, ...rest } = el
              name: el.drugName,
              percentage: el.percentage,
              unit: el.unit,
              inventoryUuid: el.inventoryUuid,
            })),
          // The ? : ternary operator acts like a short if-else:
          // If baseItem exists, it returns an object with details.
          // If baseItem is undefined or null, it returns null.
          base: (() => {
            const baseItem = compoundingStore.selectedDrugList.find(
              (el) => el.type === "Base"
            );
            return baseItem
              ? {
                  name: baseItem.drugName,
                  quantity: baseItem.quantity,
                  unit: baseItem.unit,
                  inventoryUuid: baseItem.inventoryUuid,
                }
              : null;
          })(),
          route: compoundingStore.priscripprescriptionDetail?.route,
          frequencyRate: parseFloat(
            compoundingStore.priscripprescriptionDetail?.frequencyRate
          ),
          frequencyUnit:
            compoundingStore.priscripprescriptionDetail?.frequencyUnit,
          duration: parseInt(
            compoundingStore.priscripprescriptionDetail?.duration
          ),
          // instruction: compoundingStore.activeDrug?.instruction,
          dose: compoundingStore.priscripprescriptionDetail?.dose,
          instruction: compoundingStore.priscripprescriptionDetail?.instruction,
          activeFor: compoundingStore.priscripprescriptionDetail?.activeFor,
          numberOfRefills:
            compoundingStore.priscripprescriptionDetail?.numberOfRefills,
          refillEndDate:
            compoundingStore.priscripprescriptionDetail?.refillEndDate,
          diagnosisDetail:
            compoundingStore.priscripprescriptionDetail?.diagnosisDetail,
          chiefComplaint:
            compoundingStore.priscripprescriptionDetail?.chiefComplaint,
          containerUuid:
            compoundingStore.priscripprescriptionDetail?.containerUuid,
          status: "REQUESTED",
        }
      ),
    (res) => {
      toasted(res.success, "Invoice created successfully", res.error);
      if (!res.success) {
        compoundingStore.setDone(false);
      }
    }
  );
}

function updateByStatus() {
  if (
    !prescriptions.dirty.value &&
    !compoundingStore.done &&
    prescriptions.pending.value
  )
    return;

  compoundingStore.setDone(true);

  prescriptions.send(
    () =>
      changeCompoundingPrescriptionBystatus(
        compoundingStore.compounding.prescriptionUuid,
        {
          status: "REQUESTED",
          paymentType: "Cash",
        },
        {}
      ),
    (res) => {
      toasted(res.success, "Invoice created successfully", res.error);
      if (!res.success) {
        compoundingStore.setDone(false);
      }
    }
  );
}

function check() {
  console.log(compoundingStore.ePrescription);

  if (compoundingStore.ePrescription) {
    updateByStatus();
  } else if (compoundingStore.edit) {
    update();
  } else {
    send();
  }
}

check();
</script>
<template>
  <div
    class="relative flex-1 p-4 bg-white flex flex-col gap-6 justify-center items-center"
  >
    <div
      v-if="prescriptions.pending.value"
      class="grid place-content-center absolute inset-0 z-20 backdrop-blur-sm"
    >
      <p class="bg-white p-2 rounded shadow-md">... Creating Invoice</p>
    </div>
    <img src="/src/assets/img/payment.svg" />
    <div
      v-if="!prescriptions.error.value"
      class="flex items-center gap-4 justify-center flex-col"
    >
      <p class="font-bold text-md">Order Sent to Payment Successfully!</p>
      <p>Click on “Another Prescription” to process another Prescription</p>
      <Button @click="reset" type="secondary"> Another Prescription </Button>
    </div>
    <div v-else class="text-center flex flex-col gap-2s">
      <p class="text-error p-2">{{ prescriptions.error.value }}</p>
      <Button
        :pending="prescriptions.pending.value"
        type="primary"
        @click="check"
        >Retry</Button
      >
    </div>
  </div>
</template>
