<script setup>
import { computed, nextTick, ref, watch } from "vue";
import Button from "@/components/Button.vue";
import { useRouter } from "vue-router";
import { useCompoundingStore } from "../../store/compoundingStore";
import CompoundingPrescriptionDetailForm from "../../form/CompoundingPrescriptionDetailForm.vue";

const route = useRouter();
const props = defineProps({
    goToVitals: Function,
});

const compoundingStore = useCompoundingStore();

const active = ref(0);

function submit(data, cupId, reset) {
    compoundingStore.setPrescriptionDetail(data, cupId);
    // if (active.value + 1 <= compoundingStore.selectedDrugList?.length - 1) {
    //     reset();
    //     nextTick(() => {
    //         compoundingStore.setActiveDrug(
    //             compoundingStore.selectedDrugList?.[++active.value]
    //         );
    //     })
    // }
    props.goToVitals();
}

watch(
    () => compoundingStore.activeDrug,
    () => {
        const idx = compoundingStore.selectedDrugList.findIndex(
            (el) => el.product_id == compoundingStore.activeDrug?.product_id
        );
        if (idx > -1) {
            active.value = idx;
        }
    }
);

watch(
    () => compoundingStore.selectedDrugList,
    (newVal, oldVal) => {
        if (newVal?.length > oldVal?.length && oldVal) return;

        if (compoundingStore.selectedDrugList?.length === 0) return;

        if (active.value >= compoundingStore.selectedDrugList?.length) {
            active.value = compoundingStore.selectedDrugList?.length - 1;
            compoundingStore.setActiveDrug(
                compoundingStore.selectedDrugList?.[active.value]
            );
            return;
        }

        compoundingStore.setActiveDrug(
            compoundingStore.selectedDrugList?.[active.value]
        );
    },
    { immediate: true, deep: true }
);

watch(
    () => compoundingStore.selectedDrugList,
    (val) => {
        if (val?.length == 0) {
            route.replace("/compounding");
        }
    },
    { immediate: true, deep: true }
);

const activeDrugNames = computed(() => {
    return compoundingStore.activeDrug?.map((drug) => drug.drugName).join(", ") || "";
});
</script>
<template>
    <div class="bg-white">
        <p class="p-2">
            Prescribed to
            <strong>{{
                `${compoundingStore?.selectedPatient?.firstName} ${compoundingStore?.selectedPatient?.fatherName}`
            }}</strong>
        </p>
        <p class="px-2 text-sm italic">
            prescription detail for
            <strong class="underline">{{
                activeDrugNames
            }}</strong>
        </p>
        <CompoundingPrescriptionDetailForm :prescription="compoundingStore.priscripprescriptionDetail || {}"
            :onSubmit="submit">
            <template v-if="compoundingStore.prescriptionFilled" #more-actions>
                <Button @click.prevent="goToVitals" type="secondary"> Continue </Button>
            </template>
        </CompoundingPrescriptionDetailForm>
    </div>
</template>
