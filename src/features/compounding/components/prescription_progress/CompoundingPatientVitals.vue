<script setup>
import Button from '@/components/Button.vue';
import { openModal } from '@customizer/modal-x';
import { useApiRequest } from '@/composables/useApiRequest'
import { removeUndefined, toasted } from '@/utils/utils';
import { useForm } from '@/new_form_builder/useForm'
import { updatePatient } from '@paper_prescription/api/patientApi'
import { useCompoundingStore } from '../../store/compoundingStore';
import PatientVitalsForm from '@/features/paper_prescription/components/form/PatientVitalsForm.vue';

const compoundingStore = useCompoundingStore();
const props = defineProps({
    sendToPayment: Function
})

// function send() {
//     openModal('getPharmacyInventory', {}, inventoryRes => {
//         if (inventoryRes) {
//             openModal('Confirmation', {
//                 title: 'Send To Payment',
//                 message: 'Are You Sure?'
//             }, confirmRes => {
//                 if (confirmRes) {
//                     props.sendToPayment(inventoryRes)
//                 }
//             })
//         }
//     })

// }

function send() {
    openModal('Confirmation', {
        title: 'Send To Payment',
        message: 'Are You Sure?'
    }, res => {
        if (res) {
            props.sendToPayment()
        }
    })
}

const vitalsReq = useApiRequest()
function addVitals({ values }) {
    if (!Object.values(values).some(el => el) || vitalsReq.pending.value) return

    const data = {
        ...compoundingStore.selectedPatient,
        vitals: [values],
        userUuid: undefined
    }

    vitalsReq.send(
        () => updatePatient(compoundingStore.selectedPatient?.userUuid, removeUndefined(data)),
        res => {
            toasted(res.success, 'Succesfully Added Vitals', res.error)
        }
    )
}

const { submit } = useForm('vitals')
</script>
<template>
    <div class="bg-white p-2">
        <p class="p-2 font-bold">
            Screening (POC) Test's
        </p>
        <PatientVitalsForm :addVitals="addVitals" :patient="compoundingStore.selectedPatient">
            <template #default>
                <div class="flex border-t items-center justify-end p-2 gap-2">
                    <Button :pending="vitalsReq.pending.value" @click.prevent="submit(addVitals)" type="primary">
                        Add New Vitals
                    </Button>
                    <Button :disabled="vitalsReq.pending.value" @click.prevent="send" type="secondary">
                        Send to Payment
                    </Button>
                </div>
            </template>
        </PatientVitalsForm>
    </div>
</template>