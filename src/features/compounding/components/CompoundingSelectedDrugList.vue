<script setup>
import Button from "@/components/Button.vue";
import Icon from "@/components/Icon.vue";
import { computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import CompoundingDrugCard from "./CompoundingDrugCard.vue";
import { useCompoundingStore } from "../store/compoundingStore";
import Compounding from "../pages/compounding.vue";

const props = defineProps({
  active: {
    type: [String, Number],
  },
  showDetail: Boolean,
  remove: {
    type: Boolean,
    default: true,
  },
  actions: {
    type: Boolean,
    default: true,
  },
  onSearch: {
    type: Function,
    default: (f) => f,
  },
  onDrugSelected: {
    type: Function,
    default: (f) => f,
  },
  onD2D: {
    type: Function,
    default: (f) => f,
  },
});
const emit = defineEmits(["drug:selected"]);
const router = useRouter();
const compoundingStore = useCompoundingStore();
console.log(compoundingStore.selectedDrugList);

console.log(compoundingStore.selectedDrugList);

const sortedDrugs = computed(() => {
  return [...compoundingStore.selectedDrugList].sort((a, b) =>
    a.type === "Active" ? -1 : 1
  );
});

function goToDetail() {
  if (compoundingStore.selectedDrugList.length) {
    router.push("/compounding/detail");
  }
}

// const detail = computed(
//   () => (id) =>
//     compoundingStore.priscripprescriptionDetail.find(
//       (el) => el?.inventoryUuid == id
//     )
// );

// function callculateCompundingPrice(drugs = []) {
//   const base = drugs.find(el => el.type == 'Base')
//   const basePrice = (base?.weight || 0) * ((base?.sellingUnitPrice || 0) / parseInt(base?.strength || 0))

//   console.log(basePrice)
//   const activeDrugsPrice = drugs.reduce((state, el) => {
//     if (el.type == 'Base') return state

//     const per = el.percentage * ((state?.base?.weight || 0) / 100)
//     const price = per * el.sellingUnitPrice / parseInt(el.strength)

//     state.price += price
//     return state
//   }, { price: 0, base })

//   console.log(basePrice + activeDrugsPrice.price)

//   return activeDrugsPrice.price + basePrice + (((drugs.length - 1) * 50) > 250 ? 250 : (drugs.length - 1) * 50)
//function callculateCompundingPrice(drugs = []) {
//   const base = drugs.find(el => el.type == 'Base')
//   const basePrice = (base?.weight || 0) * ((base?.sellingUnitPrice || 0) / parseInt(base?.strength || 0))

//   console.log(basePrice)
//   const activeDrugsPrice = drugs.reduce((state, el) => {
//     if (el.type == 'Base') return state

//     const per = el.percentage * ((state?.base?.weight || 0) / 100)
//     const price = per * el.sellingUnitPrice / parseInt(el.strength)

//     state.price += price
//     return state
//   }, { price: 0, base })

//   console.log(basePrice + activeDrugsPrice.price)

//   return activeDrugsPrice.price + basePrice + (((drugs.length - 1) * 50) > 250 ? 250 : (drugs.length - 1) * 50)
// } }

function callculateCompundingPrice(drugs = []) {
  console.log(drugs);

  return drugs.reduce((sum, el) => {
    sum += el.list_price * (el.quantity || 0);
    return sum;
  }, 0);
}
</script>
<template>
  <div class="bg-base-clr-2 w-full h-full flex flex-col gap-6">
    <div
      class="text-base-clr-2 flex justify-between px-7 items-center min-h-drug-search-height bg-secondary"
    >
      <p class="font-medium">Prescription List</p>
      <div class="flex items-center gap-4">
        <button class="bg-primary px-2 py-1 rounded" @click.stop="onD2D">
          D2D
        </button>
        <button @click.stop="onSearch">
          <Icon color="rgb(var(--base-clr-2))" name="mynaui:search" />
        </button>
      </div>
    </div>
    <div
      v-if="!compoundingStore.selectedDrugList?.length"
      class="flex-1 flex flex-col text-center items-center gap-4"
    >
      <img src="/src/assets/img/med_list.svg" />
      <div class="p-4 px-14 flex flex-col gap-4">
        <p class="font-bold text-md">Your list is empty.</p>
        <p class="text-xs">
          Note: you can search and add drugs to the list from the search
          section.
        </p>
      </div>
    </div>
    <div
      v-else
      :style="{
        height: 'calc(100% - 4rem - 2rem)',
      }"
      class="flex h-full text-sm flex-col gap-4"
    >
      <div
        class="show-scrollbar h-full pt-2 flex flex-col box-border gap-4 px-4 overflow-hidden overflow-y-auto"
      >
        <TransitionGroup name="list">
          <template
            v-for="(drug, idx) in sortedDrugs.slice(0, 2)"
            :key="drug.inventoryUuid"
          >
            <div
              class="box-border rounded-md p-3"
              :class="drug.type === 'Active' ? 'bg-gray-200' : 'bg-gray-200'"
            >
              <CompoundingDrugCard
                @click="
                  () =>
                    compoundingStore.setActiveDrug(
                      compoundingStore.selectedDrugList
                    )
                "
                :num="idx + 1"
                :drug="drug"
                :detail="compoundingStore.getPrescriptionDetail"
                :on-remove="compoundingStore.removeDrug"
              />
            </div>
          </template>
        </TransitionGroup>
        <div
          class="flex justify-between bg-primary border w-full rounded-md p-4 text-white font-bold"
        >
          <div>
            <p>Price</p>
          </div>
          <div>
            <p>
              ETB
              {{
                compoundingStore.compounding.prescriptionDetail
                  ?.totalPaymentAmount
              }}
            </p>
          </div>
        </div>
      </div>
      <div v-if="actions" class="min-h-12 px-4 flex gap-3">
        <slot
          v-if="
            !compoundingStore.selectedDrugList.find(
              (el) => el.weight || el.percentage
            )
          "
        >
          <Button
            @click="compoundingStore.clearDrugList"
            class="text-error border-error border w-[40%]"
          >
            Clear
          </Button>
          <Button @click="goToDetail" type="secondary" class="w-[60%]">
            Continue to Compounding
          </Button>
        </slot>
        <slot v-else>
          <!-- <div class="flex justify-between bg-primary border w-full rounded-md p-4 text-white font-bold">
            <div>
              <p>Price</p>
            </div>
            <div>
              <p>ETB {{ callculateCompundingPrice(compoundingStore.selectedDrugList) }}</p>
            </div>
          </div> -->
        </slot>
      </div>
    </div>
  </div>
</template>

<style>
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
