<script setup>
import Button from "@/components/Button.vue";
import { watch } from "vue";
import CompoundingSelectedDrugList from "./CompoundingSelectedDrugList.vue";
import { useCompoundingStore } from "../store/compoundingStore";

const props = defineProps({
  detail: {
    type: Boolean,
    default: false,
  },
  showDetail: Boolean,
  goTo: Function,
});

function go() {
  props.goTo("search");
}

function goD2D() {
  props.goTo("d2d");
}
const compoundingStore = useCompoundingStore();
console.log(props?.detail);
</script>
<template>
  <CompoundingSelectedDrugList
    :showDetail="showDetail"
    :onDrugSelected="
      compoundingStore.setActiveDrug(compoundingStore.selectedDrugList)
    "
    :active="compoundingStore.activeDrug?.inventoryUuid"
    :on-search="go"
    :onD2D="goD2D"
  >
    <template v-if="detail" #default>
      <div
        v-if="!compoundingStore.done"
        class="flex items-center w-full justify-end gap-2 mt-auto"
      >
        <Button
          @click="
            () => {
              compoundingStore.clearDrugList();
              $router.replace('/compounding');
            }
          "
          class="border truncate border-error text-error"
        >
          Clear
        </Button>
        <Button
          @click="$router.replace('/compounding')"
          class="flex-1 border truncate border-dark"
        >
          Add Drugs
        </Button>
      </div>
      <div v-else></div>
    </template>
  </CompoundingSelectedDrugList>
</template>
