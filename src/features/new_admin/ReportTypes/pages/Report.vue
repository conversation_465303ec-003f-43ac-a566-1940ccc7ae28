<script setup>
import FormLayout from '@/components/new_form_elements/NewFormLayout.vue';
import CertificateReports from '../habus_report/certificateReports.vue'
import { Input, Select } from '@/components/new_form_elements';
import { generateReports } from '@/features/report/api/reportApi'
import Tabs from '@/components/Tabs.vue';
import Report from '@/features/report/components/Report.vue'
import Summury from '@/features/report/components/Summury.vue'
import { ref, watch } from 'vue';
import { addDayToDate, removeUndefined } from '@/utils/utils';
import { ArabCountries } from '@/utils/arabCountries';
import { secondDateFormat } from '@/utils/utils';
import FormSubmitButton from '@/components/FormSubmitButton.vue';
import { getAllAgencies } from '@/features/certificate/api/agenciesApi';
import { useApiRequest } from '@/composables/useApiRequest';
import SearchAndSelectInput from '@/components/new_form_elements/SearchAndSelectInput.vue';
import ReportDataProvider from '../components/ReportDataProvider.vue';

const reportReq = useApiRequest()

const filtered = ref()

function getReport(certificate, values) {
  filtered.value = certificate
    .filter(el => {
      const beginDate = new Date(values['beginDate'])
      const endDate = new Date(values['endDate'])
      const issuedDate = new Date(el.issuedDate)
      return issuedDate >= beginDate && issuedDate <= endDate
    })
    .filter(el => {
      if (!values.branchUuid) return el
      return el.branchUuid == values.branchUuid
    })
    .filter(el => {
      if (!values.agencyUuid) return el
      return el.agencyUuid == values.agencyUuid
    })
    .filter(el => {
      if (!values.destination) return el
      return el.destination == values['destination']
    })
    .filter(el => {
      if (!values.gender) return el
      return el.gender == values['gender']
    })
}

watch(filtered, el => {
  console.log(filtered.value)
})

const excel = ref('')

function genReport({ values }) {
  reportReq.send(
    () => generateReports(removeUndefined(values)),
    res => {
      if (res.success) {
        const url = URL.createObjectURL(res.data)
        excel.value.href = url
        excel.value.setAttribute('download', `report_from_${secondDateFormat(values.beginDate)}_to_${secondDateFormat(values.endDate)}.xlsx`);
        excel.value.click()
      }
    }
  )
}

</script>
<template>
  <div class="bg-white p-2 flex flex-col gap-2 rounded-t-md">
    <p class="m-2 text-xl font-bold">Report</p>
    <a ref="excel" />
    <ReportDataProvider v-slot="{ pending, reports }">
      {{ console.log(reports) }}
      <FormLayout id="report-form" v-slot="{ submit }" v-if="!pending" class="px-2">
        <div class="flex flex-col gap-1">
          <p class="text-sm font-medium mr-auto">Date Range</p>
          <div class="flex gap-2 items-center">
            <Input :value="addDayToDate(new Date(), -120).toISOString().split('T')?.[0]" type="date" name="beginDate"
              :attributes="{
                type: 'date'
              }" />
            To
            <Input type="date" name="endDate" :value="addDayToDate(new Date(), 0).toISOString().split('T')?.[0]"
              :attributes="{
                type: 'date'
              }" />
          </div>
        </div>
        <div class="col-span-2 flex gap-2">
          <Select :obj="true" class="flex-1" name="branchUuid" label="Branch"
            :options="(reports.branches || []).map(el => ({ label: el.branch, value: el.insuranceUuid }))" :attributes="{
              placeholder: 'Search Branch',
            }" />
          <!-- <SearchAndSelectInput
            :obj="true"
            class="flex-1"
            name="agencyUuid"
            label="Agency"
            :options="(reports.agencies || []).map(el => ({label: el.agencyName, value: el.agencyUuid}))"
          /> -->
          <SearchAndSelectInput class="col-span-2" label="agency" :onInput="getAllAgencies" name="agencyUuid"
            :attributes="{
              placeholder: 'Search and Select Agency',
            }" />
          <Select class="flex-1" name="destination" :options="ArabCountries" label="Destination Country" :attributes="{
            placeholder: 'Search Country',
          }" />
          <!-- <Select
            class="flex-1"
            name="gender"
            value="Female"
            label="Gender"
            :options="['Male', 'Female']"
          /> -->
        </div>
        <div class="col-span-2 flex justify-end items-center">
          <FormSubmitButton :pending="reportReq.pending.value" btn-text="Generate Report"
            @click.prevent="submit(genReport)" class="bg-primary w-full px-4 py-2 rounded-md text-white" />
        </div>
      </FormLayout>
      <FormLoading v-else />
      <div v-if="filtered" class="p-3 border-t-2 rounded-b-md bg-white">
        <Tabs :tabs="{
          report: {
            component: Report,
            props: {
              filtered
            }
          },
          summary: {
            component: Summury,
            props: {
              certificates: reports.certificates
            }
          },
        }">
        </Tabs>
      </div>
    </ReportDataProvider>
  </div>
  <!-- <CertificateReports /> -->
</template>