import { ref } from "vue";
import { defineStore } from "pinia";

export const useViewRequested = defineStore("allViewRequestedStore", () => {
  const viewRequest = ref([]);

  function set(data) {
    viewRequest.value = data;
  }

  function getAll() {
    return viewRequest.value;
  }
  function add(data) {
    return viewRequest.value.push(data);
  }

  function update(id, data) {
    const idx = viewRequest.value.findIndex((el) => el.roleUuid == id);
    if (idx == -1) return;

    viewRequest.value.splice(idx, 1, data);
  }
  function remove(id) {
    const idx = viewRequest.value.findIndex((el) => el.roleUuid == id);
    if (idx == -1) return;

    viewRequest.value.splice(idx, 1);
  }

  function updateStatus(id, data) {
    const idx = viewRequest.value.findIndex((el) => el.branchUuid == id);
    if (idx == -1) return;
    console.log(data);
    console.log(viewRequest.value[idx]);
    viewRequest.value[idx].status = data.status;
  }

  return {
    viewRequest,
    getAll,
    update,
    remove,
    add,
    updateStatus,
    set,
  };
});
