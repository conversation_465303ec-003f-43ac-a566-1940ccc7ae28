import { ref } from "vue";
import { defineStore } from "pinia";

export const usePurchaseOrder = defineStore(
	"allPurchaseOrdesr",
	() => {
		const orders = ref([]);

		function set(data) {
			orders.value = data;
		}

		function getAll() {
			return orders.value;
		}
		function add(data) {
			return orders.value.push(data);
		}

		function update(id, data) {
			const idx = orders.value.findIndex((el) => el.purchaseOrderUuid == id);
			if (idx == -1) return;

			orders.value.splice(idx, 1, data);
		}
		function remove(id) {
			const idx = orders.value.findIndex((el) => el.purchaseOrderUuid == id);
			if (idx == -1) return;

			orders.value.splice(idx, 1);
		}

		function updateStatus(id, data) {
			const idx = orders.value.findIndex((el) => el.purchaseOrderUuid == id);
			if (idx == -1) return;
			console.log(data);
			console.log(orders.value[idx]);
			orders.value[idx].status = data.status;
		}

		return {
			orders,
			getAll,
			update,
			remove,
			add,
			updateStatus,
			set,
		};
	}
);
