import { ref } from "vue";
import { defineStore } from "pinia";

export const useApproval = defineStore("allApprovalStore", () => {
  const approval = ref([]);

  function set(data) {
    approval.value = data;
  }

  function getAll() {
    return approval.value;
  }
  function add(data) {
    return approval.value.push(data);
  }

  function update(id, data) {
    const idx = approval.value.findIndex((el) => el.roleUuid == id);
    if (idx == -1) return;

    approval.value.splice(idx, 1, data);
  }
  function remove(id) {
    const idx = approval.value.findIndex((el) => el.roleUuid == id);
    if (idx == -1) return;

    approval.value.splice(idx, 1);
  }

  function updateStatus(id, data) {
    const idx = approval.value.findIndex((el) => el.branchUuid == id);
    if (idx == -1) return;
    console.log(data);
    console.log(approval.value[idx]);
    approval.value[idx].status = data.status;
  }

  return {
    approval,
    getAll,
    update,
    remove,
    add,
    updateStatus,
    set,
  };
});
