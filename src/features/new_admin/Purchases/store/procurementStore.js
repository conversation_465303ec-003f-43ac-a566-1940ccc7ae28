import { ref } from "vue";
import { defineStore } from "pinia";

export const useProcurementRequest = defineStore(
  "allProcurementPurchaseRequestStore",
  () => {
    const purchaseRequest = ref([]);

    function set(data) {
      purchaseRequest.value = data;
    }

    function getAll() {
      return purchaseRequest.value;
    }
    function add(data) {
      return purchaseRequest.value.push(data);
    }

    function update(id, data) {
      const idx = purchaseRequest.value.findIndex((el) => el.branchUuid == id);
      if (idx == -1) return;

      purchaseRequest.value.splice(idx, 1, data);
    }
    function remove(id) {
      const idx = purchaseRequest.value.findIndex((el) => el.branchUuid == id);
      if (idx == -1) return;

      purchaseRequest.value.splice(idx, 1);
    }

    function updateStatus(id, data) {
      const idx = purchaseRequest.value.findIndex((el) => el.branchUuid == id);
      if (idx == -1) return;
      console.log(data);
      console.log(purchaseRequest.value[idx]);
      purchaseRequest.value[idx].status = data.status;
    }

    return {
      purchaseRequest,
      getAll,
      update,
      remove,
      add,
      updateStatus,
      set,
    };
  }
);
