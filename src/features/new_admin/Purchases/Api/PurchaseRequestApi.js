import ApiService from "@/service/ApiService";
import { getApi, getQueryFormObject } from "@/utils/utils";

const api = getApi('/purchase-request');

export function createRequest(data) {
  return api
    .addAuthenticationHeader()
    .post(`/createPurchaseRequest`, data);
}

export function getAllPurchaseRequest(query = {}) {
  return api.addAuthenticationHeader().get('/batch/all', {
    params: query
  })
}

export function getAllPurchaseRequestDetail(query = {}) {
  return api.addAuthenticationHeader().get('/batch/items/all', {
    params: query
  })
}

export function getPurchaseOrders(query = {}) {
  return api.addAuthenticationHeader().get('/batch/items/all', {
    params: query
  })
}

export function getPurchaseOrderById(id) {
  return api.addAuthenticationHeader().get(`/order/${id}`)
}