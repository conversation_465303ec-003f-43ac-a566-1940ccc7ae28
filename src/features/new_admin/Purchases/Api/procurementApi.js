import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const api = new ApiService();
const path = "/procurement/purchase-request";

export function getPurchaseRequest() {
  return api.addAuthenticationHeader().get(`${path}/getAggregatedRequests`);
}
export function approveRequest(id, data) {
  return api.addAuthenticationHeader().patch(`${path}/${id}/approve`, data);
}
export function getApprovedRequest() {
  return api.addAuthenticationHeader().get(`${path}/approved-requests`);
}
export function approveGroupRequest(id, data) {
  return api
    .addAuthenticationHeader()
    .patch(`${path}/group/${id}/approve`, data);
}
export function confirmApprovals(query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().post(`${path}/confirmApprovals${qr}`);
}
