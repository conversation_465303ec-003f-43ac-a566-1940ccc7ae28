<script setup>
import Button from '@/components/Button.vue';
import Table2 from '@/components/Table2.vue';
import icons from '@/utils/icons';
import { openModal } from '@customizer/modal-x';
import { useRoute, useRouter } from 'vue-router';
import { useProcurementRequest } from '../store/procurementStore';
import { ref } from 'vue';
import TableWithCheckBox from '@/components/TableWithCheckBox.vue';

const requestStore = useProcurementRequest()
const route = useRoute()
const requestedUuid = route.params.requestGroupId
console.log(requestedUuid);

const request = ref(requestStore.purchaseRequest.find((el) => el.requestGroupId == requestedUuid) || {});
console.log(request.value);

// const tableRows = request.value.branchRequests?.map(branch => ({
// 	requestUuid: branch.requestUuid,
//   branchUuid: branch.branchUuid,
// 	requestedId: branch.requestedId,
// 	branchId: branch.branchId,
// 	branchName: branch.branchName,
// 	quantity: `${branch.quantity}`,
// 	requestDate: branch.requestDate,
// 	status: branch.status,
// 	requestedBy: branch.managerName || 'null',
// })) || [];

const tableRows = ref(request.value.branchRequests || []);
console.log(tableRows.value);

const router = useRouter();
const selectedRows = ref([]);
console.log(selectedRows.value);


function goBack() {
	router.go(-1);
}
</script>
<template>
	<div class="pt-16 px-[2.88rem] flex flex-col gap-6">
		<div class="flex justify-between items-center">
			<div class="flex gap-3">
				<i @click="goBack" class="text-black" v-html="icons.leftArrowRounded" />
				<p class="font-bold text-[18px]">{{ request.drugDetails }} <span>Drug Detail</span></p>
			</div>
		</div>
		<div class="grid grid-cols-3 gap-[0.63rem]">
			<div class="py-3 pr-[0.4rem] pl-5 bg-white flex flex-col gap-[0.4rem]">
				<p class="opacity-60 text-base font-normal">Requested ID</p>
				<p class="font-bold text-base">{{ request.requestGroupId }}</p>
			</div>
			<div class="py-3 pr-[0.4rem] pl-5 bg-white flex flex-col gap-[0.4rem]">
				<p class="opacity-60 text-base font-normal">Quantity</p>
				<p class="font-bold text-base">{{ request.totalQuantity }}</p>
			</div>
			<div class="py-3 pr-[0.4rem] pl-5 bg-white flex flex-col gap-[0.4rem]">
				<p class="opacity-60 text-base font-normal">Number of branches</p>
				<p class="font-bold text-base">{{ request.branchCount }}</p>
			</div>
		</div>
		<div class="flex justify-between items-center">
			<p class="font-bold text-[1.13rem]">{{ selectedRows.length }} Branches Selected</p>
			<Button @click="openModal('ApproveAllRequests', selectedRows)" type="primary" class="">
				<p class="text-white">Approve Request</p>
			</Button>
		</div>
		<div>
			<div class="flex-1 custom-table">
				<TableWithCheckBox v-model="selectedRows" :last="false" to-be-selected="requestUuid" :headers="{
					head: [
						'Requested ID',
						'Branch ID',
						'Branch Name',
						'Quantity',
						'Requested Date',
						'Requested By',
						'Status',
						'Actions'
					],
					row: [
						'requestedId',
						'branchId',
						'branchName',
						'quantity',
						'requestDate',
						'managerName',
						'status',
					],
				}" :rows="(tableRows || [])" :cells="{
				}">
					<template #actions="{ row }">
						<Button @click="openModal('ApprovalRequest', row)" type="secondary"
							class="flex gap-4 text-white rounded items-center">
							<p class="opacity-80">Adjust</p>
						</Button>
					</template>
				</TableWithCheckBox :last="false">
			</div>
		</div>
	</div>
</template>

<style scoped>
.custom-table tbody {
	background-color: transparent !important;
	background: none !important;
}

.custom-table :deep(tbody) {
	background-color: transparent !important;
	background: none !important;
	/* Change to your desired tbody background color */
}

table.min-w-full tbody {
	background-color: transparent !important;
	background: none !important;
}

.custom-table :deep(tr) {
	background-color: #ffffff !important;
	/* Change to your desired tr background color */
}

.custom-table :deep(tr:hover) {
	background-color: #e9ecef !important;
	/* Change to your desired hover color */
}

.custom-table :deep(th) {
	background-color: #ffffff !important;
	/* Keep header background consistent */
	color: #333 !important;
	/* Header text color */
}

.custom-table :deep(td) {
	border-bottom: 1px solid #dee2e6 !important;
	/* Add borders between rows */
	padding: 0.75rem !important;
	/* Consistent padding */
}
</style>