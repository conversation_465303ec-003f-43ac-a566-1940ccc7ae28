<script setup>
import BaseIcon from "@/components/base/BaseIcon.vue";
import Button from "@/components/Button.vue";
import DefaultPage from "@/components/DefaultPage.vue";
import Table from "@/components/Table.vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import { openModal } from "@customizer/modal-x";
import { mdiMagnify } from "@mdi/js";
import { ref } from "vue";
import { getAllPurchaseOrder } from "../Api/purchaseOrderApi";
import { usePurchaseOrder } from "../store/purchaseOrder";
import { secondDateFormat } from "@/utils/utils";

const purchaseOrderStore = usePurchaseOrder();
const pagination = usePaginationcopy({
  store: purchaseOrderStore,
  cb: (data) => getAllPurchaseOrder(data)
});

const search = ref("");
const dateValue = ref("");
</script>

<template>
  <DefaultPage>
    <div class="flex justify-end gap-4 items-center">
      <div class="flex gap-4 border rounded h-12 px-4 items-center bg-accent">
        <input
          v-model="pagination.search.value"
          class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80"
          placeholder="Search"
        />
        <BaseIcon :path="mdiMagnify" :size="20" />
      </div>
			<Button @click="openModal('PurchaseOrder')" type="primary" class="h-11">Start Purchase Order</Button>
    </div>
    <div class="">
      <Table
        :pending="pagination.pending.value"
        :headers="{
          head: ['PO ID', 'Supplier', 'PO Date', 'PO Status', 'Actions'],
          row: ['purchaseOrderNumber', 'supplierName', 'poDate', 'purchaseOrderStatus'],
        }"
        :cells="{
          poDate: secondDateFormat
        }"
				:rows="purchaseOrderStore.orders"
      >
				<template #actions="{ row }" >
					<Button @click="$router.push(`/purchase-order-detail/${row?.purchaseOrderUuid}`)" type="edge" size="xs" >Detail</Button>
				</template>
			</Table>	
    </div>
  </DefaultPage>
</template>
