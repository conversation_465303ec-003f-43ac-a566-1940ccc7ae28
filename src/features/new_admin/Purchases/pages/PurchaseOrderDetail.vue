<script setup>
import DefaultPage from "@/components/DefaultPage.vue";
import Input from "@/components/new_form_elements/Input.vue";
import Table from "@/components/Table.vue";
import TableNumberCell from "@/components/TableNumberCell.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import Form from "@/new_form_builder/Form.vue";
import InputParent from "@/new_form_builder/InputParent.vue";
import { useRoute } from "vue-router";

import { computed, ref } from "vue";
import { getAllSuppliers } from "@/features/admin/supplier/api/SupplierApi";
import SearchSelect from "@/components/SearchSelect.vue";
import { getdrug } from "@/features/StoreKeeper/api/InventoryApi";
import { createPurchaseOrderItem, updatePurchaseOrderItem } from "../Api/purchaseOrderItemApi";
import { formatCurrency, toasted } from "@/utils/utils";
import Button from "@/components/Button.vue";
import { getPurchaseOrderById, updateOrderByBatch, updatePurchaseOrderByStatus } from "../Api/purchaseOrderApi";
import { openModal } from "@customizer/modal-x";

const route = useRoute();
const orderID = route.params.orderId;

const orderReq = useApiRequest();

const orders = ref([]);
orderReq.send(
  () => getPurchaseOrderById(orderID),
  (res) => {
    if (res.success) {
      orders.value = res.data?.purchaseOrderItemsResponses || [];
    }
  }
);

const thisItem = computed(() => {
  return (id) => orders.value?.find?.((el) => id == el.itemName);
});

const selectedRow = ref();

const itemReq = useApiRequest();
function addItem() {
  if (itemReq.pending.value) return;

  itemReq.send(
    () =>
      createPurchaseOrderItem(orderID, {
        itemName: selectedRow.value.drugName,
        drugBrandUuid: selectedRow.value.brand?.[0]?.drugBrandUuid,
        supplierName: orderID,
        unit: selectedRow.value.dosageForm,
        quantity: 0,
        unitPrice: 0,
        totalCost: 0,
      }),
    (res) => {
      if (res.success) {
        orders.value.unshift({
          ...res.data,
          itemName: selectedRow.value.drugName,
          supplierName: orderID,
        drugBrandUuid: selectedRow.value.brand?.[0]?.drugBrandUuid,
          unit: selectedRow.value.dosageForm,
          quantity: 0,
          unitPrice: 0,
          totalCost: 0,
        });
      }
      toasted(res.success, 'Item Added', res.error)
    },
  );
}

const getTotalConst = computed(() => {
  return (row) => row.quantity * row.unitPrice;
});

const updateReq = useApiRequest();
const edit = ref([])
function updateDrugState(values) {
  if(updateReq.pending.value) return

  edit.value.push(values.purchaseOrderUuid)
  updateReq.send(
    () => updatePurchaseOrderItem(values.purchaseOrderUuid, {
      ...values,
      totalCost: getTotalConst.value(values)
    }),
    res => {
      edit.value = edit.value.filter(el => el != values.purchaseOrderUuid)
      toasted(res.success, 'Successfully Updated', res.error)
    }
  )
}

const batchUpdateReq = useApiRequest()
function updateBatch() {
  if(batchUpdateReq.pending.value) return

  batchUpdateReq.send(
    () => updateOrderByBatch(orderID, orders.value?.map(el => ({
      poUuid: el.purchaseOrderUuid,
      quantity: el.quantity,
      unitPrice: el.unitPrice,
      totalCost: getTotalConst.value(el)
    }))),
    res => {
      if(res.success) {
        toasted(res.success, 'Successfully Updated', res.error)
      }
    }
  )
}

const submitBatchReq = useApiRequest()
function submitBatch() {
  if(submitBatchReq.pending.value) return

  openModal('Confirmation', {
    title: 'Submit Order Request',
    message: 'Are you sure you want to commit the changes'
  }, res => {
    if(res) {
      submitBatchReq.send(
        () => updatePurchaseOrderByStatus(orderID, 'PURCHASED'),
        res => {
          if(res.success) {
            toasted(res.success, 'Successfully Submitted', res.error)
          }
        }
      )
    }
  })
}
const search = ref('')
</script>

<template>
  <DefaultPage>
    <div class="border-b pb-4 flex gap-4 items-center justify-between">
      <div v-if="orderReq.response.value?.purchaseOrderStatus == 'ONGOING'" class="items-center gap-4 flex" >
        <SearchSelect
          position="left-bottom"
          :onChange="
            (value) => {
              console.log(value);
  
              selectedRow = value;
            }
          "
          name="supplierName"
          :option="{ label: 'drugName', value: 'drugUuid' }"
          :search-cb="getdrug"
          placeholder="Search Drug"
        />
        <Button :pending="itemReq.pending.value" type="primary" @click="addItem" v-if="selectedRow">Add</Button>
      </div>
      <div class="flex items-center gap-2 ml-auto" >
        <input v-model="search" placeholder="Search For Item" class="text-sm px-3 h-10 border p-2 rounded-md bg-transparent" />
        <template v-if="orderReq.response.value?.purchaseOrderStatus == 'ONGOING'" >
          <Button @click="updateBatch" type="primary" >
            Save
          </Button>
          <div class="h-10 w-1 border-l border-gray-500" ></div>
          <Button :pending="submitBatchReq.pending.value" @click="submitBatch" type="edge" class="" >
            Submit
          </Button>
        </template>
        <div class="bg-green-300 rounded-xl ml-auto px-3 text-[10px] font-extrabold" v-if="orderReq.response.value?.purchaseOrderStatus == 'DONE'" >DONE</div>
      </div>
    </div>
    <Form id="purchase-order">
      <Table
        :pending="orderReq.pending.value"
        :headers="{
          head: [
            'Drug Name',
            'Unit',
            'Supplier',
            'Quantity',
            'Unit Price',
            'Total Amount',
          ],
          row: ['drugName', 'dosageForm', 'requestedAmount'],
        }"
      >
        <template #row="{ rows }">
          <tr
            :key="row.purchaseOrderUuid"
            class="bg-white"
            v-for="(row, idx) in orders.map((el) => ({
              ...el,
              supplier: orderReq.response.value?.supplierName,
            })).filter(el => el?.itemName?.toLowerCase().includes(search.toLowerCase()))"
          >
            <TableNumberCell :index="idx" />
            <td>{{ row?.itemName }}</td>
            <td class="p-3">{{ row?.unit }}</td>
            <td>{{ row?.supplier }}</td>
            <td class="p-3">
              <div class="max-w-[6rem] py-1.5 px-3 border border-gray-500 rounded-md">
                <input
                  class="max-w-full"
                  placeholder="Enter Quantity"
                  v-model="thisItem(row.itemName).quantity"
                />
              </div>
            </td>
            <td class="p-3">
              <div class="max-w-[6rem] py-1.5 px-3 border border-gray-500 rounded-md">
                <input
                  class="max-w-full"
                  placeholder="Enter Unit Price"
                  v-model="thisItem(row.itemName).unitPrice"
                />
              </div>
            </td>
            <td class="p-3">
              {{ formatCurrency(getTotalConst(row)) }}
            </td>
            <td class="p-3">
              <Button :pending="edit.includes(row?.purchaseOrderUuid)" @click.prevent="updateDrugState(row)" type="primary" size="xs">update</Button>
            </td>
          </tr>
        </template>
      </Table>
    </Form>
  </DefaultPage>
</template>
