<script setup>
import BaseIcon from "@/components/base/BaseIcon.vue";
import Button from "@/components/Button.vue";
import Dropdown from "@/components/Dropdown.vue";
import Table from "@/components/Table.vue";
import { usePagination } from "@/composables/usePagination";
import icons from "@/utils/icons";
import { openModal } from "@customizer/modal-x";
import { Icon } from "@iconify/vue";
import { mdiMagnify, mdiPlus } from "@mdi/js";
import { ref } from "vue";
import { useRouter } from "vue-router";
import RequestedCell from "../components/RequestedCell.vue";
import Select from "@/components/new_form_elements/Select.vue";
import DefaultPage from "@/components/DefaultPage.vue";
import { getPurchaseRequest } from "../Api/procurementApi";
import { useProcurementRequest } from "../store/procurementStore";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import Input from "@/components/new_form_elements/Input.vue";
import { openNewTab, secondDateFormat } from "@/utils/utils";
import Form from "@/new_form_builder/Form.vue";
import { getAllPurchaseRequest } from "../Api/PurchaseRequestApi";

const purchaseRequestStore = useProcurementRequest();
const pagination = usePaginationcopy({
  store: purchaseRequestStore,
  cb: getAllPurchaseRequest,
});

const search = ref("");
const router = useRouter();
const dateValue = ref("");
</script>

<template>
  <DefaultPage>
    <div class="flex justify-between items-center">
      <div></div>
      <div class="flex gap-2">
        <div class="flex border rounded px-4 items-center bg-accent">
          <input
            v-model="pagination.search.value"
            class="border-0 h-10 rounded-md bg-transparent placeholder:text-text-clr placeholder:opacity-80"
            placeholder="Search request "
          />
          <BaseIcon :path="mdiMagnify" :size="20" />
        </div>
      </div>
    </div>
    <div class="">
      <Table
        @row="(row) => openNewTab(`${baseUrl}/model20/${row?.batchUuid}`)"
        :pending="pagination.pending.value"
        :headers="{
          head: ['Branch', 'Sent By', 'Request Sent Date', 'Actions'],
          row: ['branchName', 'sentBy', 'sentDate'],
        }"
        :rows="purchaseRequestStore.purchaseRequest"
        :cells="{
          sentDate: (_, row) => secondDateFormat(row?.requestedDate),
        }"
      >
        <template #actions="{ row }">
          <div class="flex gap-2">
            <Button
              @click.stop=""
              type="edge"
              size="xs"
              >View</Button
            >
          </div>
        </template>
      </Table>
    </div>
  </DefaultPage>
</template>

<style scoped>
:deep(.white-placeholder select) {
  color: white !important;
}

:deep(.white-placeholder select option:first-child) {
  color: white !important;
}

:deep(.white-placeholder select option) {
  color: #333 !important;
  background-color: white !important;
}
</style>
