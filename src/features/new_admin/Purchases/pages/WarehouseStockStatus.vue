<script setup>
import Button from "@/components/Button.vue";
import DefaultPage from "@/components/DefaultPage.vue";
import Table from "@/components/Table.vue";
import BaseIcon from "@/components/base/BaseIcon.vue";
import Input from "@/components/new_form_elements/Input.vue";
import Form from "@/new_form_builder/Form.vue";
import { mdiMagnify } from "@mdi/js";
import { getWarehouseStock } from "../Api/warehouseApi";
</script>
<template>
  <div class="flex justify-between items-center">
    <DefaultPage>
      <div class="flex justify-end gap-4 rounded h-12 px-4 items-center">
        <input
          class="border h-11 px-3 rounded-md bg-transparent placeholder:text-text-clr placeholder:opacity-80"
          placeholder="Search"
        />
        <BaseIcon :path="mdiMagnify" :size="20" />
      </div>
      <Table  
        :api="{
          key: 'content',
          cb: (data) => getWarehouseStock(data),
        }"
        :headers="{
          head: [
            'Drug Code',
            'Drug Name',
            'Unit',
            'Dosage Form',
            'Brand Name',
            'Drug Quantity',
            'Unit Price',
            'Batch Number',
            'EXP Date',
            'actions',
          ],
          row: [
            'drugCode',
            'drugName',
            'ofX',
            'dispensingUnit',
            'drugBrandName',
            'totalAmount',
            'unitPrice',
            'batchNumber',
            'expDate',
          ],
        }"
        
        :rows="[]"
      >
        <template #actions="{ row }">
          <div class="flex items-center gap-2">
            <button disabled class="border px-4 py-1 rounded border-black/50">
              Bin Card
            </button>
           
          </div>
        </template>
      </Table>
    </DefaultPage>
  </div>
</template>
