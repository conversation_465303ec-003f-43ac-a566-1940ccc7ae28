<script setup>
import BaseIcon from "@/components/base/BaseIcon.vue";
import Button from "@/components/Button.vue";
import Dropdown from "@/components/Dropdown.vue";
import Table from "@/components/Table.vue";
import { usePagination } from "@/composables/usePagination";
import icons from "@/utils/icons";
import { openModal } from "@customizer/modal-x";
import { Icon } from "@iconify/vue";
import { mdiMagnify, mdiPlus } from "@mdi/js";
import { ref } from "vue";
import { useRouter } from "vue-router";
import RequestedCell from "../components/RequestedCell.vue";
import Select from "@/components/new_form_elements/Select.vue";
import DefaultPage from "@/components/DefaultPage.vue";
import { getPurchaseRequest } from "../Api/procurementApi";
import { useProcurementRequest } from "../store/procurementStore";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import Input from "@/components/new_form_elements/Input.vue";
import { getAllPurchaseRequestDetail } from "../Api/PurchaseRequestApi";
import { useApiRequest } from "@/composables/useApiRequest";
import { createPurchaseOrder } from "../Api/purchaseOrderApi";
import { addDayToDate, formatDateToDDMMYY } from "@/utils/utils";

const purchaseRequestStore = useProcurementRequest();
const filter = ref("");

const pagination = usePaginationcopy({
  watch: [filter],
  store: purchaseRequestStore,
  cb: (data) => getAllPurchaseRequestDetail({...data, limit: 4000, filter: filter.value}),
});

const router = useApiRequest()

const createOrderReq = useApiRequest();
function createPurchase() {
  if (createOrderReq.pending.value) return;

  openModal("Confirmation", {
    title: 'Create Purcahse Order',
    message: 'Are you sure you want to create a purchase order?',
  }, (res) => {
    if (res) {
      createOrderReq.send(
        () => createPurchaseOrder(),
        (res) => {
          if(res.success) {
            router.push('/inpurchaseprocess');
          }
        }
      );
    }
  });
}
</script>

<template>
  <DefaultPage>
    <div class="flex border-b pb-4 justify-between items-center">
      <div class="flex items-center gap-2">
        Filter 
        <select v-model="filter" class="p-3 rounded-md border">
          <option selected value="" disabled>Select</option>
          <option :value="formatDateToDDMMYY(addDayToDate(new Date(), -30))" >Last Month</option>
          <option :value="formatDateToDDMMYY(addDayToDate(new Date(), -90))" >Last Quarter</option>
          <option :value="formatDateToDDMMYY(addDayToDate(new Date(), -180))" >Last Half Year</option>
          <option :value="formatDateToDDMMYY(addDayToDate(new Date(), -365))" >Last Annual</option>
        </select>
        <Button
          :pending="createOrderReq.pending.value"
          @click="createPurchase()"
          v-if="filter"
          type="primary"
          class="h-11"
          >Start Purchase Order</Button
        >
      </div>
      <div class="flex gap-2">
        <div class="flex h-11 border rounded px-4 items-center bg-accent">
          <BaseIcon :path="mdiMagnify" :size="20" />
          <input
            v-model="pagination.search.value"
            class="bg-transparent placeholder:text-text-clr placeholder:opacity-80"
            placeholder="Search request "
          />
        </div>
        <!-- <Button type="primary"
					class="flex gap-4 text-white rounded h-14 items-center">
					<p class="opacity-80">New Purchase Order</p>
				</Button> -->
      </div>
    </div>
    <div class="">
      <Table
        :pending="pagination.pending.value"
        :headers="{
          head: [
            'Requested ID',
            'Dosage Form',
            'Quantity',
            // 'Actions'
          ],
          row: ['drugName', 'dosageForm', 'requestedAmount'],
        }"
        :rows="purchaseRequestStore.purchaseRequest || []"
        :cells="{
          status: RequestedCell,
          managerName: (_, row) => {
            return '';
          },
          measure: (_, row) => {
            return '';
          },
          requestDate: (_, row) => {
            return '';
          },
          branchCount: (_, row) => {
            return '';
          },
        }"
      >
        <template #actions="{ row }">
          <Button
            @click.prevent="
              $router.push('/purchaserequests/view/' + row?.requestGroupId)
            "
            class="text-sm text-white py-[6px] px-3 rounded-sm"
            type="secondary"
            >View</Button
          >
        </template>
      </Table>
    </div>
  </DefaultPage>
</template>

<style scoped>
:deep(.white-placeholder select) {
  color: white !important;
}

:deep(.white-placeholder select option:first-child) {
  color: white !important;
}

:deep(.white-placeholder select option) {
  color: #333 !important;
  background-color: white !important;
}
</style>
