<script setup>
import BaseIcon from '@/components/base/BaseIcon.vue';
import Button from '@/components/Button.vue';
import Dropdown from '@/components/Dropdown.vue';
import Table from '@/components/Table.vue';
import { usePagination } from '@/composables/usePagination';
import icons from '@/utils/icons';
import { openModal } from '@customizer/modal-x';
import { Icon } from '@iconify/vue';
import { mdiCalendar, mdiMagnify, mdiPlus } from '@mdi/js';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import RequestedCell from '../components/RequestedCell.vue';
import Select from '@/components/new_form_elements/Select.vue';
import DefaultPage from '@/components/DefaultPage.vue';
import TableWithCheckBox from '@/components/TableWithCheckBox.vue';
import { usePaginationcopy } from '@/composables/usePaginationcopy';
import { useApproval } from '../store/ApprovalStore';
import { getApprovedRequest } from '../Api/procurementApi';
import ApprovedQuantityCell from '../components/ApprovedQuantityCell.vue';

const approvalStore = useApproval()
const pagination = usePaginationcopy({
	store: approvalStore,
	cb: getApprovedRequest,
});
const search = ref('')
const router = useRouter()
const dateValue = ref('');
const selectedRows = ref([]);

const tempData = ref([
	{
		requestedId: 'KT23874639',
		drugDetail: 'Paracetamol 250 mg Tablet',
		quantity: '4,534 packs',
		branches: '12 Branches',
	},
	{
		requestedId: 'KT23874639',
		drugDetail: 'Ibuprofen 500 mg Tablet',
		quantity: '434 st',
		branches: '13 Branches',
	},
	{
		requestedId: 'KT23874639',
		drugDetail: 'Amoxicillin 500 mg Capsule',
		quantity: '234 caps',
		branches: '11 Branches',
	},
	{
		requestedId: 'KT23874639',
		drugDetail: 'Simvastatin 20 mg Tablet',
		quantity: '434 st',
		branches: '15 Branches',
	},
	{
		requestedId: 'KT23874639',
		drugDetail: 'Amoxicillin 500 mg Capsule',
		quantity: '234 caps',
		branches: '10 Branches',
	},
	{
		requestedId: 'KT23874639',
		drugDetail: 'Amoxicillin 500 mg Capsule',
		quantity: '234 caps',
		branches: '16 Branches',
	},
	{
		requestedId: 'KT23874639',
		drugDetail: 'Amoxicillin 500 mg Capsule',
		quantity: '234 caps',
		branches: '9 Branches',
	},
	{
		requestedId: 'KT23874639',
		drugDetail: 'Amoxicillin 500 mg Capsule',
		quantity: '234 caps',
		branches: '18 Branches',
	},
]);

</script>

<template>
	<DefaultPage>
		<div class="flex justify-between items-center">
			<div class="grid grid-cols-2 gap-2 items-center">
				<div class="flex items-center gap-1 bg-white px-3 rounded h-12 border focus-within:border-primary">
					<BaseIcon :path="mdiCalendar" :size="20" class="text-gray-500" />
					<input v-model="dateValue" class="w-full border-none outline-none bg-transparent text-sm" type="date"
						placeholder="Date" onfocus="(this.type='date')" onblur="if(!this.value) this.type='text'">
				</div>
			</div>
			<div class="flex gap-2">
				<div class="flex border rounded px-4 items-center bg-accent">
					<input v-model="pagination.search.value"
						class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80"
						placeholder="Search Purchases " />
					<BaseIcon :path="mdiMagnify" :size="20" />
				</div>
				<Button @click.prevent="openModal('ApprovalConfirmation', selectedRows)" type="primary"
					class="flex gap-4 text-white rounded h-14 items-center">
					<p class="opacity-80">To Purchase Process</p>
				</Button>
			</div>
		</div>
		<div class="custom-table">
			<TableWithCheckBox v-model="selectedRows" :last="false" to-be-selected="requestUuid"
				:pending="pagination.pending.value" :headers="{
					head: [
						'Requested ID',
						'Drug Detail',
						'Request Date',
						'Requested Quantity',
						'Approved Quantity',
						'Branches',
						'Status',
						// 'Actions'
					],
					row: [
						'requestedId',
						'drugDetail',
						'requestDate',
						'totalRequestedQuantity',
						'totalApprovedQuantity',
						'branchCount',
						'status',
					],
				}" :rows="(approvalStore.approval || [])" :cells="{
					status: RequestedCell,
					totalApprovedQuantity: ApprovedQuantityCell,
					drugDetail: (_, row) => {
						return row.drugDetails.map((el) => el?.drugName).join(', ');
					},
					branchCount: (_, row) => {
						return `${row.branchCount} branches`
					}
				}">
				<!-- <template #actions="{ row }">
					<Button @click='$router.push("/purchaserequests/view" + row?.requestedUuid)'
						class="text-sm text-white py-[6px] px-3 rounded-sm" type="secondary">View</Button>
				</template> -->
			</TableWithCheckBox>
		</div>
	</DefaultPage>
</template>
