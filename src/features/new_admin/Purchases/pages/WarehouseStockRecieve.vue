<script setup>
import Button from "@/components/Button.vue";
import DefaultPage from "@/components/DefaultPage.vue";
import BaseIcon from "@/components/base/BaseIcon.vue";
import Table from "@/components/Table.vue";
import { mdiMagnify } from "@mdi/js";
import { getAllPurchaseOrder } from "../Api/purchaseOrderApi";
import { secondDateFormat } from "@/utils/utils";
</script>

<template>
  <DefaultPage>
    <div class="flex justify-end gap-4 items-center">
      <div class="flex gap-4 border rounded h-12 px-4 items-center bg-accent">
        <input
          class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80"
          placeholder="Search"
        />
        <BaseIcon :path="mdiMagnify" :size="20" />
      </div>
      <!-- <Button type="primary" class="h-11">Start Purchase Order</Button> -->
    </div>
    <Table
      :api="{
				key: 'content',
        cb: (data) => getAllPurchaseOrder({ ...data, filter: 'DONE' }),
      }"
      :headers="{
        head: ['PO ID', 'Supplier', 'PO Date', 'PO Status', 'Actions'],
        row: [
          'purchaseOrderNumber',
          'supplierName',
          'poDate',
          'purchaseOrderStatus',
        ],
      }"
      :cells="{
        poDate: secondDateFormat,
      }"
    >
			<template #actions="{ row }" >
				<Button @click="$router.push(`/stock-recieve-detail/${row?.purchaseOrderUuid}`)" size="xs" type="edge" >Detail</Button>	
			</template>
		</Table>
  </DefaultPage>
</template>
