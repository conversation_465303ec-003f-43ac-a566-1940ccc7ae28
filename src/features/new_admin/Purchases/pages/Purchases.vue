<script setup lang="ts">
import BaseIcon from '@/components/base/BaseIcon.vue';
import Button from '@/components/Button.vue';
import Dropdown from '@/components/Dropdown.vue';
import Table from '@/components/Table.vue';
import { usePagination } from '@/composables/usePagination';
import icons from '@/utils/icons';
import { Icon } from '@iconify/vue';
import { mdiMagnify, mdiPlus } from '@mdi/js';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import PurchaseCell from '../components/PurchaseCell.vue';


const pagination = usePagination({
	cb: (data) => ({ ...data, limit: 25 }),
});
const search = ref('')
const router = useRouter()

const tempData = ref([
	{
		drugId: 'k39',
		drugDetail: 'Paracetamol 250 mg Tablet',
		unitOfMeasure: 'PACK',
		quantity: '45',
		remark: 'Should be urgently distributed ',
		status: 'Purchases',
	},
	{
		drugId: 'k39',
		drugDetail: 'Paracetamol 250 mg Tablet',
		unitOfMeasure: 'PACK',
		quantity: '45',
		remark: 'Should be urgently distributed ',
		status: 'Purchases',
	},
	{
		drugId: 'k39',
		drugDetail: 'Paracetamol 250 mg Tablet',
		unitOfMeasure: 'PACK',
		quantity: '45',
		remark: 'Should be urgently distributed ',
		status: 'Purchases',
	},
	{
		drugId: 'k39',
		drugDetail: 'Paracetamol 250 mg Tablet',
		unitOfMeasure: 'Box',
		quantity: '45',
		remark: 'Should be urgently distributed ',
		status: 'Purchases',
	},
	{
		drugId: 'k39',
		drugDetail: 'Paracetamol 250 mg Tablet',
		unitOfMeasure: 'PACK',
		quantity: '45',
		remark: 'Should be urgently distributed ',
		status: 'Purchases',
	},
	{
		drugId: 'k39',
		drugDetail: 'Paracetamol 250 mg Tablet',
		unitOfMeasure: 'PACK',
		quantity: '45',
		remark: 'Should be urgently distributed ',
		status: 'Purchases',
	},
	{
		drugId: 'k39',
		drugDetail: 'Paracetamol 250 mg Tablet',
		unitOfMeasure: 'BOX',
		quantity: '45',
		remark: 'Should be urgently distributed ',
		status: 'Purchases',
	},
	{
		drugId: 'k39',
		drugDetail: 'Paracetamol 250 mg Tablet',
		unitOfMeasure: 'Strip',
		quantity: '45',
		remark: 'Should be urgently distributed ',
		status: 'Purchases',
	},

]);
</script>

<template>
	<div class="pt-16 px-12 flex flex-col gap-6">
		<div class="flex justify-between items-center">
			<p class="font-bold text-lg text-[#1D1C1B]">Purchases</p>

			<div class="flex border rounded px-4 py-4 items-center bg-accent">
				<input v-model="pagination.search.value"
					class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80"
					placeholder="Search Purchases " />
				<BaseIcon :path="mdiMagnify" :size="20" />
			</div>

		</div>
		<div class="flex-1">
			<Table :pending="pagination.pending.value" :headers="{
				head: [
					'Drug ID',
					'Drug Detail',
					'Unit Of Measure',
					'Quantity',
					'Remark',
					'Status',
				],
				row: [
					'drugId',
					'drugDetail',
					'unitOfMeasure',
					'quantity',
					'remark',
					'status',
				],
			}" :rows="(tempData || [])" :cells="{
				status: PurchaseCell
			}">
				<template #actions="{ row }">
					<Button class="text-sm text-white py-[6px] px-3 rounded-sm" type="secondary">Process</Button>
				</template>
			</Table>
		</div>
	</div>
</template>