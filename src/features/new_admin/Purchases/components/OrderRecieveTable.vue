<script setup>
import Table from "@/components/Table.vue";
import { formatCurrency } from "@/utils/utils";

const props = defineProps({
  pending: Boolean,
  rows: {
    type: Array,
    default: [],
  },
});

const thisItem = computed(() => {
  return (id) => props.rows?.find?.((el) => id == el.itemName);
});
</script>
<template>
  <Table
    :pending="pending"
    :headers="{
      head: [
        'Drug Name',
        'Unit',
        'Supplier',
        'Quantity',
        'Unit Price',
        'Total Amount',
      ],
      row: ['drugName', 'dosageForm', 'requestedAmount'],
    }"
  >
    <template>
      <tr
        :key="row.purchaseOrderUuid"
        class="bg-white"
        v-for="(row, idx) in rows
          .map((el) => ({
            ...el,
            supplier: orderReq.response.value?.supplierName,
          }))
          .filter((el) =>
            el?.itemName?.toLowerCase().includes(search.toLowerCase())
          )"
      >
        <TableNumberCell :index="idx" />
        <td>{{ row?.itemName }}</td>
        <td class="p-3">{{ row?.unit }}</td>
        <td>{{ row?.supplier }}</td>
        <td class="p-3">
          <div
            class="max-w-[6rem] py-1.5 px-3 border border-gray-500 rounded-md"
          >
            <input
              class="max-w-full"
              placeholder="Enter Quantity"
              v-model="thisItem(row.itemName).quantity"
            />
          </div>
        </td>
        <td class="p-3">
          <div
            class="max-w-[6rem] py-1.5 px-3 border border-gray-500 rounded-md"
          >
            <input
              class="max-w-full"
              placeholder="Enter Unit Price"
              v-model="thisItem(row.itemName).unitPrice"
            />
          </div>
        </td>
        <td class="p-3">
          {{ formatCurrency(getTotalConst(row)) }}
        </td>
        <td class="p-3">
          <!-- <Button :pending="edit.includes(row?.purchaseOrderUuid)" @click.prevent="updateDrugState(row)" type="primary" size="xs">update</Button> -->
        </td>
      </tr>
    </template>
  </Table>
</template>
