<script setup>
import Input from '@/components/new_form_elements/Input.vue';
import NewFormParent from '../../role/components/NewFormParent.vue';
import Button from '@/components/Button.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import { closeModal } from '@customizer/modal-x';
import { approveRequest } from '../Api/procurementApi';
import { toasted } from '@/utils/utils';
import { ref } from 'vue';

const props = defineProps({
	data: {
		type: Array,
		required: true
	}
})

console.log(props.data);
const approvedQuantity = ref(0);
const fullApproval = ref(false);


const req = useApiRequest()
const drugNames = props.data?.items?.map?.((el) => el.drugName);
const quantity = props.data?.items?.map?.((el) => el.quantity);

function approve() {
	const drugUuid = props.data?.items.map((el) => el.drugUuid);
	const RequestData = {
		fullApproval: fullApproval.value,
		approvalNotes: 'Approved',
		itemAdjustments: props.data?.items.map((el) => ({
			drugUuid: el.drugUuid,  // Single UUID (not array)
			approvedQuantity: approvedQuantity.value
		}))

	};
	req.send(() => approveRequest(props.data?.requestUuid, RequestData),
		(res) => {

			toasted(res.success, 'Request Approved Successfully', res.error);
			closeModal();
		})
}

</script>
<template>
	<div class="bg-black/50 h-55 p-6 rounded-lg min-h-full w-full grid place-items-center">
		<NewFormParent size='sm' title="Purchase Requests Approval">
			<div class="flex flex-col gap-6 pt-2">
				<div class="grid grid-cols-2 gap-4">
					<div class="flex justify-between bg-[#F8F8F8] py-3 px-4 items-center">
						<p class="opacity-60 font-normal text-base">Drug Detail</p>
						<p class="font-normal text-base ml-16">{{ drugNames?.join?.(', ') }}</p>
					</div>
					<div class="flex justify-between bg-[#F8F8F8] py-3 px-4 items-center">
						<p class="opacity-60 font-normal text-base">Requested Quantity</p>
						<p class="font-bold text-base">{{ quantity?.join?.(', ') }} </p>
					</div>
				</div>
				<div class="custom-input">
					<Input name="quantity" v-model="approvedQuantity" label="Approved Quantity" :focus="true" :attributes="{
						placeholder: 'Enter Approved Quantity'
					}" />
				</div>
				<div class="flex justify-between">
					<div class="flex items-center gap-2">
						<input type="checkbox" id="fullApproval" v-model="fullApproval" name="fullApproval" class="w-4 h-4">
						<label for="fullApproval" class="text-md font-bold">Full Approval</label>
					</div>
					<div class="flex gap-6">
						<Button class="bg-[#EE6363] text-white">Reject</Button>
						<Button @click.prevent="approve" class="bg-[#522A1F] text-white">Approve</Button>
					</div>
				</div>
			</div>
		</NewFormParent>
	</div>
</template>

<style scoped>
.custom-input :deep(input:focus) {
	border-color: #ED6033 !important;
	box-shadow: 0 8.9px 0 rgba(237, 96, 51, 0.16) !important;
	outline: none !important;
}

.custom-input :deep(.focus-within\:sys-focus:focus-within) {
	border-color: #ED6033 !important;
	/* box-shadow: 0 8.9px 0 rgba(237, 96, 51, 0.16) !important; */
}

/* .custom-input :deep(input) {
	border: 1px solid transparent;
	transition: all 0.2s ease;
}

.custom-input :deep(input:focus) {
	border: 1px solid #ED6033;
	box-shadow: 0 8.9px 0 rgba(237, 96, 51, 0.16);
} */
</style>