<script setup lang="ts">
import Button from "@/components/Button.vue";
import SearchSelect from "@/components/SearchSelect.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { getAllSuppliers } from "@/features/admin/supplier/api/SupplierApi";
import Form from "@/new_form_builder/Form.vue";
import icons from "@/utils/icons";
import { createPurchaseOrder } from "../Api/purchaseOrderApi";
import { closeModal } from "@customizer/modal-x";
import { usePurchaseOrder } from "../store/purchaseOrder";
import { toasted } from "@/utils/utils";
import { ref } from "vue";

const purchaseOrderStore = usePurchaseOrder();
const supplier = ref('')

const orderReq = useApiRequest();
function addPurchaseOrder({ values }) {
  if (orderReq.pending.value) return;

  orderReq.send(
    () => createPurchaseOrder({
			supplierName: supplier.value
		}),
    (res) => {
      if (res.success) {
				purchaseOrderStore.add(res.data)
        closeModal();
      }
			toasted(res.success, 'PO Created', res.error);
    }
  );
}

</script>

<template>
  <div class="gap-9 bg-[#4C4B4B66] h-full grid place-items-center">
    <div class="flex flex-col w-[30rem] gap-4 bg-white rounded-lg py-11 px-8">
      <div class="flex items-center justify-between border-b pb-4">
        <span class="font-bold text-base" >Add Supplier</span>
				<button class="border p-2 rounded-full" @click="closeModal()" >
					<i v-html="icons.close" class="*:size-2" />
				</button>
      </div>
      <Form v-slot="{ submit }" id="payment-order" class="grid grid-cols-1">
        <SearchSelect
					v-model="supplier"
          label="Supplier"
					name="supplierName"
          :option="{ label: 'supplierName', value: 'supplierUuid' }"
          :search-cb="getAllSuppliers"
          placeholder="Search Supplier"
        />
        <Button @click.prevent="submit(addPurchaseOrder)" class="col-span-2 mt-10" type="primary"> Create PO </Button>
      </Form>
    </div>
  </div>
</template>
