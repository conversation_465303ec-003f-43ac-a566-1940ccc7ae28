<script setup>
import Table from '@/components/Table.vue';
import NewFormParent from '../../role/components/NewFormParent.vue';
import Select from '@/components/new_form_elements/Select.vue';
import { closeModal } from '@customizer/modal-x';
import ApprovalConfirmationCell from './ApprovalConfirmationCell.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import { toasted } from '@/utils/utils';
import { confirmApprovals } from '../Api/procurementApi';
import { ref } from 'vue';

const props = defineProps({
	data: {
		type: Array,
		required: true
	},
})

console.log(props.data);
const selected = ref([]);
const req = useApiRequest()

function confirm() {
	req.send(() => confirmApprovals({
		purchaseType: selected.value
	}),
		(res) => {
			toasted(res.success, 'Request Confirmed Successfully', res.error);
			closeModal();
		})
}

</script>

<template>
	<div @click.self="closeModal()" class="bg-black/50 h-55 p-6 rounded-lg min-h-full w-full grid place-items-center">
		<NewFormParent size='' title="Approval Confirmation">
			<div class="flex flex-col gap-6 pt-2">
				<p class="font-normal text-base opacity-80">Selected Requestes</p>

				<Table :headers="{
					head: [
						'Requested ID',
						'Drug Detail',
						'Requested Quantity',
						'Approved Quantity',
						'Branches',
					],
					row: [
						'requestedId',
						'drugDetail',
						'requestedQuantity',
						'approvedQuantity',
						'branchCount',
					],
				}" :rows="(props.data || [])" :cells="{
					drugDetail: (_, row) => {
						return row.drugDetails.map((el) => el?.drugName).join(', ');
					},
					requestedQuantity: (_, row) => {
						return row.drugDetails.map((el) => el?.requestedQuantity).reduce((acc, cur) => acc + cur, 0);
					},
					approvedQuantity: ApprovalConfirmationCell,
					branchCount: (_, row) => {
						return `${row.branchCount} branches`
					}
				}">

				</Table>
				<hr class="w-[921px] opacity-30 border tect-[#55291B]">
				<div>
					<Select v-model="selected" name="typeOfPurchase" label="Type of Purchase"
						:Arguments="{ placeholder: 'Select type of purchse' }"
						:options="['REGULAR', 'EMERGENCY', 'CAPITAL', 'CONTRACT', 'BLANKET', 'CONSIGNMENT', 'DIRECT', 'TENDER', 'FRAMEWORK', 'QUOTA', 'BACKORDER', 'DISCOUNT', 'TRIAL', 'DONATION', 'REPLACEMENT', 'PATIENT_SPECIFIC', 'CLINICAL_TRIAL', 'GOVERNMENT', 'DISASTER', 'SEASONAL', 'PROMOTIONAL']" />
				</div>
			</div>
			<div class="flex justify-end">
				<button @click.prevent="confirm"
					class="bg-primary text-white py-2 px-[34px] border-[0.38px] rounded-[4px] flex gap-1 items-center">
					To Purchase Process
				</button>
			</div>
		</NewFormParent>
	</div>
</template>