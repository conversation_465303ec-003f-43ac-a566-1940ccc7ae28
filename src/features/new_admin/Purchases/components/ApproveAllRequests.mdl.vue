<script setup>
import Input from '@/components/new_form_elements/Input.vue';
import NewFormParent from '../../role/components/NewFormParent.vue';
import Button from '@/components/Button.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import { closeModal } from '@customizer/modal-x';
import { approveGroupRequest, approveRequest } from '../Api/procurementApi';
import { toasted } from '@/utils/utils';
import { ref } from 'vue';

const props = defineProps({
	data: {
		type: Array,
		required: true
	}
})

console.log(props.data);
const req = useApiRequest()
const fullApproval = ref(true);

function approve() {
	const drugUuid = props.data[0]?.items[0]?.drugUuid;
	const itemAdjustments = props.data.flatMap(parentItem => {
		return parentItem.items.map(item => ({
			drugUuid: item.drugUuid,
			approvedQuantity: item.quantity // Using the item's quantity
		}));
	});

	// Prepare request data
	const RequestData = {
		fullApproval: fullApproval.value,
		approvalNotes: 'Approved',
		itemAdjustments: itemAdjustments
	};

	req.send(() => approveGroupRequest(drugUuid, RequestData),
		(res) => {
			toasted(res.success, 'Request Approved Successfully', res.error);
			closeModal();
		})
}

</script>
<template>
	<div class="bg-black/50 h-55 p-6 rounded-lg min-h-full w-full grid place-items-center">
		<NewFormParent size='sm' title="Approve All Requests?">
			<div class="flex flex-col gap-6 pt-2">
				<p>Do you confirm your approval for all selected ({{ props.data.length }}) items?</p>
				<div class="flex justify-end gap-6">
					<Button @click="closeModal()" class="bg-[#EE6363] text-white">No</Button>
					<Button @click.prevent="approve" class="bg-[#522A1F] text-white">Confirm</Button>
				</div>
			</div>
		</NewFormParent>
	</div>
</template>

<style scoped></style>