<script setup>
import Form from '@/new_form_builder/Form.vue';
import NewFormParent from '../../role/components/NewFormParent.vue';
import Input from '@/components/new_form_elements/Input.vue';
import Table from '@/components/Table.vue';
import { usePagination } from '@/composables/usePagination';
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import PurchaseTableRow from './PurchaseTableRow.vue';
import Table2 from '@/components/Table2.vue';
import Button from '@/components/Button.vue';
import Textarea from '@/components/new_form_elements/Textarea.vue';

const props = defineProps({
	data: {
		type: Object,
		required: true
	}
})

console.log(props.data);


const pagination = usePagination({
	cb: (data) => ({ ...data, limit: 25 }),
});

const tableData = computed(() => {
	return Object.values(props.data)
})

console.log(tableData);


</script>

<template>
	<div class="bg-black/50 h-55 p-10 min-h-full w-full grid place-items-center">
		<NewFormParent size='xs' title="Purchase Detail">
			<div class="flex flex-col gap-6">
				<div class="">
					<Form class="grid grid-cols-3 gap-6 p-6 rounded-[4px] bg-[#F1F1F1]" v-slot="{ submit }" id="purchaseform">
						<div class="flex gap-2 rounded-[4px] px-4 py-3 items-center bg-white">
							<label class="text-sm border-r-2 pr-4">Branch</label>
							<input class="!border-none !bg-transparent !shadow-none !ring-0 focus:!ring-0 focus:!border-none"
								name="branchName" />
						</div>
						<div class="flex gap-2 rounded-[4px] px-4 py-3 items-center bg-white">
							<label class="text-sm border-r-2 pr-4">Requested by</label>
							<input class="!border-none !bg-transparent !shadow-none !ring-0 focus:!ring-0 focus:!border-none"
								name="requestedBy" />
						</div>
						<div class="flex gap-2 rounded-[4px] px-4 py-3 items-center bg-white">
							<label class="text-sm border-r-2 pr-4">Requested on</label>
							<input class="!border-none !bg-transparent !shadow-none !ring-0 focus:!ring-0 focus:!border-none"
								name="requestedOn" />
						</div>
						<div class="flex gap-2 col-span-3 rounded-[4px] px-4 py-3 items-center bg-white">
							<label class="text-sm border-r-2 pr-4">Remark</label>
							<input class="!border-none !bg-transparent !shadow-none !ring-0 focus:!ring-0 focus:!border-none"
								name="remark" />
						</div>
					</Form>
				</div>
				<div class="bg-[#F1F1F1] flex-1">
					<Table2 :headers="{
						head: [
							'Drug Detail',
							'Unit Of Measure',
							'Quantity',
							'Received Amount',
							'Unit Price',
							'Actions'
						],
						row: [
							'drugDetail',
							'unitOfMeasure',
							'quantity',
							'receivedAmount',
							'unitPrice',
						],
					}" :rows="([data] || [])" :cells="{

					}">
						<template #actions="{ row }">
							<Button class="text-sm text-white py-[6px] px-3 rounded-sm" type="secondary">Mark as Received</Button>
						</template>
					</Table2>
				</div>
				<div class="flex justify-end">
					<button class="bg-primary text-white py-2 px-[34px] border-[0.38px] rounded-[4px] flex gap-1 items-center">
						Save as Purchased
					</button>
				</div>
			</div>
		</NewFormParent>
	</div>
</template>