<script setup lang="ts">
import DrugRecieveForm from "@/features/StoreKeeper/components/DrugRecieveForm.vue";
import NewFormParent from "../../role/components/NewFormParent.vue";
import ModalParent from "@/components/ModalParent.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { closeModal, openModal } from "@customizer/modal-x";
import { recieveDrug } from "../Api/warehouseApi";
import { toasted } from "@/utils/utils";
const props = defineProps({
  data: Object,
});

const req = useApiRequest();
function recieve(values) {
  if (req.pending.value) return;

  openModal(
    "Confirmation",
    {
      title: "Recieve Drug",
      message: "Are you sure you want to recieve this drug?",
    },
    (res) => {
      if (res) {
        req.send(
          () =>
            recieveDrug({
              supplierUuid: values.supplierName,
              ...values,
							...(props.data || {})
            }),
          (res) => {
            if (res.success) {
              toasted(res.success, "Drug Recieved", res.error);
              closeModal();
            }
          }
        );
      }
    }
  );
}
</script>

<template>
  <ModalParent>
    <NewFormParent size="lg" title="Recieve Drug">
      <DrugRecieveForm
        :onSubmit="recieve"
        :disabledWithValue="true"
        :filter="{
          supplier: false,
          compounding: false,
          unit: false,
          c: false,
          by: false,
        }"
        :drug="{ ...props.data, drugName: props.data?.itemName }"
      />
    </NewFormParent>
  </ModalParent>
</template>
