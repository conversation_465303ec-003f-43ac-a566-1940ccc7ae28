<script setup>
import Form from '@/new_form_builder/Form.vue';
import NewFormParent from '../../role/components/NewFormParent.vue';
import Input from '@/components/new_form_elements/Input.vue';
import Table from '@/components/Table.vue';
import { usePagination } from '@/composables/usePagination';
import { computed, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import PurchaseTableRow from './PurchaseTableRow.vue';
import Table2 from '@/components/Table2.vue';
import Button from '@/components/Button.vue';
import Textarea from '@/components/new_form_elements/Textarea.vue';
import Select from '@/components/new_form_elements/Select.vue';
import NewPurchaseTableRow from './NewPurchaseTableRow.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import { getAllDrug } from '../../drug/Api/drugApi';
import { allRequest, toasted } from '@/utils/utils';
import MultipleSelect from '@/components/new_form_elements/MultipleSelect.vue';
import { createRequest } from '../Api/PurchaseRequestApi';
import { useAuth } from '@/stores/auth';
import { closeModal } from '@customizer/modal-x';
import ConfirmProcurementTableRow from './ConfirmProcurementTableRow.vue';

const pagination = usePagination({
	cb: (data) => ({ ...data, limit: 25 }),
});


</script>

<template>
	<div @click.self="closeModal()" class="bg-black/50 h-55 p-10 min-h-full w-full grid place-items-center">
		<NewFormParent size='xs' title="Confirm Procurement">
			<div class="flex flex-col gap-6 pt-6">
				<p class="font-bold text-sm opacity-80">KT23874639 Details</p>
				<div class="flex-1">
					<Table2 :lastCol="true" v-model="tableItems" :headers="{
						head: [
							'Drug Detail',
							'Unit',
							'Approved',
							'Procured Amount',
							'Supplier'
						],
						row: [
							'drugDetail',
							'unit',
							'approved',
							'procuredAmount',
							'supplier'
						],
					}" :rows="(tableData || [])" :rowCom="ConfirmProcurementTableRow" :cells="{

					}">
					</Table2>
				</div>
				<div>
					<hr class="w-[921px] opacity-30 border tect-[#55291B]">
				</div>
				<div class="flex justify-end">
					<button @click="submitPurchaseRequest"
						class="bg-[#828282] text-white py-4 px-[34px] rounded flex gap-1 items-center">
						Confirm Procurement
					</button>
				</div>
			</div>
		</NewFormParent>
	</div>
</template>
