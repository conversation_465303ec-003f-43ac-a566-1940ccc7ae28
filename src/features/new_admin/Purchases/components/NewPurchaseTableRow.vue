<script setup>
import { ref, watch, computed } from 'vue'
import Button from '@components/Button.vue'
import { genId } from '@/utils/utils';
import { useRouter } from 'vue-router'
import { Icon } from '@iconify/vue';
import icons from '@/utils/icons';
import { usePagination } from '@/composables/usePagination';
import Dropdown from '@/components/Dropdown.vue';
import SelectInput from '@/components/new_form_elements/SelectInput.vue';
import SelectAllInput from '@/components/new_form_elements/SelectAllInput.vue';
import Select from '@/components/new_form_elements/Select.vue';
import Input from '@/components/new_form_elements/Input.vue';
import TableQuantityInput from '@/features/stock/components/TableQuantityInput.vue';
import InputFile from '@/components/new_form_elements/InputFile.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import { getAllBranches } from '../../Branches/Api/BranchesApi';

const props = defineProps({
	headKeys: {
		type: Array,
		required: true
	},
	rowData: {
		type: Array,
		required: true
	},
	rowKeys: {
		type: Array,
		required: true
	},
	cells: Object,
	selectedRows: {
		type: Array,
		default: []
	},
	modelValue: { // For v-model support
		type: Object,
		default: () => ({})
	},
})

console.log(props.modelValue);

console.log(props.rowData);
console.log("hhjhj");

const pagination = usePagination({
	cb: (data) => ({ ...data, limit: 25 }),
});

const emit = defineEmits(['row', 'update:SelectedRows'])
const router = useRouter()
const rows = ref((props.rowData || []).map(el => ({ ...el, id: genId.next().value })))

const branchreq = useApiRequest()
const branchResponse = ref([])
branchreq.send(() => getAllBranches({ page: 1, limit: 500 }), (res) => {
	if (res.success) {
		branchResponse.value = res.data
		console.log(branchResponse.value);
	}
})

const branchOptions = computed(() => {
	return branchResponse.value.map(branch => ({
		label: branch.branchName,
		value: branch.branchUuid,
	}))
})

watch(props, () => {
	rows.value = (props.rowData || []).map(el => ({ ...el, id: genId.next().value }))
})

</script>

<template>
	<tr @click="emit('row', row)" v-for="(row, idx) in rowData" :key="idx"
		class="bg-[#F1F1F1] border-b-[0.2px] hover:bg-gray-200">
		<td class="p-3">{{ idx + 1 }}</td>

		<td class="text-left" v-for="key in rowKeys" :key="key">
			<!-- Policy Status -->
			<!-- <template v-if="key === 'unit'">
				<Select name="unit" v-model="modelValue.measure" :Arguments="{ placeholder: '' }"
					:options="['Pack', 'Box', 'Strip', 'Piece', 'Bottle', 'Tube']" />
			</template> -->
			<template v-if="key === 'quantity'">
				<Input class="w-[8rem]" name="quantity" v-model="modelValue.quantity" :Arguments="{ placeholder: '' }" />
			</template>
			<template v-else-if="key === 'branch'">
				<Select :obj="true" name="branch" v-model="modelValue.branch" :Arguments="{ placeholder: '' }"
					:options="branchOptions" />
			</template>
			<span v-else>
				{{ row[key] }}
			</span>
		</td>

		<!-- Actions -->
		<!-- <td class="p-3 flex gap-3" v-if="headKeys.includes('Actions')">
			<Button class="text-sm text-white py-[6px] px-3 rounded-sm" type="secondary">Mark as Received</Button>
		</td> -->
		<!-- <td v-if="$slots.lastCol" class="p-3">
			<slot name="lastCol" :row="row"></slot>
		</td> -->
		<!-- <td>
			<SelectInput v-bind="selectedRows" :item="row" />
		</td> -->
	</tr>
</template>
