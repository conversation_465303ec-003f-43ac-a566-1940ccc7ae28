<script setup>
import { ref, watch, computed } from 'vue'
import Button from '@components/Button.vue'
import { genId } from '@/utils/utils';
import { useRouter } from 'vue-router'
import D2DInteractionPage from '@/features/paper_prescription/components/D2DInteractionPage.vue';

const props = defineProps({
	headKeys: {
		type: Array,
		required: true
	},
	rowData: {
		type: Array,
		required: true
	},
	rowKeys: {
		type: Array,
		required: true
	},
	cells: Object
})
const emit = defineEmits(['row'])

const router = useRouter()

const rows = ref((props.rowData || []).map(el => ({ ...el, id: genId.next().value })))

const openedRows = ref('')
const opened = computed(() => {
	return (id) => {
		console.log('id', id, !!openedRows.value.find(el => el == id))
		return openedRows.value.includes(id)
	}
})

function toggleDropdown(id) {
	console.log(id)
	if (openedRows.value == id) {
		openedRows.value = ''
	} else {
		openedRows.value = id
	}
}

watch(openedRows, () => console.log(props.rowData), { immediate: true })
watch(props, () => {
	rows.value = (props.rowData || []).map(el => ({ ...el, id: genId.next().value }))
})

function setPatien(data) {
	paperPrescriptionStore.setData(data);
	router.push({
		name: 'Dispense',
		params: {
			Uuid: data?.prescriptionUuid,
		},
	})
}
</script>
<template>
	<tbody>
		<template :key="row.id" v-for="(row, index) in rows">
			<tr @click="emit('row', row)"
				class="cursor-pointer hover:bg-gray-300 bg-gray-200 border-x-[0.2px] border-b-[0.2px] border-t-[0.2px]">
				<td class="p-2">{{ index + 1 }}</td>
				<td class="p-2 max-w-40" :key="key" v-for="key in rowKeys">
					<span v-if="!Object.keys(cells || {}) || !cells?.[key]">
						{{
							key.split(".").reduce((all, el) => {
								return all?.[el];
							}, row)
						}}
					</span>
					<component v-else-if="Object.keys(cells || {}) && cells[key].__hmrId" :row="row" :is="cells[key]" />
					<span v-else-if="typeof cells[key] == 'function'">
						{{ cells[key](row?.[key], row) }}
					</span>
				</td>
				<td class="p-3 flex items-center gap-3">
				</td>
			</tr>
			<tr v-if="openedRows == row.id">
				<td :colspan="headKeys.length + 1">
				</td>
			</tr>
		</template>
	</tbody>
</template>