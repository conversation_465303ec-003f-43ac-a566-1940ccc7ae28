<script setup>
const props = defineProps({
    row: Object
})
console.log(props?.row);

</script>

<template>
    <progress class="progress" :value="props?.row?.totalAmount" :max="props?.row?.minima" />
</template>

<style scoped>
.progress {
    width: 191.3px;
    height: 3.84px;
    border-radius: 5px;
    appearance: none;
    /* Background color of the progress bar */
}

/* Style the progress bar track (background) */
.progress::-webkit-progress-bar {
    background-color: #FFD5A4;
    /* Background color */
    border-radius: 5px;
    /* Rounded corners */
}

/* Style the progress bar value (filled part) */
.progress::-webkit-progress-value {
    background-color: #FF8F0D;
    /* Custom progress color */
    border-radius: 5px;
    /* Rounded corners */
}
</style>