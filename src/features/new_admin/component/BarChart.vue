<script setup>
import { ref, computed, onMounted } from "vue";
import { Chart as ChartJS, BarElement, BarController, CategoryScale, LinearScale, Tooltip, Legend } from "chart.js";
import { Bar } from "vue-chartjs"; // Correct package: vue-chartjs, NOT vue-chart-3
import { useApiRequest } from "@/composables/useApiRequest";
import { getWeeklyCompoundingPrescription, getWeeklyPrescription } from "../dashboard/Api/DashboardApi";

// ✅ Register required Chart.js components
ChartJS.register(BarElement, BarController, CategoryScale, LinearScale, Tooltip, Legend);

// Dummy data for when API calls fail or return empty data
const dummyWeeklyData = {
    labels: ["<PERSON>", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
    Prescription: [12500, 17800, 14300, 19500, 16200, 21500, 18700],
};

const dummyCompoundingData = {
    labels: ["Mon", "<PERSON><PERSON>", "Wed", "Thu", "Fri", "Sat", "<PERSON>"],
    compounding: [8500, 10200, 9300, 11500, 9800, 12500, 10700],
};

const weeklySales = ref({
    labels: dummyWeeklyData.labels,
    Prescription: dummyWeeklyData.Prescription,
});
const comweeklySales = ref({
    labels: dummyCompoundingData.labels,
    compounding: dummyCompoundingData.compounding,
});

const req = useApiRequest();
const comreq = useApiRequest();


const fetchWeeklySales = () => {
    req.send(() => getWeeklyPrescription(), (res) => {
        if (res.success && Object.keys(res.data).length > 0) {
            console.log("Response:", res.data);
            const labels = Object.keys(res.data);
            console.log(labels);
            const Prescription = Object.values(res.data);
            console.log(Prescription);
            weeklySales.value = { labels, Prescription };
        } else {
            console.log("Using dummy prescription data");
            // Keep using dummy data if API fails or returns empty data
        }
    })
};

const fetchCompoundingWeeklySales = () => {
    comreq.send(() => getWeeklyCompoundingPrescription(), (res) => {
        if (res.success && Object.keys(res.data).length > 0) {
            console.log("Response:", res.data);
            const labels = Object.keys(res.data);
            console.log(labels);
            const compounding = Object.values(res.data);
            console.log(compounding);
            comweeklySales.value = { labels, compounding };
        } else {
            console.log("Using dummy compounding data");
            // Keep using dummy data if API fails or returns empty data
        }
    })
};

onMounted(() => {
    fetchWeeklySales();
    fetchCompoundingWeeklySales();
});

const chartData = computed(() => ({
    labels: weeklySales.value.labels,
    datasets: [
        {
            label: `Compounding Prescription`,
            data: comweeklySales.value.compounding,
            backgroundColor: "#FFCF00",
            borderRadius: 1.92,
            barPercentage: 0.4, // Reduce bar width
            categoryPercentage: 0.5, //Reduce spacing between bars
        },
        {
            label: `Paper Prescription`,
            data: weeklySales.value.Prescription,
            backgroundColor: "#55291B80",
            borderRadius: 1.92,
            barPercentage: 0.4, //Reduce bar width
            categoryPercentage: 0.5, // Reduce spacing between bars
        },
    ],
}));

const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
        x: {
            ticks: {
                color: "#333", // Label color
                font: {
                    weight: "bold",
                },
            },
        },
        y: {
            ticks: {
                callback: (value) => `${value / 1000}k`, // Display as "5k", "10k"
            },
        },
    },
    plugins: {
        legend: {
            position: "bottom",
            labels: {
                usePointStyle: true, // Make legend circles
                pointStyle: "circle",
            },
        },
    },
};
</script>

<template>
    <div class="bg-white p-4">
        <div class="h-[300px]">
            <Bar :data="chartData" :options="chartOptions" />
        </div>
    </div>
</template>
