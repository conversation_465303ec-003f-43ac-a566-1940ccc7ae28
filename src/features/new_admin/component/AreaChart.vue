<script setup>
import { ref, computed, onMounted } from "vue";
import { Line } from "vue-chartjs";
import {
    Chart as ChartJS,
    LineElement,
    PointElement,
    CategoryScale,
    LinearScale,
    Title,
    Tooltip,
    Legend,
} from "chart.js";

// Register Chart.js components
ChartJS.register(LineElement, PointElement, CategoryScale, LinearScale, Title, Tooltip, Legend);

// Function to create gradient fill
const createGradient = (chart, color1, color2) => {
    const gradient = chart.createLinearGradient(0, 0, 0, 300);
    gradient.addColorStop(0, color1 + "FF"); // Top (Opaque)
    gradient.addColorStop(1, color2 + "00"); // Bottom (Transparent)
    return gradient;
};

// Dummy data for stock ins and outs
const stockData = {
    labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"],
    stockIns: [348950, 340000, 240000, 330000, 225000, 320000, 215000, 310000, 205000, 300000, 195000, 290000],
    stockOuts: [130000, 130000, 190000, 114000, 195000, 111500, 155000, 111600, 111000, 121000, 121700, 220000]
};

// Define chart data
const chartData = ref(null);

// Initialize with dummy data
const initializeChart = () => {
    // Try to get canvas context, but handle case where it might not be available yet
    let chart;
    try {
        const canvas = document.querySelector('canvas');
        if (canvas) {
            chart = canvas.getContext('2d');
        }
    } catch (error) {
        console.error("Canvas not available yet:", error);
    }

    chartData.value = {
        labels: stockData.labels,
        datasets: [
            {
                label: "Stock Outs",
                data: stockData.stockOuts,
                borderColor: "#FF3E0080",
                backgroundColor: chart ? createGradient(chart, "#052226", "#052226") : "rgba(5, 34, 38, 0.2)",
                tension: 0.4,
                pointRadius: 2,
                pointBackgroundColor: "#052226",
                pointBorderColor: "#052226",
                borderWidth: 2,
                fill: true,
            },
            {
                label: "Stock Ins",
                data: stockData.stockIns,
                borderColor: "#FF3E0080",
                backgroundColor: chart ? createGradient(chart, "#2E7987", "#2E7987") : "rgba(46, 121, 135, 0.2)",
                tension: 0.4,
                pointRadius: 2,
                pointBackgroundColor: "#2E7987",
                pointBorderColor: "#2E7987",
                borderWidth: 1.92,
                fill: true,
            },
        ],
    };
};

onMounted(() => {
    initializeChart();
});

// Define chart options
const chartOptions = {
    type: 'line',
    responsive: true,
    maintainAspectRatio: false,
    scales: {
        x: {

            grid: { display: false },
            ticks: { color: "#333", display: false, font: { size: 14 } },
        },
        y: {
            display: false,
            min: 0,
            max: 400000,
            ticks: { stepSize: 100000, color: "#333", font: { size: 10 } },
            grid: { color: "#ddd" },
        },
    },
    plugins: {
        legend: {
            display: false,
            labels: {
                display: false,

            },
        },
    },
};
</script>

<template>
    <div class="bg-white p-4">
        <div class="h-[300px]">
            <Line v-if="chartData" :data="chartData" :options="chartOptions" />
        </div>
        <div class="flex items-center gap-[34px] mb-4">
            <div>
                <p class="text-[#FF7A50] text-[15.37px] font-normal">Stock-ins</p>
                <p class="text-base font-medium text-[#FF7A50]">348,957 Items</p>
                <p class="text-xs text-gray-500">Last updated: {{ new Date().toLocaleDateString() }}</p>
            </div>

            <!-- Vertical Line using Border -->
            <div class="border-l border-gray-300 h-12"></div>

            <div>
                <p class="text-[#96A5B8] text-[15.37px] font-normal">Stock-outs</p>
                <p class="text-base font-medium text-[#052226]">124,568 Items</p>
                <p class="text-xs text-gray-500">Last updated: {{ new Date().toLocaleDateString() }}</p>
            </div>
        </div>

    </div>
</template>