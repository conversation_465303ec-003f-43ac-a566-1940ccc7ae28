import { ref } from "vue";
import { defineStore } from "pinia";

export const useDrugs = defineStore("allDrugStore", () => {
  const drugs = ref([]);

  function set(data) {
    console.log(data);

    drugs.value = data;
  }

  function getAll() {
    return drugs.value;
  }
  function add(data) {
    return drugs.value.push(add);
  }

  function update(id, data) {
    console.log(id, data);

    const idx = drugs.value.findIndex((el) => el.drugUuid == id);
    if (idx == -1) return;

    drugs.value.splice(idx, 1, data);
  }
  //   function remove(id) {
  //     const idx = drugs.value.findIndex((el) => el.userUuid == id);
  //     if (idx == -1) return;
  //     drugs.value.splice(idx, 1);
  //   }

  return {
    drugs,
    getAll,
    update,
    //remove,
    set,
    add,
  };
});
