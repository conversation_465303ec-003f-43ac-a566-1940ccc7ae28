<script setup lang="ts">
import BaseIcon from '@/components/base/BaseIcon.vue';
import NewFormModal from '@/components/NewFormModal.vue';
import { closeModal } from '@customizer/modal-x';
import { mdiClose } from '@mdi/js';
import DrugForm from '../form/DrugForm.vue';
import NewFormParent from '../../role/components/NewFormParent.vue';
</script>

<template>
    <div class="bg-black/50 min-h-full w-full p-10 grid place-items-center">
        <NewFormParent size="lg" title="Add Drug">
            <div class="p-8 gap-10 bg-white rounded-lg flex flex-col">
                <DrugForm />
            </div>
        </NewFormParent>
    </div>
</template>