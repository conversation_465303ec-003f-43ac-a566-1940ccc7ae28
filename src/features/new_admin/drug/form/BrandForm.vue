<script setup>
import FormSubmitButton from '@/components/FormSubmitButton.vue';
import Input from '@/components/new_form_elements/Input.vue';
import Form from '@/new_form_builder/Form.vue';
import { useRouter } from 'vue-router';
import { useDrugs } from '../store/drugStore';
import { craeteBrand } from '../Api/drugApi';
import { useForm } from '@/new_form_builder/useForm';
import { useApiRequest } from '@/composables/useApiRequest';
import { toasted } from '@/utils/utils';
import { closeModal } from '@customizer/modal-x';
import Button from '@/components/Button.vue';

const props = defineProps({
    drug: {
        type: Object
    }
})
console.log(props.drug);

const { submit } = useForm('BrandForm')
const drugs = useApiRequest()
const router = useRouter()
const drug = useDrugs()

function brandsAdd({ values }) {
    drugs.send(() => craeteBrand(props.drug?.drugUuid, values),
        (res) => {
            if (res.success) {
                drug.add(res.data);
            }
            toasted(res.success, 'Brand Created Succesfully', res.error);
            closeModal();
        })

}

</script>

<template>

    <Form class="grid grid-cols-2 w-full gap-8" id="BrandForm" v-slot="{ submit }">
        <Input name="drugBrandCode" validation="required" label="Brand Code"
            :attributes="{ placeholder: 'Enter Brand Code', type: 'text' }" />
        <Input name="drugBrandName" validation="required" label="Brand Name"
            :attributes="{ placeholder: 'Enter Brand Name', type: 'text' }" />

        <div class="w-full flex pt-10 col-span-3 justify-end">
            <div class="flex w-[8rem] justify-end">
                <FormSubmitButton @click.prevent="submit(brandsAdd)" class="col-span-2 font-bold rounded bg-secondary"
                    btn-text="Add Brand" />
            </div>
        </div>

    </Form>
</template>