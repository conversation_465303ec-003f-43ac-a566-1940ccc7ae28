<script setup>
import FormSubmitButton from '@/components/FormSubmitButton.vue';
import Input from '@/components/new_form_elements/Input.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import Form from '@/new_form_builder/Form.vue';
import { useRouter } from 'vue-router';
import { craeteDrug } from '../Api/drugApi';
import { useDrugs } from '../store/drugStore';
import { toasted } from '@/utils/utils';
import { useForm } from '@/new_form_builder/useForm';
import Textarea from '@/components/new_form_elements/Textarea.vue';
import { closeModal } from '@customizer/modal-x';

const { submit } = useForm('addform')
const drugs = useApiRequest()
const router = useRouter()
const drug = useDrugs()

function drugsAdd({ values }) {
    drugs.send(() => craeteDrug(values),
        (res) => {
            if (res.success) {
                drug.add(res.data);
            }
            toasted(res.success, 'Drug Created', res.error);
            closeModal();
        })

}
</script>

<template>
    <Form class="grid grid-cols-3 w-full gap-8" id="DrugForm" v-slot="{ submit }">
        <Input name="drugName" validation="required" label="Drug Name"
            :attributes="{ placeholder: 'Enter Drug Name', type: 'text' }" />
        <Input name="drugCode" validation="required" label="Drug Code"
            :attributes="{ placeholder: 'Enter Drug Code', type: 'text' }" />
        <Input name="category" validation="required" label="Category"
            :attributes="{ placeholder: 'Enter Category', type: 'text' }" />
        <Input name="subCategory" label="Sub-Category"
            :attributes="{ placeholder: 'Enter Sub-Category', type: 'text' }" />
        <Input name="dosageForm" validation="required" label="Dosage Form"
            :attributes="{ placeholder: 'Enter Dosage Form', type: 'text' }" />
        <Textarea name="drugDescription" label="Description" :attributes="{ placeholder: 'Enter Description  ' }" />

        <Textarea name="indications" label="Indications" :attributes="{ placeholder: 'Enter Indications' }" />
        <Textarea name="cautions" label="Cautions" :attributes="{ placeholder: 'Enter Cautions' }" />
        <Textarea name="drugInteractions" label="Drug Interactions"
            :attributes="{ placeholder: 'Enter Drug Interactions' }" />
        <Textarea name="contraIndications" label="Contra-Indications"
            :attributes="{ placeholder: 'Enter Contra-Indications' }" />
        <Textarea name="sideEffects" label="Side Effects" :attributes="{ placeholder: 'Enter Side Effects' }" />
        <Textarea name="doseAndAdministration" label="Dose and Administration"
            :attributes="{ placeholder: 'Enter Dose and Administration' }" />
        <Textarea name="storage" label="Storage" :attributes="{ placeholder: 'Enter Storage Details' }" />

        <!-- DrugBrandsDto Fields -->
        <Input name="drugBrandCode" validation="required" label="Brand Code"
            :attributes="{ placeholder: 'Enter Brand Code', type: 'text' }" />
        <Input name="drugBrandName" validation="required" label="Brand Name"
            :attributes="{ placeholder: 'Enter Brand Name', type: 'text' }" />

        <div class="w-full flex pt-10 col-span-3 justify-end">
            <div class="flex w-[8rem] justify-end">
                <FormSubmitButton @click.prevent="submit(drugsAdd)" class="col-span-2 font-bold rounded bg-secondary"
                    btn-text="Add" />
            </div>
        </div>
    </Form>
</template>