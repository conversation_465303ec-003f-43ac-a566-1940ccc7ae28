import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const api = new ApiService();
const path = "/prescriptions";
const path2 = "/compounds";

export function getDailyPrescription() {
  // const qr = getQueryFormObject(query);
  return api
    .addAuthenticationHeader()
    .get(`${path}/PrescriptionSalesDashboard`);
}

export function getWeeklyPrescription() {
  return api
    .addAuthenticationHeader()
    .get(`${path}/getWeeklyPrescriptionSalesReport`);
}

export function getDailyCompoundingPrescription() {
  return api
    .addAuthenticationHeader()
    .get(`${path2}/compoundingDailySalesReport`);
}

export function getWeeklyCompoundingPrescription() {
  return api
    .addAuthenticationHeader()
    .get(`${path2}/getWeeklyCompoundingPrescriptionSalesReport`);
}
export function getMonthlyPrescription() {
  return api.addAuthenticationHeader().get(`${path}/getEachMonthSoldItems`);
}
export function getMonthlyCompoundingPrescription() {
  return api
    .addAuthenticationHeader()
    .get(`${path2}/getEachMonthSoldCompounds`);
}
