<script setup>
import Input from "@/components/new_form_elements/Input.vue";
import MultipleSelect from "@/components/new_form_elements/MultipleSelect.vue";
import Select from "@/components/new_form_elements/Select.vue";
import Textarea from "@/components/new_form_elements/Textarea.vue";
import Form from "@/new_form_builder/Form.vue";
import { ref, watch } from "vue";

const props = defineProps({
  branch: {
    type: Object,
    default: () => ({}),
  },
  modelValue: {
    type: String,
    default: "ACTIVE",
  },
});

const branchStatus = ref(props.branch?.status || "ACTIVE");

const emit = defineEmits(["update:modelValue"]);

function toggleStatus(status) {
  branchStatus.value = status;
  emit("update:modelValue", status);
}

console.log(props.branch?.subCity);

const selectedSubCity = ref(props.branch?.subCity || "");
const selectedWoreda = ref(props.branch?.woreda || "");
const availableWoredas = ref([]);

const subcityWoredas = {
  "Addis Ketema": [
    "Arada",
    "Addisu Gebeya",
    "Bantyiketu",
    "Hayahulet",
    "<PERSON><PERSON>e",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>to",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON> <PERSON>da",
  ],
  "A<PERSON><PERSON> <PERSON>ti": [
    "A<PERSON><PERSON>",
    "<PERSON><PERSON>le",
    "<PERSON>ti",
    "<PERSON>ra",
    "<PERSON><PERSON>e",
    "<PERSON><PERSON> <PERSON>ra",
    "<PERSON><PERSON>",
    "<PERSON>f<PERSON>",
  ],
  <PERSON>da: [
    "Ab<PERSON>",
    "Addisu <PERSON><PERSON>eya",
    "<PERSON>t <PERSON><PERSON>",
    "<PERSON>dase",
    "Legehar",
    "Piazza",
    "Shiro Meda",
  ],
  Bole: [
    "Bole Arabsa",
    "Bole Bulbula",
    "Bole Mikael",
    "Bole Rwanda",
    "Dembel",
    "Gelan",
    "Kotebe",
    "Old Airport",
  ],
  Gulele: ["Abado", "Goro", "Kechene", "Kolfe", "Qirqos", "Sarbet", "Tera"],
  Kirkos: [
    "Autobis Tera",
    "Basha Wolde",
    "Cherkos",
    "Erer",
    "Lagahar",
    "Sidist Kilo",
    "Wube Berha",
  ],
  "Kolfe Keranyo": ["Gergi", "Keranio", "Kolfe", "Tabor"],
  Lideta: ["Abune Petros", "Addisu Gebeya", "Amist Kilo", "Hidase", "Legehar"],
  "Nifas Silk": [
    "Ayat",
    "Bole Bello",
    "Gotera",
    "Kazanchis",
    "Lancha",
    "Nifas Silk",
    "Wello Sefer",
  ],
  Yeka: [
    "Bole Bulbula",
    "Gerji",
    "Kebena",
    "Kotebe",
    "Mekanisa",
    "Saris",
    "Yeka Abado",
  ],
};

const subcities = Object.keys(subcityWoredas);

watch(selectedSubCity, () => {
  availableWoredas.value = subcityWoredas[selectedSubCity.value];
}, {immediate: true});

watch(
  () => props.branch?.status,
  (newStatus) => {
    if (newStatus) {
      branchStatus.value = newStatus;
    }
  },
  { immediate: true }
);
</script>

<template>
  <Form
    :inner="false"
    id="addbranchform"
    class="flex flex-col gap-6 rounded-lg p-6 bg-white"
  >
    <p class="font-bold text-base font-ubuntu">Branch Information</p>
    <hr />
    <div class="grid grid-cols-4 gap-12">
      <div class="flex flex-col gap-1">
        <div class="flex gap-1">
          <label class="text-sm">Branch Number</label>
          <span class="text-red-500">*</span>
        </div>
        <Input
          name="code"
          :value="branch?.code"
          validation="required"
          :attributes="{
            placeholder: 'Enter Branch Code',
          }"
        />
      </div>

      <div class="flex flex-col gap-1 col-span-3">
        <div class="flex gap-1">
          <label class="text-sm">Branch Name</label>
          <span class="text-red-500">*</span>
        </div>
        <Input
          class="col-span-3"
          name="branchName"
          :value="branch?.branchName"
          validation="required"
          :attributes="{
            placeholder: 'Enter Branch Name',
          }"
        />
      </div>

      <div class="flex flex-col gap-1 col-span-4">
        <div class="flex gap-1">
          <label class="text-sm">Description</label>
          <span class="text-red-500">*</span>
        </div>
        <Textarea
          name="description"
          :value="branch?.description"
          validation="required"
          :attributes="{
            placeholder: 'Write branch description(if necessary)',
          }"
        />
      </div>
    </div>
    <p class="font-bold text-base font-ubuntu">Business Address</p>
    <hr />
    <div class="grid grid-cols-4 gap-6">
      <div class="flex flex-col gap-1 col-span-2">
        <div class="flex gap-1">
          <label class="text-sm">Subcity</label>
          <span class="text-red-500">*</span>
        </div>
        <Select
          name="subCity"
          validation="required"
          v-model="selectedSubCity"
          :options="subcities"
          :attributes="{
            placeholder: 'Select Sub city',
          }"
        />
      </div>
      <div class="flex flex-col gap-1 col-span-2">
        <div class="flex gap-1">
          <label class="text-sm">Woreda</label>
          <span class="text-red-500">*</span>
        </div>
        <Select
          name="woreda"
          v-model="selectedWoreda"
          validation="required"
          :options="availableWoredas"
          :attributes="{
            placeholder: selectedSubCity
              ? 'Select Woreda'
              : 'Please select a subcity first',
          }"
        />
      </div>
      <div class="flex flex-col gap-1 col-span-2">
        <div class="flex gap-1">
          <label class="text-sm">Latitude</label>
          <span class="text-red-500">*</span>
        </div>
        <Input
          class=""
          name="latitude"
          :value="branch?.latitude"
          validation="required"
          :attributes="{
            placeholder: 'Enter Latitude',
          }"
        />
      </div>
      <div class="flex flex-col gap-1 col-span-2">
        <div class="flex gap-1">
          <label class="text-sm">Longitude</label>
          <span class="text-red-500">*</span>
        </div>
        <Input
          class=""
          name="longitude"
          :value="branch?.longitude"
          validation="required"
          :attributes="{
            placeholder: 'Enter Longitude',
          }"
        />
      </div>
      <div class="flex flex-col gap-1 col-span-2">
        <div class="flex gap-1">
          <label class="text-sm">Branch Phone Number</label>
          <span class="text-red-500">*</span>
        </div>
        <Input
          class=""
          name="contactPhoneNumber"
          :value="branch?.contactPhoneNumber"
          validation="required"
          :attributes="{
            placeholder: 'Enter Branch PhoneNumber',
          }"
        />
      </div>
      <div class="flex flex-col gap-1 col-span-2">
        <div class="flex gap-1">
          <label class="text-sm">Branch Email</label>
          <span class="text-red-500">*</span>
        </div>
        <Input
          class=""
          name="branchEmail"
          :value="branch?.branchEmail"
          validation="required"
          :attributes="{
            placeholder: 'Enter Branch Email',
          }"
        />
      </div>
    </div>
    <p class="font-bold text-base font-ubuntu">Branch Configuration</p>
    <hr />
    <div class="grid grid-cols-4 gap-4">
      <div class="flex gap-6 col-span-2 rounded-lg p-4 bg-[#FFDED7]">
        <input
          name="status"
          @click="toggleStatus('ACTIVE')"
          class="custom-checkbox col-span-2"
          type="checkbox"
          :checked="branchStatus === 'ACTIVE'"
        />
        <label class="text-sm opacity-80 font-normal text-[#ED6033]"
          >Active</label
        >
      </div>
      <div class="flex gap-6 col-span-2 rounded-lg p-4 bg-[#EEEEEE]">
        <input
          name="status"
          @click="toggleStatus('INACTIVE')"
          class="custom-checkbox1 col-span-2"
          type="checkbox"
          :checked="branchStatus === 'INACTIVE'"
        />
        <label class="text-sm opacity-80 font-normal text-[#1D1C1B]"
          >Inactive</label
        >
      </div>
    </div>
  </Form>
</template>

<style scoped>
.custom-checkbox {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid #ed6033;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  background-color: white;
}

.custom-checkbox:checked::after {
  content: "";
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 11px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.custom-checkbox:checked {
  background-color: #ed6033;
}

.custom-checkbox1 {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid #1d1c1b;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  background-color: white;
}

.custom-checkbox1:checked::after {
  content: "";
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 11px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.custom-checkbox1:checked {
  background-color: #1d1c1b;
}
</style>
