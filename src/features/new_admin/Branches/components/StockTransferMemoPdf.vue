<script setup>
import StockTransferPdf from '@/components/StockTransfer.pdf.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import { getPrescriptionByUuid } from '@/features/paper_prescription/api/paperPrescriptionApi';
import { usePaperPrescriptionStore } from '@/features/paper_prescription/store/paperPrescriptionStore';
import icons from '@/utils/icons';
import { getBgbase64Url } from '@/utils/utils';
import { ref } from 'vue';
import { useRoute } from 'vue-router';

const paperPrescriptionStore = usePaperPrescriptionStore();
const route = useRoute();
const Uuid = route.params.uuid
const found = paperPrescriptionStore.paper_prescription.prescriptionUuid == Uuid;

const photo = ref('');
(async function getPhoto() {
	const alert = await getBgbase64Url('/images.png')
	photo.value = alert
})()


</script>

<template>
	<StockTransferPdf :alert-photo="photo" />
	<!-- <div class="grid place-items-center h-full">
		<i v-html="icons.spinner" class="animate-spin" />
	</div> -->
</template>