<script setup>
import { ref, watch, computed } from "vue";
import Button from "@components/Button.vue";
import { genId, toasted } from "@/utils/utils";
import { useRouter } from "vue-router";
import { Icon } from "@iconify/vue";
import icons from "@/utils/icons";
import { usePagination } from "@/composables/usePagination";
import Dropdown from "@/components/Dropdown.vue";
import SelectInput from "@/components/new_form_elements/SelectInput.vue";
import SelectAllInput from "@/components/new_form_elements/SelectAllInput.vue";
import { openModal } from "@customizer/modal-x";
import { useApiRequest } from "@/composables/useApiRequest";
import { deleteBranch, updateStatus } from "../Api/BranchesApi";
import { useBranches } from "../Store/BranchesStore";
import { BranchStatus } from "../../../../utils/utils";

const props = defineProps({
  headKeys: {
    type: Array,
    required: true,
  },
  rowData: {
    type: Array,
    required: true,
  },
  rowKeys: {
    type: Array,
    required: true,
  },
  cells: Object,
  selectedRows: {
    type: Array,
    default: [],
  },
});

const emit = defineEmits(["row", "update:SelectedRows"]);
const router = useRouter();
const req = useApiRequest();
const branchesStore = useBranches();
const rows = ref(
  (props.rowData || [])?.map?.((el) => ({ ...el, id: genId.next().value }))
);

watch(props, () => {
  rows.value = (props.rowData || [])?.map?.((el) => ({
    ...el,
    id: genId.next().value,
  }));
});

function handleViewMap(row) {
  if (row?.latitude && row?.longitude) {
    // Option 1: Open in Google Maps
    // const url = `https://www.google.com/maps?q=${row.latitude},${row.longitude}`;
    // window.open(url, '_blank');

    // Option 2: If you have a map component in your app
    openModal("MapView", {
      title: `${row.branchName} Location`,
      latitude: row.latitude,
      longitude: row.longitude,
    });
  } else {
    // Handle case when coordinates are not available
    alert("Map coordinates not available for this branch");
  }
}

function removeBranch(row) {
  openModal(
    "Confirmation",
    {
      title: "Delete Branch",
      message: `Are You Sure you want to delete '${row?.branchName}' Branch?`,
    },
    (res) => {
      if (res) {
        req.send(
          () => deleteBranch(row?.branchUuid),
          (res) => {
            if (res.success) {
              branchesStore.remove(row?.branchUuid);
            }
            toasted(res.success, "Branch Suspended Successfully", res.error);
          }
        );
      }
    }
  );
}

function activeBranch(row, status) {
  openModal(
    "Confirmation",
    {
      title: "Activate Branch",
      message: `Are You Sure you want to ${status == BranchStatus.ACTIVE ? "activate" : "suspend"} '${row?.branchName}' Branch?`,
    },
    (res) => {
      if (res) {
        req.send(
          () => updateStatus(row?.branchUuid, { status: status }),
          (res) => {
            if (res.success) {
              branchesStore.update(row?.branchUuid, { ...row, status: status });
            }
            toasted(res.success, "Branch Suspended Successfully", res.error);
          }
        );
      }
    }
  );
}
</script>

<template>
  <tr
    @click="emit('row', row)"
    v-for="(row, idx) in rowData"
    :key="idx"
    class="bg-white border-b-[0.2px] hover:bg-gray-200"
  >
    <td class="p-3">{{ idx + 1 }}</td>

    <td class="text-left" v-for="key in rowKeys" :key="key">
      <!-- Policy Status -->
      <template v-if="key === 'map'">
        <Button
          @click.stop="handleViewMap(row)"
          class="flex items-center gap-1 text-sm border-none hover:py-2 px-3 hover:bg-[#FFEDEF] underline underline-offset-2"
        >
          <Icon icon="mdi:map-marker" class="text-black" />
          View Map
        </Button>
      </template>
      <template v-else-if="key == 'address'">
        <span>{{ `${row?.subCity}, woreda ${row?.woreda}` }}</span>
      </template>
      <template v-else-if="key === 'contact'">
        <div class="flex items-center gap-1">
          <p>{{ row[key] }}</p>
          <i v-html="icons.contact" />
          <!-- <Icon icon="mdi:phone" class="text-black" /> -->
        </div>
      </template>
      <span v-else>
        {{ row[key] }}
      </span>
    </td>

    <!-- Actions -->
    <td class="p-3 flex gap-3" v-if="headKeys.includes('Actions')">
      <Dropdown top="170%" v-slot="{ setRef, toggleDropdown }">
        <button
          @click="toggleDropdown"
          class="underline underline-offset-2 decoration-white bg-[#55291B] py-1 px-3 rounded-sm"
        >
          <div class="text-white text-sm font-normal">More</div>
        </button>
        <div :ref="setRef">
          <div
            class="rounded border-primary border flex flex-col *:truncate *:text-left gap-2 p-2 bg-white shadow-lg min-w-40"
          >
            <button
              @click="$router.push('/edit_branch/' + row?.branchUuid)"
              class="flex gap-2 px-4 text-black h-10 items-center hover:bg-[#FFEDEF] border-transparent border hover:rounded"
            >
              <i v-html="icons.editbranch" />
              <p class="">Edit Brach</p>
            </button>
            <button
              @click="openModal('checkBoxConfirmition', row)"
              class="flex gap-2 px-4 text-black h-10 items-center hover:bg-[#FFEDEF] border-transparent border hover:rounded"
            >
              <i v-html="icons.changestatus" />
              Change Status
            </button>
            <!-- <Button
              :class="[
                row?.status === BranchStatus.INACTIVE
                  ? 'text-green-400'
                  : 'text-[#EE6363]',
              ]"
              :pending="req.pending.value"
              @click.prevent="activeBranch(row, row?.status === BranchStatus.INACTIVE ? BranchStatus.ACTIVE : BranchStatus.INACTIVE)"
              class="flex gap-2 px-4 h-10 items-center hover:bg-[#FFEDEF] border-transparent border hover:rounded"
            >
              <i
                v-html="
                  row?.status === BranchStatus.INACTIVE
                    ? icons.start
                    : icons.delete
                "
              />
              {{
                row?.status === BranchStatus.INACTIVE ? "Activate" : "Suspend"
              }}
            </Button> -->
          </div>
        </div>
      </Dropdown>
    </td>
    <!-- <td v-if="$slots.lastCol" class="p-3">
			<slot name="lastCol" :row="row"></slot>
		</td> -->
    <!-- <td>
			<SelectInput v-bind="selectedRows" :item="row" />
		</td> -->
  </tr>
</template>
