<script setup>
import { closeModal, getModal, openModal } from "@customizer/modal-x";
import Icon from "@/components/Icon.vue";
import Textarea from "@/components/new_form_elements/Textarea.vue";
import Button from "@/components/Button.vue";
import Form from "@/new_form_builder/Form.vue";
import { ref } from "vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { updateStatus } from "../Api/BranchesApi";
import { useBranches } from "../Store/BranchesStore";
import { BranchStatus, toasted } from "@/utils/utils";

const props = defineProps({
	data: Object,
});

const status = ref(props.data?.status || '');
console.log(status.value);

const branchesStore = useBranches();
const req = useApiRequest();

function activeBranch() {
  openModal(
    "Confirmation",
    {
      title: "Activate Branch",
      message: `Are You Sure you want to ${status == BranchStatus.ACTIVE ? "activate" : "suspend"} '${props.data?.branchName}' Branch?`,
    },
    (res) => {
      if (res) {
        req.send(
          () => updateStatus(props.data?.branchUuid, { status: status.value }),
          (res) => {
            if (res.success) {
              branchesStore.update(props.data?.branchUuid, { ...props.data, status: status.value });
            }
            toasted(res.success, "Branch Suspended Successfully", res.error);
          }
        );
      }
    }
  );
}

function cancel() {
	closeModal();
}

function handleStatusChange(newStatus) {
	// Only update status if it's different from current
	if (status.value !== newStatus) {
		status.value = newStatus;
	}
}

</script>
<template>
	<div class="h-full bg-dark/50 grid place-items-center">
		<div class="bg-base-clr-2 flex- flex-col gap-6 rounded-lg p-[2rem] px-6 w-[38rem]">
			<div class="h-12 border-b flex items-center justify-between">
				<p class="font-bold">{{ data?.title || "Change Status" }}</p>
				<button @click="closeModal()">
					<Icon name="solar:close-circle-outline" />
				</button>
			</div>
			<p class="p-2 text-dark/80 opacity-50 mt-3">Are you sure you want to change the status of " {{ data?.branchName }}
				" ?</p>
			<div class="grid grid-cols-2 gap-4 mt-3">
				<div class="flex gap-6 rounded-lg p-4 items-center bg-[#FFDED7]"
					:class="{ 'bg-[#FFDED7]': status !== 'ACTIVE', 'bg-[#FFDED7]': status === 'Active' }">
					<input class="custom-checkbox col-span-2" type="checkbox" value="" :checked="status === 'ACTIVE'"
						@change="handleStatusChange('ACTIVE')">
					<label class="text-sm opacity-80 font-normal text-[#ED6033]"
						:class="{ 'text-[#ED6033]': status !== 'ACTIVE', 'text-[#ED6033]': status === 'ACTIVE' }">Active</label>
				</div>
				<div class="flex gap-6 rounded-lg p-4 items-center bg-[#EEEEEE]"
					:class="{ 'bg-[#EEEEEE]': status !== 'INACTIVE', 'bg-gray-300': status === 'INACTIVE' }">
					<input class="custom-checkbox1 col-span-2" type="checkbox" value="" :checked="status === 'INACTIVE'"
						@change="handleStatusChange('INACTIVE')">
					<label class="text-sm opacity-80 font-normal text-[#1D1C1B]"
						:class="{ 'text-[#1D1C1B]': status !== 'INACTIVE', 'text-[#1D1C1B]': status === 'INACTIVE' }">Inactive</label>
				</div>
			</div>
			<div class="flex flex-col gap-4 mt-3">
				<p class="opacity-80 font-normal text-base">Reason</p>
				<Form id="remark" v-slot="{ submit }">
					<Textarea :attributes="{ placeholder: 'Type your reason here' }" name="remark" />
					<div class="flex justify-end py-2 items-center gap-4 mt-3">
						<Button @click.prevent="cancel" class="text-sm border-dark border">Cancel</Button>
						<Button :pending="req.pending.value" @click.prevent="submit(activeBranch)" class="text-sm text-white" type="primary">Confirm</Button>
					</div>
				</Form>
			</div>

		</div>
	</div>
</template>

<style scoped>
.custom-checkbox {
	appearance: none;
	width: 20px;
	height: 20px;
	border: 2px solid #ED6033;
	border-radius: 4px;
	cursor: pointer;
	position: relative;
	background-color: white;
}

.custom-checkbox:checked::after {
	content: '';
	position: absolute;
	left: 6px;
	top: 2px;
	width: 6px;
	height: 11px;
	border: solid white;
	border-width: 0 2px 2px 0;
	transform: rotate(45deg);
}

.custom-checkbox:checked {
	background-color: #ED6033;
}

.custom-checkbox1 {
	appearance: none;
	width: 20px;
	height: 20px;
	border: 2px solid #1D1C1B;
	border-radius: 4px;
	cursor: pointer;
	position: relative;
	background-color: white;
}

.custom-checkbox1:checked::after {
	content: '';
	position: absolute;
	left: 6px;
	top: 2px;
	width: 6px;
	height: 11px;
	border: solid white;
	border-width: 0 2px 2px 0;
	transform: rotate(45deg);
}

.custom-checkbox1:checked {
	background-color: #1D1C1B;
}
</style>
