import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const api = new ApiService();
const path = "/branches";

export function getAllBranches(query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path}/all${qr}`);
}


export function getBranchById(id) {
  return api.addAuthenticationHeader().get(`${path}/${id}`);
}

export function addBranch(data) {
  return api.addAuthenticationHeader().post(`${path}/addBranch`, data);
}

export function updateBranch(id, data) {
  return api.addAuthenticationHeader().put(`${path}/${id}`, data);
}

export function updateStatus(id, query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().patch(`${path}/status/${id}${qr}`);
}

export function deleteBranch(id) {
  return api.addAuthenticationHeader().delete(`${path}/${id}`);
}
