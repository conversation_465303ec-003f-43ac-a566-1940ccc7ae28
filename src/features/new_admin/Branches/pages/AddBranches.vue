<script setup>
import { openModal } from '@customizer/modal-x';
import Button from '@/components/Button.vue';
import { computed, ref, watch } from 'vue';
import { useApiRequest } from '@/composables/useApiRequest';
import { addBranch } from '../Api/BranchesApi';
import { useBranches } from '../Store/BranchesStore';
import { toasted } from '@/utils/utils';
import BranchForm from '../Form/BranchForm.vue';
import DefaultPage from '@/components/DefaultPage.vue';
import { useForm } from '@/new_form_builder/useForm';
import { Icon } from '@iconify/vue';

const isEditMode = computed(() => !!branchUuid);
const pageTitle = computed(() => isEditMode.value ? 'Edit Branch' : 'Add New Branch');
const buttonText = computed(() => isEditMode.value ? 'Update Branch' : 'Add Branch');

const branchesStore = useBranches()
const branchStatus = ref('ACTIVE');
const { submit } = useForm('addbranchform');
const req = useApiRequest();

function add({ values }) {
	const dataToSubmit = {
		...values,
		status: branchStatus.value
	};
	req.send(() => addBranch(dataToSubmit),
		(res) => {
			if (res.success) {
				branchesStore.add(res.data);
			}
			toasted(res.success, 'Branch Added Succesfully', res.error);
		})
}

</script>

<template>
	<DefaultPage>
		<BranchForm v-model="branchStatus" />
		<div class="flex justify-end">
			<Button @click.prevent="submit(add)"
				class="bg-primary text-white py-4 px-[34px] border-[0.38px] rounded-[4px] flex gap-1 items-center"
				type="primary">
				<div v-if="req.pending.value">
					<Icon icon="svg-spinners:3-dots-scale" class="text-2xl" />
				</div>
				<div v-if="!req.pending.value">Add Branch</div>
			</Button>
		</div>
	</DefaultPage>
</template>
