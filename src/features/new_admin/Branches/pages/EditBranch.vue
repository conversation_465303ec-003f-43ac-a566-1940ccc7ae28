<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import BranchForm from '../Form/BranchForm.vue';
import { useBranches } from '../Store/BranchesStore';
import { ref, watch } from 'vue';
import DefaultPage from '@/components/DefaultPage.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import { useForm } from '@/new_form_builder/useForm';
import { toasted } from '@/utils/utils';
import { getBranchById, updateBranch } from '../Api/BranchesApi';
import Button from '@/components/Button.vue';
import { Icon } from '@iconify/vue';
import icons from '@/utils/icons';

const { submit } = useForm('addbranchform');
const branchesStore = useBranches()
const route = useRoute()
const branchUuid = route.params.branchUuid
const branch = ref(branchesStore.branches.find((el) => el.branchUuid == branchUuid));
const branchStatus = ref(branch.value?.status || 'ACTIVE');

const req = useApiRequest();
const getBranchReq = useApiRequest();

if(!branch.value) {
	getBranchReq.send(() => getBranchById(branchUuid), (res) => {
		if (res.success) {
			branch.value = res.data;
		}
	})
}

function update({ values }) {
	const dataToSubmit = {
		...values,
		status: branchStatus.value
	};
	req.send(() => updateBranch(branchUuid, dataToSubmit),
		(res) => {
			if (res.success) {
				branchesStore.update(res.data);
			}
			toasted(res.success, 'Branch Updated Succesfully', res.error);
		})
}



</script>

<template>
	<DefaultPage size="xl">
		<BranchForm v-if="branch" v-model="branchStatus" :branch="branch" />
		<div v-else class="grid place-items-center h-full" >
			<i v-html="icons.spinner" />
		</div>
		<div v-if="!getBranchReq.pending.value " class="flex justify-end">
			<Button @click.prevent="submit(update)"
				class="bg-primary text-white py-4 px-[34px] border-[0.38px] rounded-[4px] flex gap-1 items-center"
				type="primary">
				<div v-if="req.pending.value">
					<Icon icon="svg-spinners:3-dots-scale" class="text-2xl" />
				</div>
				<div v-if="!req.pending.value">Update Branch</div>
			</Button>
		</div>
	</DefaultPage>
</template>