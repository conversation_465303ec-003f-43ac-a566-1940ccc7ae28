<script setup>
import BaseIcon from "@/components/base/BaseIcon.vue";
import Button from "@/components/Button.vue";
import Dropdown from "@/components/Dropdown.vue";
import Table from "@/components/Table.vue";
import { usePagination } from "@/composables/usePagination";
import icons from "@/utils/icons";
import { Icon } from "@iconify/vue";
import { mdiMagnify, mdiPlus } from "@mdi/js";
import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import BranchesTableRow from "../components/BranchesTableRow.vue";
import SelectInput from "@/components/new_form_elements/SelectInput.vue";
import SelectAllInput from "@/components/new_form_elements/SelectAllInput.vue";
import { getAllBranches } from "../Api/BranchesApi";
import { useBranches } from "../Store/BranchesStore";
import { usePaginationTemp } from "@/composables/usePaginaionTemp";
import DefaultPage from "@/components/DefaultPage.vue";

const branchesStore = useBranches();
const pagination = usePaginationTemp({
  store: branchesStore,
  cb: getAllBranches,
});
const search = ref("");
const router = useRouter();

const selectedBranchForMap = ref(null);
const showMapModal = ref(false);

const tempData = ref([
  {
    code: "k39",
    branchName: "Tablet",
    description: "strength",
    address: "Bole, Woreda 12",
    contact: "+251 945 678 987",
    status: "Active",
  },
  {
    code: "k39",
    branchName: "Tablet",
    description: "strength",
    address: "Bole, Woreda 12",
    contact: "+251 945 678 987",
    status: "Active",
  },
  {
    code: "k39",
    branchName: "Tablet",
    description: "strength",
    address: "Bole, Woreda 12",
    contact: "+251 945 678 987",
    status: "Active",
  },
  {
    code: "k39",
    branchName: "Tablet",
    description: "strength",
    address: "Bole, Woreda 12",
    contact: "+251 945 678 987",
    status: "Active",
  },
  {
    code: "k39",
    branchName: "Tablet",
    description: "strength",
    address: "Bole, Woreda 12",
    contact: "+251 945 678 987",
    status: "Active",
  },
  {
    code: "k39",
    branchName: "Tablet",
    description: "strength",
    address: "Bole, Woreda 12",
    contact: "+251 945 678 987",
    status: "Active",
  },
  {
    code: "k39",
    branchName: "Tablet",
    description: "strength",
    address: "Bole, Woreda 12",
    contact: "+251 945 678 987",
    status: "Active",
  },
]);

const selectedBranches = ref([]);
const allBranches = computed(() => tempData.value || []);
</script>

<template>
  <DefaultPage>
    <div class="flex justify-between items-center">
      <p class="font-bold text-lg text-[#1D1C1B]">Branches</p>
      <div class="flex gap-2">
        <div class="flex border rounded px-4 items-center bg-accent">
          <input
            v-model="pagination.search.value"
            class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80"
            placeholder="Search Branches"
          />
          <BaseIcon :path="mdiMagnify" :size="20" />
        </div>
        <Button
          @click.prevent="$router.push('/addbranch')"
          type="primary"
          class="flex gap-4 text-white rounded h-14 items-center"
        >
          <i v-html="icons.addbranch" />
          <p class="opacity-80">Add Branch</p>
        </Button>
      </div>
    </div>
    <div class="flex-1">
      <Table
        :pending="pagination.pending.value"
        :headers="{
          head: [
            'Branch Number',
            'Branch Name',
            'Description',
            'Address',
            'Contact',
            'Map',
            'Status',
            'Actions',
          ],
          row: [
            'code',
            'branchName',
            'description',
            'address',
            'contactPhoneNumber',
            'map',
            'status',
          ],
        }"
        :rows="branchesStore.branches || []"
        :cells="{
          address: (_, row) => {
            return `${row?.subCity}, woreda ${row?.woreda}`;
          },
        }"
        :rowCom="BranchesTableRow"
      >
        <!-- <template #headerLast>
					<SelectAllInput v-model="selectedBranches" :items="allBranches" />
				</template> -->
        <!-- <template #lastCol="{ row }">
					<SelectInput v-model="selectedBranches" :item="row" />
				</template> -->
        <template #actions="{ row }"> </template>
      </Table>
    </div>
  </DefaultPage>
</template>
