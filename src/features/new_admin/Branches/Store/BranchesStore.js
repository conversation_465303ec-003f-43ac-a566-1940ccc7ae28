import { ref } from "vue";
import { defineStore } from "pinia";

export const useBranches = defineStore("allBranchesStore", () => {
  const branches = ref([]);

  function set(data) {
    branches.value = data;
  }

  function getAll() {
    return branches.value;
  }
  function add(data) {
    return branches.value.push(data);
  }

  function update(id, data) {
    const idx = branches.value.findIndex((el) => el.branchUuid == id);
    if (idx == -1) return;

    branches.value.splice(idx, 1, data);
  }
  function remove(id) {
    const idx = branches.value.findIndex((el) => el.branchUuid == id);
    if (idx == -1) return;

    branches.value.splice(idx, 1);
  }

  function updateStatus(id, data) {
    const idx = branches.value.findIndex((el) => el.branchUuid == id);
    if (idx == -1) return;
    console.log(data);
    console.log(branches.value[idx]);
    branches.value[idx].status = data.status;
  }

  return {
    branches,
    getAll,
    update,
    remove,
    add,
    updateStatus,
    set,
  };
});
