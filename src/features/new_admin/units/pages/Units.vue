<script setup>
import DefaultPage from '@/components/DefaultPage.vue';
import Button from '@/components/Button.vue';
import Table from '@/components/Table.vue';
import TableNumberCell from '@/components/TableNumberCell.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import { usePagination } from '@/composables/usePagination';
import { getAllUnits, deleteUnitById, updateUnitStatus } from '../Api/UnitApi';
import { useUnits } from '../store/unitStore';
import { toasted } from '@/utils/utils';
import { openModal } from '@customizer/modal-x';
import { ref, computed, onMounted } from 'vue';
import BaseIcon from '@/components/base/BaseIcon.vue';
import { mdiPencil, mdiDelete, mdiPlus, mdiEye } from '@mdi/js';

// Store and pagination
const unitStore = useUnits();
const pagination = usePagination({
  cb: getAllUnits 
});

// Reactive data
const search = ref('');
const selectedType = ref('');
const selectedStatus = ref('');
const req = useApiRequest();

// Computed
const filteredUnits = computed(() => {
  let filtered = unitStore.units;

  // Search filter
  if (search.value) {
    const searchTerm = search.value.toLowerCase();
    filtered = filtered.filter(unit =>
      unit.unitName?.toLowerCase().includes(searchTerm) ||
      unit.unitSymbol?.toLowerCase().includes(searchTerm) ||
      unit.unitType?.toLowerCase().includes(searchTerm)
    );
  }

  // Type filter
  if (selectedType.value) {
    filtered = filtered.filter(unit => unit.unitType === selectedType.value);
  }

  // Status filter
  if (selectedStatus.value) {
    filtered = filtered.filter(unit => unit.status === selectedStatus.value);
  }

  return filtered;
});

const unitTypes = computed(() => {
  const types = [...new Set(unitStore.units.map(unit => unit.unitType))];
  return types.map(type => ({ label: type, value: type }));
});

// Methods
function loadUnits() {
  req.send(
    () => getAllUnits({
      page: pagination.currentPage,
      limit: pagination.itemsPerPage
    }),
    (res) => {
      if (res.success) {
        unitStore.set(res.data?.content || res.data || []);
        updatePagination(res.data);
      } else {
        toasted(false, 'Failed to load units', res.error);
      }
    }
  );
}

function openAddModal() {
  openModal('AddUnit', null, (result) => {
    if (result) {
      loadUnits(); // Refresh the list
    }
  });
}

function openEditModal(unit) {
  openModal('EditUnit', unit, (result) => {
    if (result) {
      loadUnits(); // Refresh the list
    }
  });
}

function openViewModal(unit) {
  openModal('ViewUnit', unit);
}

function confirmDelete(unit) {
  openModal(
    'Confirmation',
    {
      title: 'Delete Unit',
      message: `Are you sure you want to delete the unit "${unit.unitName}" (${unit.unitSymbol})? This action cannot be undone.`
    },
    (confirmed) => {
      if (confirmed) {
        deleteUnit(unit);
      }
    }
  );
}

function deleteUnit(unit) {
  const deleteReq = useApiRequest();
  deleteReq.send(
    () => deleteUnitById(unit.unitUuid),
    (res) => {
      if (res.success) {
        unitStore.remove(unit.unitUuid);
        toasted(true, 'Unit deleted successfully', '');
      } else {
        toasted(false, 'Failed to delete unit', res.error);
      }
    }
  );
}

function toggleStatus(unit) {
  const newStatus = unit.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
  const statusReq = useApiRequest();

  openModal(
    'Confirmation',
    {
      title: `${newStatus === 'ACTIVE' ? 'Activate' : 'Deactivate'} Unit`,
      message: `Are you sure you want to ${newStatus === 'ACTIVE' ? 'activate' : 'deactivate'} the unit "${unit.unitName}"?`
    },
    (confirmed) => {
      if (confirmed) {
        statusReq.send(
          () => updateUnitStatus(unit.unitUuid, newStatus),
          (res) => {
            if (res.success) {
              unitStore.update(unit.unitUuid, { ...unit, status: newStatus });
              toasted(true, `Unit ${newStatus === 'ACTIVE' ? 'activated' : 'deactivated'} successfully`, '');
            } else {
              toasted(false, `Failed to ${newStatus === 'ACTIVE' ? 'activate' : 'deactivate'} unit`, res.error);
            }
          }
        );
      }
    }
  );
}

function getStatusBadgeClass(status) {
  return status === 'ACTIVE'
    ? 'bg-green-100 text-green-800'
    : 'bg-red-100 text-red-800';
}

function getTypeBadgeClass(type) {
  const colors = {
    'WEIGHT': 'bg-blue-100 text-blue-800',
    'VOLUME': 'bg-purple-100 text-purple-800',
    'COUNT': 'bg-green-100 text-green-800',
    'DOSAGE': 'bg-orange-100 text-orange-800',
    'CONCENTRATION': 'bg-indigo-100 text-indigo-800'
  };
  return colors[type] || 'bg-gray-100 text-gray-800';
}

// Lifecycle
onMounted(() => {
  loadUnits();
});
</script>

<template>
  <DefaultPage>
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Unit Management</h1>
        <p class="text-gray-600">Manage measurement units for your inventory</p>
      </div>
      <Button @click="openAddModal" type="primary" class="flex items-center gap-2">
        <BaseIcon :path="mdiPlus" :size="20" />
        Add Unit
      </Button>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <input
            v-model="search"
            type="text"
            placeholder="Search units..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <!-- Type Filter -->
        <div>
          <select
            v-model="selectedType"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Types</option>
            <option v-for="type in unitTypes" :key="type.value" :value="type.value">
              {{ type.label }}
            </option>
          </select>
        </div>

        <!-- Status Filter -->
        <div>
          <select
            v-model="selectedStatus"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Status</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
          </select>
        </div>

        <!-- Clear Filters -->
        <div>
          <Button
            @click="search = ''; selectedType = ''; selectedStatus = ''"
            class="w-full"
            type="edge"
          >
            Clear Filters
          </Button>
        </div>
      </div>
    </div>

    <!-- Units Table -->
    <div class="bg-white rounded-lg shadow-sm">
      <Table
        :pending="req.pending.value"
        :headers="{
          head: [
            'Unit Name',
            'Symbol',
            'Type',
            'Conversion Factor',
            'Base Unit',
            'Status',
            'Actions'
          ]
        }"
      >
        <template #row>
          <tr
            v-for="(unit, index) in filteredUnits"
            :key="unit.unitUuid"
            class="hover:bg-gray-50"
          >
            <TableNumberCell :index="index" />

            <!-- Unit Name -->
            <td class="px-6 py-4">
              <div>
                <div class="font-medium text-gray-900">{{ unit.unitName }}</div>
                <div v-if="unit.description" class="text-sm text-gray-500">
                  {{ unit.description }}
                </div>
              </div>
            </td>

            <!-- Symbol -->
            <td class="px-6 py-4">
              <span class="font-mono font-semibold text-blue-600">{{ unit.unitSymbol }}</span>
            </td>

            <!-- Type -->
            <td class="px-6 py-4">
              <span :class="getTypeBadgeClass(unit.unitType)" class="px-2 py-1 text-xs font-medium rounded-full">
                {{ unit.unitType }}
              </span>
            </td>

            <!-- Conversion Factor -->
            <td class="px-6 py-4 text-sm text-gray-900">
              {{ unit.conversionFactor || 1 }}
            </td>

            <!-- Base Unit -->
            <td class="px-6 py-4 text-sm text-gray-900">
              {{ unit.baseUnit || '-' }}
            </td>

            <!-- Status -->
            <td class="px-6 py-4">
              <button
                @click="toggleStatus(unit)"
                :class="getStatusBadgeClass(unit.status)"
                class="px-2 py-1 text-xs font-medium rounded-full cursor-pointer hover:opacity-80"
              >
                {{ unit.status }}
              </button>
            </td>

            <!-- Actions -->
            <td class="px-6 py-4">
              <div class="flex items-center gap-2">
                <Button
                  @click="openViewModal(unit)"
                  size="xs"
                  class="text-blue-600 hover:text-blue-800"
                >
                  <BaseIcon :path="mdiEye" :size="16" />
                </Button>
                <Button
                  @click="openEditModal(unit)"
                  size="xs"
                  class="text-green-600 hover:text-green-800"
                >
                  <BaseIcon :path="mdiPencil" :size="16" />
                </Button>
                <Button
                  @click="confirmDelete(unit)"
                  size="xs"
                  class="text-red-600 hover:text-red-800"
                >
                  <BaseIcon :path="mdiDelete" :size="16" />
                </Button>
              </div>
            </td>
          </tr>
        </template>

        <template v-if="!req.pending.value && filteredUnits.length === 0" #placeholder>
          <div class="text-center py-12">
            <div class="text-gray-400 mb-4">
              <BaseIcon :path="mdiPlus" :size="48" class="mx-auto" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No units found</h3>
            <p class="text-gray-500 mb-4">
              {{ search || selectedType || selectedStatus ? 'No units match your filters.' : 'Get started by adding your first unit.' }}
            </p>
            <Button v-if="!search && !selectedType && !selectedStatus" @click="openAddModal" type="primary">
              Add First Unit
            </Button>
          </div>
        </template>
      </Table>
    </div>
  </DefaultPage>
</template>
