import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils.js";

const api = new ApiService();
const path = "/units";

export function getAllUnits(query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path}/all${qr}`);
}

export function createUnit(data) {
  return api.addAuthenticationHeader().post(`${path}/create`, data);
}

export function getUnitById(id) {
  return api.addAuthenticationHeader().get(`${path}/${id}`);
}

export function updateUnitById(id, data) {
  return api.addAuthenticationHeader().put(`${path}/update/${id}`, data);
}

export function deleteUnitById(id) {
  return api.addAuthenticationHeader().delete(`${path}/delete/${id}`);
}

export function updateUnitStatus(id, status) {
  return api.addAuthenticationHeader().patch(`${path}/status/${id}`, { status });
}
