import { ref } from "vue";
import { defineStore } from "pinia";

export const useUnits = defineStore("unitStore", () => {
  const units = ref([]);
  const loading = ref(false);
  const error = ref(null);

  function set(data) {
    units.value = data;
  }

  function getAll() {
    return units.value;
  }

  function add(data) {
    units.value.unshift(data);
  }

  function update(id, data) {
    const idx = units.value.findIndex((el) => el.unitUuid == id);
    if (idx == -1) return;

    units.value.splice(idx, 1, data);
  }

  function remove(id) {
    const idx = units.value.findIndex((el) => el.unitUuid == id);
    if (idx == -1) return;

    units.value.splice(idx, 1);
  }

  function setLoading(state) {
    loading.value = state;
  }

  function setError(errorMessage) {
    error.value = errorMessage;
  }

  function clearError() {
    error.value = null;
  }

  function findById(id) {
    return units.value.find((el) => el.unitUuid == id);
  }

  function findByName(name) {
    return units.value.find((el) => el.unitName?.toLowerCase() === name?.toLowerCase());
  }

  return {
    units,
    loading,
    error,
    getAll,
    set,
    add,
    update,
    remove,
    setLoading,
    setError,
    clearError,
    findById,
    findByName,
  };
});
