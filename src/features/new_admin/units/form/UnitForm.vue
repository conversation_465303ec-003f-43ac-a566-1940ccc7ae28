<script setup>
import Input from '@/components/new_form_elements/Input.vue';
import Textarea from '@/components/new_form_elements/Textarea.vue';
import Select from '@/components/new_form_elements/Select.vue';
import Form from '@/new_form_builder/Form.vue';
import { ref, computed } from 'vue';

const props = defineProps({
  unit: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

// Form data
const unitName = ref(props.unit?.unitName || '');
const unitSymbol = ref(props.unit?.unitSymbol || '');
const unitType = ref(props.unit?.unitType || 'WEIGHT');
const description = ref(props.unit?.description || '');
const conversionFactor = ref(props.unit?.conversionFactor || 1);
const baseUnit = ref(props.unit?.baseUnit || '');
const status = ref(props.unit?.status || 'ACTIVE');

// Unit type options
const unitTypeOptions = [
  { label: 'Weight', value: 'WEIGHT' },
  { label: 'Volume', value: 'VOLUME' },
  { label: 'Length', value: 'LENGTH' },
  { label: 'Count/Piece', value: 'COUNT' },
  { label: 'Time', value: 'TIME' },
  { label: 'Other', value: 'OTHER' }
];

// Status options
const statusOptions = [
  { label: 'Active', value: 'ACTIVE' },
  { label: 'Inactive', value: 'INACTIVE' }
];

// Computed title
const formTitle = computed(() => props.isEdit ? 'Edit Unit' : 'Add New Unit');

// Common weight units for suggestions
const weightUnits = ['mg', 'g', 'kg', 'oz', 'lb'];
const volumeUnits = ['ml', 'l', 'fl oz', 'cup', 'pt', 'qt', 'gal'];
const lengthUnits = ['mm', 'cm', 'm', 'km', 'in', 'ft', 'yd'];
const countUnits = ['pc', 'pcs', 'piece', 'pieces', 'tablet', 'capsule', 'vial', 'bottle', 'box', 'pack'];
const timeUnits = ['sec', 'min', 'hr', 'day', 'week', 'month', 'year'];

const getSuggestions = computed(() => {
  switch (unitType.value) {
    case 'WEIGHT':
      return weightUnits;
    case 'VOLUME':
      return volumeUnits;
    case 'LENGTH':
      return lengthUnits;
    case 'COUNT':
      return countUnits;
    case 'TIME':
      return timeUnits;
    default:
      return [];
  }
});
</script>

<template>
  <Form id="unit-form">
    <div class="grid grid-cols-1 gap-6">
      <!-- Unit Name -->
      <div class="custom-input">
        <Input
          name="unitName"
          v-model="unitName"
          label="Unit Name"
          :focus="!isEdit"
          validation="required|alpha2"
          :attributes="{
            placeholder: 'Enter unit name (e.g., Milligram, Liter, Piece)'
          }"
        />
      </div>

      <!-- Unit Symbol -->
      <div class="custom-input">
        <Input
          name="unitSymbol"
          v-model="unitSymbol"
          label="Unit Symbol/Abbreviation"
          validation="required"
          :attributes="{
            placeholder: 'Enter unit symbol (e.g., mg, ml, pc)',
            maxlength: 10
          }"
        />
        <div v-if="getSuggestions.length > 0" class="mt-2">
          <p class="text-sm text-gray-600 mb-1">Common symbols for {{ unitType.toLowerCase() }}:</p>
          <div class="flex flex-wrap gap-1">
            <button
              v-for="suggestion in getSuggestions"
              :key="suggestion"
              type="button"
              @click="unitSymbol = suggestion"
              class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
            >
              {{ suggestion }}
            </button>
          </div>
        </div>
      </div>

      <!-- Unit Type -->
      <div class="custom-input">
        <Select
          name="unitType"
          v-model="unitType"
          label="Unit Type"
          validation="required"
          :options="unitTypeOptions"
          :attributes="{
            placeholder: 'Select unit type'
          }"
        />
      </div>

      <!-- Conversion Factor and Base Unit (for advanced users) -->
      <div class="grid grid-cols-2 gap-4">
        <div class="custom-input">
          <Input
            name="conversionFactor"
            v-model="conversionFactor"
            label="Conversion Factor"
            validation="num_min:0"
            :attributes="{
              placeholder: '1',
              type: 'number',
              step: '0.001'
            }"
          />
          <p class="text-xs text-gray-500 mt-1">Factor to convert to base unit (default: 1)</p>
        </div>

        <div class="custom-input">
          <Input
            name="baseUnit"
            v-model="baseUnit"
            label="Base Unit (Optional)"
            :attributes="{
              placeholder: 'e.g., gram, liter'
            }"
          />
          <p class="text-xs text-gray-500 mt-1">Reference unit for conversion</p>
        </div>
      </div>

      <!-- Description -->
      <div class="custom-input">
        <Textarea
          name="description"
          v-model="description"
          label="Description (Optional)"
          :attributes="{
            placeholder: 'Enter unit description or usage notes...',
            rows: 3
          }"
        />
      </div>

      <!-- Status (only show in edit mode) -->
      <div v-if="isEdit" class="custom-input">
        <Select
          name="status"
          v-model="status"
          label="Status"
          validation="required"
          :options="statusOptions"
        />
      </div>

      <!-- Information Panel -->
      <div class="bg-blue-50 p-4 rounded-lg">
        <h4 class="font-medium text-blue-800 mb-2">Unit Information</h4>
        <div class="text-sm text-blue-700 space-y-1">
          <p><strong>Name:</strong> {{ unitName || 'Not specified' }}</p>
          <p><strong>Symbol:</strong> {{ unitSymbol || 'Not specified' }}</p>
          <p><strong>Type:</strong> {{ unitTypeOptions.find(opt => opt.value === unitType)?.label || 'Not specified' }}</p>
          <p v-if="conversionFactor !== 1"><strong>Conversion Factor:</strong> {{ conversionFactor }}</p>
          <p v-if="baseUnit"><strong>Base Unit:</strong> {{ baseUnit }}</p>
        </div>
      </div>
    </div>
  </Form>
</template>

<style scoped>
.custom-input :deep(input:focus),
.custom-input :deep(select:focus),
.custom-input :deep(textarea:focus) {
  border-color: #3B82F6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.custom-input :deep(.focus-within\:sys-focus:focus-within) {
  border-color: #3B82F6 !important;
}
</style>
