<script setup>
import Input from '@/components/new_form_elements/Input.vue';
import Textarea from '@/components/new_form_elements/Textarea.vue';
import Select from '@/components/new_form_elements/Select.vue';
import Form from '@/new_form_builder/Form.vue';
import { ref, computed, watch } from 'vue';

const props = defineProps({
  unit: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

// Form data
const unitName = ref(props.unit?.unitName || '');
const unitSymbol = ref(props.unit?.unitSymbol || '');
const unitType = ref(props.unit?.unitType || 'WEIGHT');
const description = ref(props.unit?.description || '');
const conversionFactor = ref(props.unit?.conversionFactor || 1);
const baseUnit = ref(props.unit?.baseUnit || '');
const status = ref(props.unit?.status || 'ACTIVE');

// Unit type options (pharmacy/drug focused)
const unitTypeOptions = [
  { label: 'Weight', value: 'WEIGHT' },
  { label: 'Volume', value: 'VOLUME' },
  { label: 'Count/Piece', value: 'COUNT' },
  { label: 'Dosage', value: 'DOSAGE' },
  { label: 'Concentration', value: 'CONCENTRATION' }
];

// Status options
const statusOptions = [
  { label: 'Active', value: 'ACTIVE' },
  { label: 'Inactive', value: 'INACTIVE' }
];

// Computed title (for future use)
// const formTitle = computed(() => props.isEdit ? 'Edit Unit' : 'Add New Unit');

// Pharmacy/drug-specific unit suggestions
const weightUnits = ['mg', 'g', 'kg', 'mcg', 'µg', 'grain'];
const volumeUnits = ['ml', 'l', 'µl', 'fl oz', 'drop', 'tsp', 'tbsp'];
const countUnits = ['tablet', 'capsule', 'pill', 'vial', 'ampoule', 'bottle', 'box', 'pack', 'strip', 'blister', 'sachet', 'tube', 'jar', 'syringe'];
const dosageUnits = ['IU', 'unit', 'dose', 'spray', 'puff', 'application', 'patch'];
const concentrationUnits = ['mg/ml', 'g/l', '%', 'ppm', 'mg/g', 'IU/ml', 'mcg/ml'];

// Base units for each type
const baseUnits = {
  'WEIGHT': 'gram',
  'VOLUME': 'liter',
  'COUNT': 'piece',
  'DOSAGE': 'unit',
  'CONCENTRATION': 'mg/ml'
};

const getSuggestions = computed(() => {
  switch (unitType.value) {
    case 'WEIGHT':
      return weightUnits;
    case 'VOLUME':
      return volumeUnits;
    case 'COUNT':
      return countUnits;
    case 'DOSAGE':
      return dosageUnits;
    case 'CONCENTRATION':
      return concentrationUnits;
    default:
      return [];
  }
});

// Auto-set base unit when unit type changes
watch(unitType, (newType) => {
  if (newType && baseUnits[newType]) {
    baseUnit.value = baseUnits[newType];
  }
}, { immediate: true });

// Function to get conversion factor for a unit
function getConversionFactor(symbol, type) {
  if (!symbol || !type) return null;

  const lowerSymbol = symbol.toLowerCase();

  if (type === 'WEIGHT') {
    const weightConversions = {
      'mg': 0.001,
      'mcg': 0.000001,
      'µg': 0.000001,
      'g': 1,
      'kg': 1000,
      'grain': 0.0648
    };
    return weightConversions[lowerSymbol] || null;
  }

  if (type === 'VOLUME') {
    const volumeConversions = {
      'ml': 0.001,
      'µl': 0.000001,
      'l': 1,
      'drop': 0.00005,
      'tsp': 0.005,
      'tbsp': 0.015
    };
    return volumeConversions[lowerSymbol] || null;
  }

  if (type === 'COUNT') {
    const countConversions = {
      'tablet': 1,
      'capsule': 1,
      'pill': 1,
      'piece': 1,
      'vial': 1,
      'ampoule': 1,
      'bottle': 1,
      'box': 10,      // 1 box = 10 pieces
      'pack': 10,     // 1 pack = 10 pieces
      'strip': 10,    // 1 strip = 10 pieces
      'blister': 10,  // 1 blister = 10 pieces
      'sachet': 1,
      'tube': 1,
      'jar': 1,
      'syringe': 1
    };
    return countConversions[lowerSymbol] || null;
  }

  if (type === 'DOSAGE') {
    const dosageConversions = {
      'iu': 1,        // International Unit (base)
      'unit': 1,      // Generic unit (base)
      'dose': 1,      // Single dose (base)
      'spray': 1,     // Single spray (base)
      'puff': 1,      // Single puff (base)
      'application': 1, // Single application (base)
      'patch': 1      // Single patch (base)
    };
    return dosageConversions[lowerSymbol] || null;
  }

  if (type === 'CONCENTRATION') {
    // For concentration, most are already in their base form
    // These are just common concentration units
    const concentrationConversions = {
      'mg/ml': 1,     // Base concentration unit
      'g/l': 1000,    // 1 g/l = 1000 mg/ml
      '%': 10000,     // 1% = 10000 mg/ml (for w/v)
      'ppm': 0.001,   // 1 ppm = 0.001 mg/ml
      'mg/g': 1,      // Similar to mg/ml for topical
      'iu/ml': 1,     // International units per ml
      'mcg/ml': 0.001 // 1 mcg/ml = 0.001 mg/ml
    };
    return concentrationConversions[lowerSymbol] || null;
  }

  return null;
}

// Computed property for conversion suggestions
const getConversionSuggestions = computed(() => {
  return getConversionFactor(unitSymbol.value, unitType.value);
});

// Watch for unit symbol changes to suggest conversion factor
watch(unitSymbol, (newSymbol) => {
  if (newSymbol && unitType.value) {
    const suggestion = getConversionFactor(newSymbol, unitType.value);
    if (suggestion !== null) {
      conversionFactor.value = suggestion;
    }
  }
});

// Watch for unit type changes to suggest conversion factor
watch(unitType, (newType) => {
  if (newType && unitSymbol.value) {
    const suggestion = getConversionFactor(unitSymbol.value, newType);
    if (suggestion !== null) {
      conversionFactor.value = suggestion;
    }
  }
});
</script>

<template>
  <Form id="unit-form">
    <div class="grid grid-cols-1 gap-6">
      <!-- Unit Name -->
      <Input
        name="unitName"
        v-model="unitName"
        label="Unit Name"
        :focus="!isEdit"
        validation="required|alpha2"
        :attributes="{
          placeholder: 'Enter unit name (e.g., Milligram, Liter, Piece)'
        }"
      />

      <!-- Unit Symbol -->
      <div>
        <Input
          name="unitSymbol"
          v-model="unitSymbol"
          label="Unit Symbol/Abbreviation"
          validation="required"
          :attributes="{
            placeholder: 'Enter unit symbol (e.g., mg, ml, pc)',
            maxlength: 10
          }"
        />
        <div v-if="getSuggestions.length > 0" class="mt-2">
          <p class="text-sm text-gray-600 mb-1">Common symbols for {{ unitType.toLowerCase() }}:</p>
          <div class="flex flex-wrap gap-1">
            <button
              v-for="suggestion in getSuggestions"
              :key="suggestion"
              type="button"
              @click="unitSymbol = suggestion"
              class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
            >
              {{ suggestion }}
            </button>
          </div>
        </div>
      </div>

      <!-- Unit Type -->
      <Select
        :obj="true"
        name="unitType"
        v-model="unitType"
        label="Unit Type"
        validation="required"
        :options="unitTypeOptions"
        :attributes="{
          placeholder: 'Select unit type'
        }"
      />

      <!-- Conversion Factor and Base Unit (for advanced users) -->
      <div class="grid grid-cols-2 gap-4">
        <div>
          <Input
            name="conversionFactor"
            v-model="conversionFactor"
            label="Conversion Factor (Auto-suggested)"
            validation="num_min:0"
            :attributes="{
              placeholder: '1',
              type: 'number',
              step: '0.001'
            }"
          />
          <p class="text-xs text-gray-500 mt-1">
            Factor to convert to base unit (auto-suggested based on symbol)
          </p>
          <div v-if="getConversionSuggestions && unitSymbol" class="mt-1">
            <p class="text-xs text-blue-600">
              Suggested: 1 {{ unitSymbol }} = {{ getConversionSuggestions }} {{ baseUnit }}
            </p>
            <button
              v-if="conversionFactor !== getConversionSuggestions"
              type="button"
              @click="conversionFactor = getConversionSuggestions"
              class="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 mt-1"
            >
              Use Suggested Value
            </button>
          </div>
        </div>

        <div>
          <Input
            name="baseUnit"
            v-model="baseUnit"
            label="Base Unit (Auto-set)"
            :attributes="{
              placeholder: 'Automatically set based on unit type',
              readonly: true
            }"
          />
          <p class="text-xs text-gray-500 mt-1">Automatically set based on unit type selection</p>
        </div>
      </div>

      <!-- Description -->
      <Textarea
        name="description"
        v-model="description"
        label="Description (Optional)"
        :attributes="{
          placeholder: 'Enter unit description or usage notes...',
          rows: 3
        }"
      />

      <!-- Status (only show in edit mode) -->
      <Select
        v-if="isEdit"
        name="status"
        v-model="status"
        label="Status"
        validation="required"
        :options="statusOptions"
      />

      <!-- Information Panel -->
      <div class="bg-blue-50 p-4 rounded-lg">
        <h4 class="font-medium text-blue-800 mb-2">Unit Information</h4>
        <div class="text-sm text-blue-700 space-y-1">
          <p><strong>Name:</strong> {{ unitName || 'Not specified' }}</p>
          <p><strong>Symbol:</strong> {{ unitSymbol || 'Not specified' }}</p>
          <p><strong>Type:</strong> {{ unitTypeOptions.find(opt => opt.value === unitType)?.label || 'Not specified' }}</p>
          <p><strong>Conversion Factor:</strong> {{ conversionFactor }}</p>
          <p v-if="baseUnit"><strong>Base Unit:</strong> {{ baseUnit }}</p>
          <p v-if="getConversionSuggestions"><strong>Suggested Factor:</strong> {{ getConversionSuggestions }}</p>
        </div>
      </div>


    </div>
  </Form>
</template>

<style scoped>
.custom-input :deep(input:focus),
.custom-input :deep(select:focus),
.custom-input :deep(textarea:focus) {
  border-color: #3B82F6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.custom-input :deep(.focus-within\:sys-focus:focus-within) {
  border-color: #3B82F6 !important;
}
</style>
