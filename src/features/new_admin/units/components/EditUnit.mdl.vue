<script setup>
import NewFormParent from '../../role/components/NewFormParent.vue';
import ModalParent from '@/components/ModalParent.vue';
import Button from '@/components/Button.vue';
import UnitForm from '../form/UnitForm.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import { closeModal, openModal } from '@customizer/modal-x';
import { updateUnitById } from '../Api/UnitApi';
import { useUnits } from '../store/unitStore';
import { toasted } from '@/utils/utils';
import { useForm } from '@/new_form_builder/useForm';

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
});

// Store and form setup
const unitStore = useUnits();
const { submit } = useForm('unit-form');
const req = useApiRequest();

// Methods
function handleSubmit({ values }) {
  if (req.pending.value) return;

  // Check for duplicate unit name or symbol (excluding current unit)
  const existingByName = unitStore.units.find(unit => 
    unit.unitUuid !== props.data.unitUuid && 
    unit.unitName?.toLowerCase() === values.unitName?.toLowerCase()
  );
  
  const existingBySymbol = unitStore.units.find(unit => 
    unit.unitUuid !== props.data.unitUuid && 
    unit.unitSymbol?.toLowerCase() === values.unitSymbol?.toLowerCase()
  );

  if (existingByName) {
    toasted(false, 'Unit name already exists', 'Please choose a different name');
    return;
  }

  if (existingBySymbol) {
    toasted(false, 'Unit symbol already exists', 'Please choose a different symbol');
    return;
  }

  // Confirm update
  openModal(
    'Confirmation',
    {
      title: 'Update Unit',
      message: `Are you sure you want to update the unit "${values.unitName}" (${values.unitSymbol})?`
    },
    (confirmed) => {
      if (confirmed) {
        updateUnitRecord(values);
      }
    }
  );
}

function updateUnitRecord(values) {
  const unitData = {
    unitName: values.unitName.trim(),
    unitSymbol: values.unitSymbol.trim(),
    unitType: values.unitType,
    description: values.description?.trim() || null,
    conversionFactor: parseFloat(values.conversionFactor) || 1,
    baseUnit: values.baseUnit?.trim() || null,
    status: values.status
  };

  req.send(
    () => updateUnitById(props.data.unitUuid, unitData),
    (res) => {
      if (res.success) {
        unitStore.update(props.data.unitUuid, { ...props.data, ...res.data });
        toasted(true, 'Unit updated successfully', '');
        closeModal(res.data);
      } else {
        toasted(false, 'Failed to update unit', res.error);
      }
    }
  );
}
</script>

<template>
  <ModalParent>
    <NewFormParent size="lg" title="Edit Unit">
      <UnitForm :unit="data" :is-edit="true" />
      
      <template #bottom>
        <div class="flex justify-end gap-3 p-2">
          <Button 
            @click="closeModal()" 
            class="px-6 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </Button>
          <Button 
            @click="submit(handleSubmit)" 
            type="primary" 
            :pending="req.pending.value"
            class="px-6 py-2"
          >
            Update Unit
          </Button>
        </div>
      </template>
    </NewFormParent>
  </ModalParent>
</template>
