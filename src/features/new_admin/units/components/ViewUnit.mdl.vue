<script setup>
import NewFormParent from '../../role/components/NewFormParent.vue';
import ModalParent from '@/components/ModalParent.vue';
import Button from '@/components/Button.vue';
import { closeModal } from '@customizer/modal-x';
import { computed } from 'vue';

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
});

const unit = computed(() => props.data);

const unitTypeLabels = {
  'WEIGHT': 'Weight',
  'VOLUME': 'Volume',
  'LENGTH': 'Length',
  'COUNT': 'Count/Piece',
  'TIME': 'Time',
  'OTHER': 'Other'
};

const getStatusBadgeClass = (status) => {
  return status === 'ACTIVE' 
    ? 'bg-green-100 text-green-800' 
    : 'bg-red-100 text-red-800';
};

const getTypeBadgeClass = (type) => {
  const colors = {
    'WEIGHT': 'bg-blue-100 text-blue-800',
    'VOLUME': 'bg-purple-100 text-purple-800',
    'LENGTH': 'bg-yellow-100 text-yellow-800',
    'COUNT': 'bg-green-100 text-green-800',
    'TIME': 'bg-indigo-100 text-indigo-800',
    'OTHER': 'bg-gray-100 text-gray-800'
  };
  return colors[type] || 'bg-gray-100 text-gray-800';
};

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>

<template>
  <ModalParent>
    <NewFormParent size="md" title="Unit Details">
      <div class="space-y-6">
        <!-- Header Info -->
        <div class="text-center border-b pb-4">
          <h2 class="text-2xl font-bold text-gray-900">{{ unit.unitName }}</h2>
          <p class="text-lg font-mono text-blue-600 mt-1">{{ unit.unitSymbol }}</p>
          <div class="flex justify-center gap-2 mt-3">
            <span :class="getTypeBadgeClass(unit.unitType)" class="px-3 py-1 text-sm font-medium rounded-full">
              {{ unitTypeLabels[unit.unitType] || unit.unitType }}
            </span>
            <span :class="getStatusBadgeClass(unit.status)" class="px-3 py-1 text-sm font-medium rounded-full">
              {{ unit.status }}
            </span>
          </div>
        </div>

        <!-- Basic Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Unit Name</label>
              <p class="text-gray-900 bg-gray-50 p-2 rounded">{{ unit.unitName }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Symbol/Abbreviation</label>
              <p class="text-gray-900 bg-gray-50 p-2 rounded font-mono">{{ unit.unitSymbol }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Unit Type</label>
              <p class="text-gray-900 bg-gray-50 p-2 rounded">{{ unitTypeLabels[unit.unitType] || unit.unitType }}</p>
            </div>
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Conversion Factor</label>
              <p class="text-gray-900 bg-gray-50 p-2 rounded">{{ unit.conversionFactor || 1 }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Base Unit</label>
              <p class="text-gray-900 bg-gray-50 p-2 rounded">{{ unit.baseUnit || 'Not specified' }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <p class="text-gray-900 bg-gray-50 p-2 rounded">{{ unit.status }}</p>
            </div>
          </div>
        </div>

        <!-- Description -->
        <div v-if="unit.description">
          <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <p class="text-gray-900 bg-gray-50 p-3 rounded">{{ unit.description }}</p>
        </div>

        <!-- Metadata -->
        <div class="border-t pt-4">
          <h3 class="text-lg font-medium text-gray-900 mb-3">System Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <label class="block text-gray-600">Unit ID</label>
              <p class="font-mono text-gray-900">{{ unit.unitUuid || 'N/A' }}</p>
            </div>
            <div>
              <label class="block text-gray-600">Created Date</label>
              <p class="text-gray-900">{{ formatDate(unit.createdAt) }}</p>
            </div>
            <div>
              <label class="block text-gray-600">Last Modified</label>
              <p class="text-gray-900">{{ formatDate(unit.updatedAt) }}</p>
            </div>
            <div>
              <label class="block text-gray-600">Created By</label>
              <p class="text-gray-900">{{ unit.createdBy || 'System' }}</p>
            </div>
          </div>
        </div>

        <!-- Usage Examples -->
        <div class="bg-blue-50 p-4 rounded-lg">
          <h4 class="font-medium text-blue-800 mb-2">Usage Examples</h4>
          <div class="text-sm text-blue-700 space-y-1">
            <p><strong>Display:</strong> 500 {{ unit.unitSymbol }} of Medicine XYZ</p>
            <p><strong>Conversion:</strong> 
              {{ unit.conversionFactor !== 1 ? `1 ${unit.unitSymbol} = ${unit.conversionFactor} ${unit.baseUnit || 'base units'}` : 'No conversion needed (base unit)' }}
            </p>
            <p><strong>Type:</strong> Used for {{ unitTypeLabels[unit.unitType]?.toLowerCase() || unit.unitType?.toLowerCase() }} measurements</p>
          </div>
        </div>
      </div>

      <template #bottom>
        <div class="flex justify-end p-2">
          <Button 
            @click="closeModal()" 
            type="primary"
            class="px-6 py-2"
          >
            Close
          </Button>
        </div>
      </template>
    </NewFormParent>
  </ModalParent>
</template>
