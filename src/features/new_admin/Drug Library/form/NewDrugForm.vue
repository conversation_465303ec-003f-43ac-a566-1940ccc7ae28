<script setup>
import Button from '@/components/Button.vue';
import Input from '@/components/new_form_elements/Input.vue';
import MultipleSelect from '@/components/new_form_elements/MultipleSelect.vue';
import Select from '@/components/new_form_elements/Select.vue';
import Textarea from '@/components/new_form_elements/Textarea.vue';
import Form from '@/new_form_builder/Form.vue';
import { genId, routes } from '@/utils/utils';
import { ref } from 'vue';

const Taxable = ref('false');
const check = ref('false');
const drugId = ref('');
function handleAutoGenerate() {
	const generatedId = genId.next().value;
	drugId.value = generatedId;
}
</script>

<template>
	<div class="flex flex-col gap-6 rounded-lg bg-white">
		<Form class="grid grid-cols-2 w-full gap-4" id="NewDrugForm" v-slot="{ submit }">
			<Input v-model="drugId" name="drugId" validation="required" label="Drug ID"
				:attributes="{ placeholder: 'Enter Drug ID', type: 'text' }">
			<template #right>
				<button @click.prevent="handleAutoGenerate"
					class="text-sm font-normal px-13 rounded bg-white w-[6.5rem] my-1 mr-2">Auto
					Generate</button>
			</template>
			</Input>
			<Select name="genericName" label="Generic Name" validation="required"
				:options="['Tylenol', 'Advil', 'Amoxil', 'prof', 'Glucophage', 'Lipitor', 'Prilosec', 'Zyrtec', 'Cozaar', 'Norvasc', 'Ventolin']"
				:attributes="{
					type: 'text',
					placeholder: 'Select Generc Name',
				}">
			</Select>
			<Select name="brandName" label="Brand Name" validation="required"
				:options="['Tylenol', 'Advil', 'Amoxil', 'prof', 'Glucophage', 'Lipitor', 'Prilosec', 'Zyrtec', 'Cozaar', 'Norvasc', 'Ventolin']"
				:attributes="{
					type: 'text',
					placeholder: 'Select Brand Name',
				}">
			</Select>
			<Select name="formulation" label="Formulation" validation="required"
				:options="['Tylenol', 'Advil', 'Amoxil', 'prof', 'Glucophage', 'Lipitor', 'Prilosec', 'Zyrtec', 'Cozaar', 'Norvasc', 'Ventolin']"
				:attributes="{
					type: 'text',
					placeholder: 'Formulation',
				}">
			</Select>
			<Select validation="required" label="Route" :attributes="{ placeholder: 'Routes', type: 'text' }"
				:options="routes" name="route" />
			<MultipleSelect name="indication" validation="required" label="Indications"
				:options="['Therapeutic Indications', 'Prophylactic Indications', 'Diagnostic Indications', 'prof', 'Glucophage', 'Palliative Indications']"
				:attributes="{ placeholder: 'indications', type: 'text' }" />
			<Input name="dosage" validation="required" label="Dosage" :attributes="{ placeholder: 'Dosage', type: 'text' }" />
			<Textarea name="sideEffects" label="Side Efects" validation="required" :attributes="{
				type: 'text',
				placeholder: 'side effect',
			}">
</Textarea>
			<div class="flex gap-6 rounded-lg p-4 bg-[#EEEEEE]">
				<input class="custom-checkbox" type="checkbox" v-model="Taxable" value="">
				<label class="text-sm opacity-80 font-bold text-[#1D1C1B]">Taxable</label>
			</div>
			<div class="flex gap-6 rounded-lg p-4 bg-[#EEEEEE]">
				<input class="custom-checkbox" type="checkbox" v-model="check" value="">
				<label class="text-sm opacity-80 font-bold text-[#1D1C1B]">Requires Prescription</label>
			</div>
			<div class="col-span-2">
				<Textarea name="remark" label="Remark" validation="required" :attributes="{
					type: 'text',
					placeholder: 'Chief\'s Complaint',
				}" />
			</div>
		</Form>
		<div class="flex justify-end">
			<Button class="" type="primary">Add Drug</Button>
		</div>
	</div>
</template>
<style scoped>
.custom-checkbox {
	appearance: none;
	width: 20px;
	height: 20px;
	border: 2px solid #1D1C1B;
	border-radius: 4px;
	cursor: pointer;
	position: relative;
	background-color: white;
}

.custom-checkbox:checked::after {
	content: '';
	position: absolute;
	left: 6px;
	top: 2px;
	width: 6px;
	height: 11px;
	border: solid white;
	border-width: 0 2px 2px 0;
	transform: rotate(45deg);
}

.custom-checkbox:checked {
	background-color: #1D1C1B;
}

.custom-checkbox:hover {
	border-color: #000000;
}
</style>