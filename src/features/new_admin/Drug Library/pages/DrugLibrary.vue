<script setup>
import BaseIcon from "@/components/base/BaseIcon.vue";
import Dropdown from "@/components/Dropdown.vue";
import Table from "@/components/Table.vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import { openModal } from "@customizer/modal-x";
import { mdiImport, mdiMagnify, mdiPencil, mdiPlus } from "@mdi/js";
import { ref } from "vue";
import { getAllDrug } from "@/features/admin/drug/Api/drugApi";
import { useDrugs } from "@/features/admin/drug/store/drugStore";

const drugStore = useDrugs();
const pagination = usePaginationcopy({
  store: drugStore,
  cb: (params) => getAllDrug(params),
});
console.log("fghjj");
</script>

<template>
  <div class="flex flex-col gap-4 pt-16 px-12">
    <div class="flex justify-between h-14 mb-3">
      <p class="font-bold text-lg text-[#1D1C1B]">Drugs Library</p>
      <div class="flex gap-3">
        <div
          class="flex gap-2 border rounded px-2 items-center w-[37.5rem] bg-accent focus-within:ring-primary focus-within:border-primary cursor-text"
        >
          <BaseIcon :path="mdiMagnify" :size="20" />
          <input
            v-model="pagination.search.value"
            class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80 w-full"
            placeholder="Search Drugs "
          />
          <!-- @keydown.enter="fetchInstitution"  -->
        </div>
        <button
          @click="openModal('ImportDrugs')"
          class="flex gap-4 bg-secondary text-white px-4 border rounded-lg h-14 items-center"
        >
          <BaseIcon :path="mdiImport" size="lg" />
          <p>Import</p>
        </button>
        <button
          @click.prevent="openModal('AddNewDrugs')"
          class="flex gap-4 bg-primary text-white px-4 border rounded-lg h-14 items-center"
        >
          <BaseIcon :path="mdiPlus" :size="20" />
          <p class="opacity-80">Add New Drug</p>
        </button>
      </div>
    </div>
    <Table
      class=""
      :headers="{
        head: [
          'Drug ID',
          'Generic Name',
          'Brand Name',
          'Formulation',
          'Route',
          'Indications',
          'Dosage',
          'Side Effects',
          'Taxable',
          'Prescriptions',
          'Actions',
        ],
        row: [
          'drugId',
          'genericName',
          'brandName',
          'formulation',
          'route',
          'indications',
          'dosage',
          'sideEffects',
          'taxable',
          'prescriptions',
        ],
      }"
      :cells="{}"
      :pending="pagination.pending.value"
      :rows="drugStore.drugs"
    >
      <template #actions="{ row }"> </template>
    </Table>
  </div>
</template>
