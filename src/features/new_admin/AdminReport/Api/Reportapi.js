import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const api = new ApiService();
const path = "/prescriptions";

export function getSalesReport(query = {}) {
  const qr = getQueryFormObject(query);
  return api
    .addAuthenticationHeader()
    .get(`${path}/PrescriptionSalesReport${qr}`);
}

export function getExportSalesReport(query = {}) {
  const qr = getQueryFormObject(query);
  return api
    .addAuthenticationHeader()
    .get(`${path}/PrescriptionSalesReport${qr}`, {
      responseType: "blob",
    });
}
