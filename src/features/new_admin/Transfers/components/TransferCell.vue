<script setup>
const props = defineProps({
	row: Object
})
</script>
<template>
	<div :class="[
		row.status === 'Pending' ? 'pending' : '',
		row.status === 'In Progress' ? 'in-progress' : '',
		row.status === 'Completed' ? 'completed' : ''
	]" class="grid place-items-center place-content-center text-sm">
		<span class="">{{ row.status }}</span>
	</div>
</template>

<style>
.pending {
	width: 84px;
	color: #B8860B;
	font-weight: 700;
}

.in-progress {
	width: 84px;
	color: #0066CC;
	font-weight: 700;
}

.completed {
	width: 84px;
	color: #1b8f21;
	font-weight: 700;
}
</style>
