<script setup>
import { closeModal, getModal } from "@customizer/modal-x";
import Icon from "@/components/Icon.vue";
import Textarea from "@/components/new_form_elements/Textarea.vue";
import Button from "@/components/Button.vue";
import Form from "@/new_form_builder/Form.vue";
import { ref } from "vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { toasted } from "@/utils/utils";
import Input from "@/components/new_form_elements/Input.vue";

const props = defineProps({
	data: Object,
});
console.log(props.data);

const status = ref(props.data?.status || '');
console.log(status.value);
const req = useApiRequest();

// function confirm({ values }) {
// 	const dataToSubmit = {
// 		...values,
// 		status: status.value
// 	};
// 	req.send(() => updateStatus(props.data?.branchUuid, dataToSubmit),
// 		(res) => {
// 			if (res.success) {
// 				branchesStore.updateStatus(props.data?.branchUuid, { ...props.data, ...dataToSubmit });
// 			}
// 			toasted(res.success, 'Status Updated Successfully', res.error);
// 			closeModal(dataToSubmit);
// 		})
// }

function cancel() {
	closeModal(null);
}

function handleStatusChange(newStatus) {
	// Only update status if it's different from current
	if (status.value !== newStatus) {
		status.value = newStatus;
	}
}

</script>
<template>
	<div @click.self="closeModal()" class="h-full bg-dark/50 grid place-items-center">
		<div class="bg-base-clr-2 flex- flex-col gap-6 rounded-lg p-[2rem] px-6 w-[38rem]">
			<div class="h-12 border-b flex items-center justify-between">
				<p class="font-bold">{{ data?.title || "Confirm or reject transfer" }}</p>
				<button @click="closeModal()">
					<Icon name="solar:close-circle-outline" />
				</button>
			</div>
			<p class="p-2 text-dark/80 opacity-50 mt-3">Are you sure you want to approve or reject the transfer request ?</p>
			<div class="grid grid-cols-2 gap-4 mt-3">
				<div class="flex gap-6 rounded-lg p-4 items-center bg-[#FFDED7]"
					:class="{ 'bg-[#FFDED7]': status !== 'APPROVE', 'bg-[#FFDED7]': status === 'APPROVE' }">
					<input class="custom-checkbox col-span-2" type="checkbox" value="" :checked="status === 'APPROVE'"
						@change="handleStatusChange('APPROVE')">
					<label class="text-sm opacity-80 font-normal text-[#ED6033]"
						:class="{ 'text-[#ED6033]': status !== 'APPROVE', 'text-[#ED6033]': status === 'APPROVE' }">Approve Transfer
						Request</label>
				</div>
				<div class="flex gap-6 rounded-lg p-4 items-center bg-[#EEEEEE]"
					:class="{ 'bg-[#EEEEEE]': status !== 'REJECT', 'bg-gray-300': status === 'REJECT' }">
					<input class="custom-checkbox1 col-span-2" type="checkbox" value="" :checked="status === 'REJECT'"
						@change="handleStatusChange('REJECT')">
					<label class="text-sm opacity-80 font-normal text-[#1D1C1B]"
						:class="{ 'text-[#1D1C1B]': status !== 'REJECT', 'text-[#1D1C1B]': status === 'REJECT' }">Reject
						Transfer Request</label>
				</div>
			</div>
			<div class="w-full">
				<Form class="flex flex-col gap-3 pt-5 w-full" id="remark" v-slot="{ submit }">
					<Input name="reason" label="Reason" :attributes="{ placeholder: 'Type Reason' }" />
					<Textarea name="remark" label="Remark" :attributes="{ placeholder: 'Type your Remark here' }" />
					<div class="flex justify-end py-2 items-center gap-4 mt-3">
						<Button @click.prevent="cancel" class="text-sm border-dark border">Cancel</Button>
						<Button @click.prevent="submit(confirm)" class="text-sm text-white" type="primary">Confirm</Button>
					</div>
				</Form>
			</div>
		</div>
	</div>
</template>

<style scoped>
.custom-checkbox {
	appearance: none;
	width: 20px;
	height: 20px;
	border: 2px solid #ED6033;
	border-radius: 4px;
	cursor: pointer;
	position: relative;
	background-color: white;
}

.custom-checkbox:checked::after {
	content: '';
	position: absolute;
	left: 6px;
	top: 2px;
	width: 6px;
	height: 11px;
	border: solid white;
	border-width: 0 2px 2px 0;
	transform: rotate(45deg);
}

.custom-checkbox:checked {
	background-color: #ED6033;
}

.custom-checkbox1 {
	appearance: none;
	width: 20px;
	height: 20px;
	border: 2px solid #1D1C1B;
	border-radius: 4px;
	cursor: pointer;
	position: relative;
	background-color: white;
}

.custom-checkbox1:checked::after {
	content: '';
	position: absolute;
	left: 6px;
	top: 2px;
	width: 6px;
	height: 11px;
	border: solid white;
	border-width: 0 2px 2px 0;
	transform: rotate(45deg);
}

.custom-checkbox1:checked {
	background-color: #1D1C1B;
}
</style>
