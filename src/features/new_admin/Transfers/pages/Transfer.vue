<script setup>
import BaseIcon from "@/components/base/BaseIcon.vue";
import Button from "@/components/Button.vue";
import Dropdown from "@/components/Dropdown.vue";
import Table from "@/components/Table.vue";
import { usePagination } from "@/composables/usePagination";
import icons from "@/utils/icons";
import { Icon } from "@iconify/vue";
import { mdiMagnify, mdiPlus } from "@mdi/js";
import { ref } from "vue";
import { useRouter } from "vue-router";
import TransferCell from "../components/TransferCell.vue";
import { openModal } from "@customizer/modal-x";
import DefaultPage from "@/components/DefaultPage.vue";

const pagination = usePagination({
  cb: (data) => ({ ...data, limit: 25 }),
});
const search = ref("");
const router = useRouter();

const tempData = ref([
  {
    transferCode: "PR-001",
    from: "Bulbula St. Yossef Branch",
    to: "Yosse<PERSON> Branch",
    drugs: "Paracetamol, Amoxa...",
    remark: "Urgent",
    status: "Pending",
  },
  {
    transferCode: "PR-002",
    from: "Kera St. Bulbula Branch",
    to: "Bulbula Branch",
    drugs: "Paracetamol, Amoxa...",
    remark: "Urgent",
    status: "Pending",
  },
  {
    transferCode: "PR-003",
    from: "4Kilo St. Megenagna Branch",
    to: "Megenagna Branch",
    drugs: "Paracetamol, Amoxa...",
    remark: "Urgent",
    status: "Pending",
  },
  {
    transferCode: "PR-004",
    from: "Bulbula St. Petros",
    to: "Petros Branch",
    drugs: "Paracetamol, Amoxa...",
    remark: "Urgent",
    status: "In Progress",
  },
  {
    transferCode: "PR-005",
    from: "Petros St. 4Kilo",
    to: "4Kilo Branch",
    drugs: "Paracetamol, Amoxa...",
    remark: "Urgent",
    status: "Completed",
  },
  // {
  //   transferCode: "PR-006",
  //   from: "Bulbula St. Yossef Branch",
  //   to: "Megenagna Branch",
  //   drugs: "Paracetamol, Amoxa...",
  //   remark: "Urgent",
  //   status: "Pending",
  // },
  // {
  //   transferCode: "PR-001",
  //   from: "Bulbula St. Yossef Branch",
  //   to: "Megenagna Branch",
  //   drugs: "Paracetamol, Amoxa...",
  //   remark: "Urgent",
  //   status: "In Progress",
  // },
  // {
  //   transferCode: "PR-001",
  //   from: "Bulbula St. Yossef Branch",
  //   to: "Megenagna Branch",
  //   drugs: "Paracetamol, Amoxa...",
  //   remark: "Urgent",
  //   status: "Pending",
  // },
]);
</script>

<template>
  <DefaultPage>
    <div class="flex justify-between items-center">
      <p class="font-bold text-lg text-[#1D1C1B]">Transfers</p>
      <div class="flex gap-2">
        <div class="flex border rounded px-4 items-center bg-accent">
          <input
            v-model="pagination.search.value"
            class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80"
            placeholder="Search Medicine "
          />
          <BaseIcon :path="mdiMagnify" :size="20" />
        </div>
        <Button
          type="primary"
          class="flex gap-4 text-white rounded h-14 items-center"
        >
          <p class="opacity-80">New Transfer</p>
        </Button>
      </div>
    </div>
    <div class="flex-1">
      <Table
        :pending="pagination.pending.value"
        :headers="{
          head: [
            'Transfer Code',
            'From',
            'To',
            'Drugs',
            'Remark',
            'Status',
            'Actions',
          ],
          row: ['transferCode', 'from', 'to', 'drugs', 'remark', 'status'],
        }"
        :rows="tempData || []"
        :cells="{
          status: TransferCell,
        }"
      >
        <template #actions="{ row }">
          <Button
            @click.prevent="openModal('ConfirmRejectTransfer')"
            class="text-sm text-white py-[6px] px-3 rounded-sm"
            type="secondary"
            >Open</Button
          >
        </template>
      </Table>
    </div>
  </DefaultPage>
</template>
