<script setup lang="ts">
import BaseIcon from '@/components/base/BaseIcon.vue';
import Button from '@/components/Button.vue';
import Dropdown from '@/components/Dropdown.vue';
import Table from '@/components/Table.vue';
import { usePagination } from '@/composables/usePagination';
import icons from '@/utils/icons';
import { Icon } from '@iconify/vue';
import { mdiChevronDown, mdiMagnify, mdiPlus } from '@mdi/js';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import QuantityCell from '../components/QuantityCell.vue';
import DefaultPage from "@/components/DefaultPage.vue";


const pagination = usePagination({
	cb: (data) => ({ ...data, limit: 25 }),
});
const search = ref('')
const router = useRouter()

const selectedBranchForMap = ref(null);
const showMapModal = ref(false);

const tempData = ref([
	{
		medicineId: 'Id',
		medicineName: 'medicine name',
		dosageForm: 'Tablet',
		strength: '250mg',
		price: 'price',
		quantity: '430',
	},
	{
		medicineId: 'Id',
		medicineName: 'medicine name',
		dosageForm: 'Tablet',
		strength: '250mg',
		price: 'price',
		quantity: '430',
	},
	{
		medicineId: 'Id',
		medicineName: 'medicine name',
		dosageForm: 'Tablet',
		strength: '250mg',
		price: 'price',
		quantity: '430',
	},
	{
		medicineId: 'Id',
		medicineName: 'medicine name',
		dosageForm: 'Tablet',
		strength: '250mg',
		price: 'price',
		quantity: '430',
	},
	{
		medicineId: 'Id',
		medicineName: 'medicine name',
		dosageForm: 'Tablet',
		strength: '250mg',
		price: 'price',
		quantity: '430',
	},
	{
		medicineId: 'Id',
		medicineName: 'medicine name',
		dosageForm: 'Tablet',
		strength: '250mg',
		price: 'price',
		quantity: '430',
	},
	{
		medicineId: 'Id',
		medicineName: 'medicine name',
		dosageForm: 'Tablet',
		strength: '250mg',
		price: 'price',
		quantity: '430',
	},
]);

</script>

<template>
	<DefaultPage>
		<div class="flex justify-between items-center">
			<p class="font-bold text-lg text-[#1D1C1B]">Central Warehouse</p>
			<div class="flex gap-2">
				<div
					class="w-[37.6rem] flex gap-2 border rounded px-4 py-[14px] items-center bg-accent focus-within:ring-primary focus-within:border-primary cursor-text">
					<BaseIcon :path="mdiMagnify" :size="30" />
					<input v-model="pagination.search.value"
						class="border-0 bg-accent placeholder:text-text-clr placeholder:opacity-80 w-full"
						placeholder="Search Medicine " />
				</div>
			</div>
		</div>
		<div class="flex-1">
			<Table :pending="pagination.pending.value" :headers="{
				head: [
					'Medicine ID',
					'Medicine Name',
					'Dosage Form',
					'Strength',
					'Price',
					'Quantity',
					'Actions'
				],
				row: [
					'medicineId',
					'medicineName',
					'dosageForm',
					'strength',
					'price',
					'quantity',
				],
			}" :rows="(tempData || [])" :cells="{
				quantity: QuantityCell
			}">
				<template #actions="{ row }">
					<Dropdown top="170%" v-slot="{ setRef, toggleDropdown }">
						<p @click="toggleDropdown" class=" decoration-white  py-1 px-3 border-none flex gap-1">
						<div v-if="pagination.pending.value">
							<Icon icon="svg-spinners:3-dots-scale" class="text-2xl" />
						</div>
						<div class="text-[#ED6033] text-sm font-normal" v-if="!pagination.pending.value">More</div>
						<span>
							<BaseIcon class="text-[#ED6033]" :path="mdiChevronDown" :size="24" />
						</span>
						</p>
						<div :ref="setRef">
							<!-- <div class="flex flex-col *:text-left gap-2 p-2 bg-white shadow-lg w-40">
								<button
									class="flex gap-2 px-4 text-black h-10 items-center hover:bg-[#FFEDEF] hover:border hover:rounded">
									<i v-html="icons.editbranch" />
									<p class="">Edit Brach</p>
								</button>
								<button
									class="flex gap-2 px-4 text-black h-10 items-center hover:bg-[#FFEDEF] hover:border hover:rounded">
									<i v-html="icons.changestatus" />
									Change Status
								</button>
								<button
									class="flex gap-2 px-4 text-[#EE6363] h-10 items-center hover:bg-[#FFEDEF] hover:border hover:rounded">
									<i class="text-[#EE6363]" v-html="icons.delete" />
									Delete
								</button>
							</div> -->
						</div>
					</Dropdown>
				</template>
			</Table>
		</div>
	</DefaultPage>
</template>