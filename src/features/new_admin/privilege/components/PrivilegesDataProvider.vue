<script setup>
import { usePagination } from '@/composables/usePagination';
import { watch } from 'vue';
import { usePrivilege } from '../store/privilegeStore';
import { getAllPrivilege } from '../Api/PrivilegeApi';
import { data } from 'autoprefixer';


const props = defineProps({
  prePage: {
    type: Number,
    default: 25
  }
})
const privilegesStore = usePrivilege();

const pagination = usePagination({
  store: privilegesStore,
  cb: (data) => getAllPrivilege({ ...data, limit: 100 }),
});

if (privilegesStore.privilege.length == 0) {
  pagination.send();
}

watch(pagination.data, console.log, { immediate: true })
</script>
<template>
  {{ console.log(privilegesStore.privilege) }}
  <slot :pending="pagination.pending.value" :error="pagination.error.value" :privileges="privilegesStore.privilege" />
</template>
