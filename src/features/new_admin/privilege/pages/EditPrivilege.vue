<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import { useApiRequest } from '@/composables/useApiRequest';
import { getPrivilegeById, updatePrivilege } from '../Api/PrivilegeApi';
import NewFormParent from '../../role/components/NewFormParent.vue';
import PrivilegeForm from '../form/PrivilegeForm.vue';
import { usePrivilege } from '../store/privilegeStore';
import { ref } from 'vue';
import { toasted } from '@/utils/utils.js';
import Button from '@/components/Button.vue';
import { useForm } from '@/new_form_builder/useForm';
import NewFormParent2 from '../../role/components/NewFormParent2.vue';

const { submit } = useForm('privilegeForm');
const privilegeStore = usePrivilege()
const route = useRoute()
const privilegeUuid = route.params.privilegeUuid;
const req = useApiRequest()
const updateReq = useApiRequest()

const privilege = ref(
    privilegeStore.privilege.find((el) => el.privilegeUuid == privilegeUuid) || {}
);

if (!Object.keys(privilege.value).length) {
    req.send(
        () => getPrivilegeById(privilegeUuid),
        (res) => {
            if (res.success) {
                privilege.value = res.data;
            }
        }
    );
}

function update({ values }) {
    updateReq.send(
        () => updatePrivilege(privilegeUuid, values),
        (res) => {
            if (res.success) {
                privilegeStore.update(privilegeUuid, { ...privilege, ...values });
            }
            toasted(res.success, 'Successfully Updated', res.error);
        }
    );
}

const router = useRouter();

const goBack = () => {
    router.go(-1);
};

</script>
<template>
    <button @click.prevent="goBack">
        <div className="p-6 flex gap-2 item-center ">

            <span class="item-center mt-1">
                <svg width="7" height="13" viewBox="0 0 7 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.6" fill-rule="evenodd" clip-rule="evenodd"
                        d="M5.82539 1.0134C6.03505 1.20471 6.05933 1.54072 5.87962 1.76391L2.15854 6.38525L5.87962 11.0066C6.05933 11.2298 6.03505 11.5658 5.82539 11.7571C5.61572 11.9484 5.30007 11.9226 5.12036 11.6994L1.12037 6.73164C0.959876 6.53232 0.959876 6.23819 1.12037 6.03887L5.12036 1.07113C5.30008 0.847943 5.61572 0.822096 5.82539 1.0134Z"
                        fill="#263558" stroke="#263558" stroke-linecap="round" />
                </svg>
            </span>
            <h3>Go Back</h3>
        </div>
    </button>
    <NewFormParent2 size="xl" title="Update Privileges" class="flex flex-col h-96">
        <PrivilegeForm :privilege="privilege" />
        <Button size="xl" type="primary" class="flex justify-center items-center mt-[23rem] gap-3 p-2 bg-primary"
            :pending="req.pending.value" @click.prevent="submit(update)">
            Update Privilege
        </Button>
        <!-- <template #bottom>
            <div class="flex justify-center p-2 px-4">
                <Button class="flex items-center gap-3" :pending="updateReq.pending.value" type="primary"
                    @click.prevent="submit(update)">
                   
                    Update Privilege
                </Button>
            </div>
        </template> -->
    </NewFormParent2>
</template>