<script setup>
import { Form, Input } from '@/components/new_form_elements';
import privileges from '@/privilege.json'

const props = defineProps({
    privilege: Object,
    onSubmit: {
        type: Function,
    },
});
console.log(privileges);

</script>
<template>
    <Form class="grid grid-cols-4 gap-4 grid-rows-3 mt-6 p-6" :inner="false" id="privilegeForm" v-slot="{ }">
        <Input name="privilegeName" validation="required" label="Privilege Name" :value="privilege?.privilegeName || ''"
            :attributes="{
                placeholder: 'Enter Privilege',
            }" />
        <Input validation="required|alpha" class="col-span-2" name="privilegeDescription"
            :value="privilege?.privilegeDescription || ''" label="Privilege Description" :attributes="{
                placeholder: 'Enter Privilege description',
            }" />
        <Input :value="privilege?.privilegeCategory || ''" name="privilegeCategory" label="Privilege Category"
            validation="required|alpha" :attributes="{
                placeholder: 'Enter Privilege category',
            }" />
    </Form>
</template>
