import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const api = new ApiService();

const path = "/auth/users";

export function editProfile(id, qyery, data) {
  const qr = getQueryFormObject(qyery);
  return api
    .addAuthenticationHeader()
    .put(`${path}/editProfile/${id}${qr}`, data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
}

export function getProfileById(id) {
  return api.addAuthenticationHeader().get(`${path}/getProfile/${id}`);
}
