<script setup>
import { useRoute } from "vue-router";
import { useApiRequest } from "@/composables/useApiRequest";
import PatientDetail from "@paper_prescription/components/PatientDetail.vue";
import SelectedDrugsList from "@paper_prescription/components/SelectedDrugsList.vue";
import DrugDetail from "../components/DrugDetail.vue";
import { watch } from "vue";
import Icon from "@components/Icon.vue";
import CompoundingPatientDetail from "@/features/compounding/components/CompoundingPatientDetail.vue";
import CompoundingSelectedDrugList from "@/features/compounding/components/CompoundingSelectedDrugList.vue";
import CompoundingDrugDetail from "../components/compoundingDrugDetail.vue";
import { useCompoundingStore } from "@/features/compounding/store/compoundingStore";
import { getPrescriptionByUuid } from "@/features/compounding/api/compoundingApi";

const compoundingStore = useCompoundingStore();
const route = useRoute();
const paymentReq = useApiRequest();

if (!compoundingStore.selectedPatient) {
  paymentReq.send(
    () => getPrescriptionByUuid(route.params.Uuid),
    (res) => {
      if (res.success) {
        compoundingStore.setData(res.data);
      }
    }
  );
}

watch(
  () => compoundingStore.selectedPatient,
  () => {
    if (compoundingStore.selectedPatient) {
      compoundingStore.setActiveDrug(compoundingStore.selectedDrugList?.[0]);
    }
  },
  { immediate: true }
);
</script>
<template>
  <div class="flex gap-1 h-full overflow-hidden __with-patient-grid">
    <div class="relative gap-1 flex h-full overflow-hidden bg-base-clr-2">
      <CompoundingPatientDetail
        :actions="false"
        class="w-1/2"
        v-if="compoundingStore.selectedPatient"
      />
      <CompoundingSelectedDrugList
        :remove="false"
        :actions="false"
        class="!w-1/2"
        v-if="compoundingStore.selectedPatient"
        @drug:selected="(drug) => compoundingStore.setActiveDrug(drug)"
        :active="compoundingStore.activeDrug?.product_id"
      />
      <div
        v-if="paymentReq.pending.value"
        class="inset-0 z-20 bg-base-clr-2 grid place-items-center absolute"
      >
        <Icon
          :width="50"
          :height="50"
          icon="svg-spinners:3-dots-scale"
          class="text-2xl"
        />
      </div>
    </div>
    <div
      v-if="compoundingStore.selectedPatient"
      class="flex w-full justify-center flex-1 h-full overflow-hidden"
    >
      <CompoundingDrugDetail
        class="w-full"
        :data="compoundingStore.activeDrug"
        :prescriptionUuid="route.params.Uuid"
      />
    </div>
  </div>
</template>
