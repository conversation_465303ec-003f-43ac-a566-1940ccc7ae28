<script setup>
import Button from "@/components/Button.vue";
import Table from "@/components/Table.vue";
import { usePaginationTemp } from "@/composables/usePaginaionTemp";
import { usePagination } from "@/composables/usePagination";
import { searchInvoice } from "@/features/cashier/api/getInvoice";
import { checkMedicineAvailability } from "@/features/paper_prescription/api/paperPrescriptionApi";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import { formatCurrency, removeUndefined } from "@/utils/utils";
import DispenseTableRow from '@/features/dispense/components/DispenseTableRow.vue'
import { watch } from 'vue'
import { useRouter } from "vue-router";
import { formatAgeToFraction } from '@/utils/utils'

const pagination = usePaginationTemp({

  cb: (data) => {
    return searchInvoice(
      removeUndefined({
        search: data.search,
        status: "PAID",
      })
    );
  },
});

function fetchDrugs(ev) {
  pagination.search.value = ev.target.value;
}
const router = useRouter()

</script>
<template>
  <div class="flex h-full pt-8">
    <div class="flex justify-center flex-1 overflow-auto show-scrollbar">
      <div class="flex flex-col gap-4 w-[90%] h-full">
        <div class="flex justify-end">
          <Button type="primary" v-if="router.currentRoute.value.path !== '/paidOrder/compoundingpaidOrder'"
            @click="router.push('/paidOrder/compoundingpaidOrder')" class="text-sm bg-dark text-white py-3" size="md">
            Compounding PaidOrder
          </Button>
        </div>
        <div
          class="flex justify-between gap-2 items-center px-2 rounded border-primary border-2 min-h-drug-search-height">
          <input @input="fetchDrugs" autofocus class="px-2 bg-transparent flex-1 h-12"
            placeholder="Search by Phone Number or Patient ID" />
          <Button size="md" type="primary"> Search </Button>
        </div>
        <div v-if="!pagination.pending.value && !pagination.data.value?.length"
          class="flex-1 flex flex-col gap-12 items-center">
          <img src="/src/assets/img/search_med.svg" />
          <div class="flex flex-col gap-4 items-center">
            <p class="font-medium text-md">
              Please search medicine availability and price.
            </p>
            <p class="text-xs px-20">
              Note: you can search a medicine using medicine or generic name.
            </p>
          </div>
        </div>
        <Table class="" v-else :pending="pagination.pending.value" :headers="{
          head: [
            'Patient Name',
            'Age',
            'Phone Number',
            'Drug Detail',
            'Drug Price',
            'actions',
          ],
          row: [
            'fullName',
            'age',
            'patient.mobilePhone',
            'drugPrescriptions',
            'totalPrice',
          ],
        }" :rows="pagination.data.value || []" :cells="{
          age: (_, row) => formatAgeToFraction(row?.patient?.age),
          fullName(_, row) {
            return `${row.patient?.firstName} ${row?.patient?.fatherName}`
          },
          drugPrescriptions: (drugPrescriptions) => {
            return drugPrescriptions.map((el) => el.drugName).join(',');
          },
          drugPrice: (drugPrescriptions, row) => {
            return row.drugPrescriptions.reduce(
              (accumulator, currentValue) =>
                accumulator + currentValue.list_price,
              0
            );
          },
          totalPrice: (_, row) => {
            return formatCurrency(row?.totalPrice)
          }
        }" :Fallback="TableRowSkeleton" :rowCom="DispenseTableRow" />
      </div>
    </div>
  </div>
</template>
