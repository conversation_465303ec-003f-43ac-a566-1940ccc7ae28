<script setup>
import Button from '@/components/Button.vue';
import NewFormModal from '@/components/NewFormModal.vue';
import { Form } from '@components/new_form_elements'

</script>
<template>
	<NewFormModal>
		<div class="flex p-2 flex-col gap-2">
			<div class="h-12 m-2 flex items-center justify-between">
				<p>Remark</p>
			</div>
			<div>
				<Form id="remark">

				</Form>
				<Button type="primary">
					Submit
				</Button>
			</div>
		</div>
	</NewFormModal>
</template>