<script setup>
import { useApiRequest } from '@/composables/useApiRequest';
import { useD2D } from '@/features/dispense/store/D2DStore'
import { getD2D } from '@/features/dispense/api/d2dApi'
import { computed } from 'vue'
import { usePaperPrescriptionStore } from '@/features/paper_prescription/store/paperPrescriptionStore';

const paperPrescriptionStore = usePaperPrescriptionStore()

const props = defineProps({
  id: String,
})

const req = useApiRequest()

const d2ds = useD2D()

if (!d2ds.d2ds.find(el => el.id == props.id)) {
  req.send(
    () => getD2D({
      prompt: props.id.reduce((state, el, idx) => {
        state += `${el}`
        if (idx < paperPrescriptionStore.selectedDrugList.length - 1) {
          state += ' and '
        }
        return state
      }, `What is the drug interaction between `)
    }),
    res => {
      if (res.success) {
        d2ds.d2ds.push({
          id: props.id.join(','),
          response: res.data
        })
      }
    }
  )
}

const data = computed(() => {
  return d2ds.d2ds.find(el => el.id == (props.id.join && props.id.join(',')))
})

</script>
<template>
  <div v-if="!data && req.pending.value" class="animate-pulse flex flex-col gap-2 items-en w-1/2">
    <p class="h-4 bg-gray-300 rounded-full self-end d w-[90%]"></p>
    <p class="h-4 bg-gray-300 rounded-full"></p>
    <p class="h-4 bg-gray-300 rounded-full"></p>
  </div>
  <div v-else-if="data">
    {{ data?.response }}
  </div>
  <div v-else>
    Couldn't Get Any Data
  </div>
</template>