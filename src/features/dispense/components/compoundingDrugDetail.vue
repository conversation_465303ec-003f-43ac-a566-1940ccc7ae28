<script setup>
import Icon from "@/components/Icon.vue";
import { ref, watch } from "vue";
import { Input, Form, Select, Textarea } from "@components/new_form_elements";
import Button from "@/components/Button.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { toasted } from "@/utils/utils";
import { openModal } from "@customizer/modal-x";
import { updatePrescription } from "@/features/compounding/api/compoundingApi";

const props = defineProps({
  prescriptionUuid: {
    type: String,
  },
  data: {
    type: Object,
  },
});

const data = ref([]);

const toggle = ref(false);

const prescription = useApiRequest();
function dispense({ values }) {
  prescription.send(
    () =>
      updatePrescription(
        props?.prescriptionUuid,
        { status: "DISPENSED" }
      ),
    (res) => {
      toasted(res.success, "Successfully Dispensed", res.error);
      toggle.value = true;
    }
  );
}

watch(
  () => props.data,
  () => {
    data.value = [
      // {
      //   title: "Indications",
      //   value: props?.data?.indications,
      // },
      // {
      //   title: "Cautions",
      //   value: props?.data?.cautions,
      // },
      // {
      //   title: "Contraindication",
      //   value: props?.data?.contraindication,
      // },
      // {
      //   title: "Side Effect",
      //   value: props?.data?.sideEffect,
      // },
    ];
  },
  { immediate: true }
)

function dispenseModal() {
  openModal('Dispense', {}, res => {

  })
}
</script>
<template>
  <div class="w-full h-full flex flex-col gap-6 bg-white">
    <div class="text-base-clr-2 flex px-7 items-center min-h-drug-search-height bg-secondary rounded-md">
      <p class="font-medium">Drug Details</p>
    </div>
    <div class="px-4 overflow-auto show-scrollbar flex-1 overflow-x-hidden">
      <div v-if="!toggle" class="bg-white flex flex-col gap-6">
        <div v-for="detail in data" :key="detail.title" class="gap-2 flex flex-col">
          <div class="bg-base-clr-3 flex flex-col rounded-lg py-2 px-4">
            <p class="border-dark opacity-80 font-bold">{{ detail.title }}</p>
            <ul class="list-disc ml-6">
              <li class="opacity-80">{{ detail.value }}</li>
            </ul>
          </div>
        </div>
        <Form class="flex flex-col gap-4" v-slot="{ submit }" id="payment-form">
          <Textarea label="Remark" :attributes="{ placeholder: 'Type your remark here' }" name="remark" />
          <div class="py-4 border-t mt-2 flex items-center gap-2 justify-end">
            <Button size="md" @click.prevent="submit(dispense)" type="secondary">
              Dispense
            </Button>
          </div>
        </Form>
      </div>
      <div v-else class="flex-1 flex flex-col text-center items-center gap-4">
        <img src="/src/assets/img/dispense.svg" />
        <div class="p-4 px-14 flex flex-col gap-4">
          <p class="font-bold text-md">Drug Dispensed Successfully!</p>
          <p class="text-xs w-72">
            Click on “Another Order” to process another dispensary.
          </p>
          <Button @click.stop="$router.push('/paidOrder')" type="secondary">Another Order</Button>
        </div>
      </div>
    </div>
  </div>
</template>
