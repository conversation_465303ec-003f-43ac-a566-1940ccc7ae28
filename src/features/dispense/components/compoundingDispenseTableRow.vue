<script setup>
import { ref, watch, computed } from 'vue'
import Button from '@components/Button.vue'
import { genId } from '@/utils/utils';
import D2DInteraction from './D2DInteraction.vue';
import { usePaperPrescriptionStore } from "@paper_prescription/store/paperPrescriptionStore";
import { useRouter } from 'vue-router'
import CompoundingD2DInteractionPage from '@/features/compounding/components/CompoundingD2DInteractionPage.vue';
import D2DInteractionPage from '@/features/paper_prescription/components/D2DInteractionPage.vue';
import { useCompoundingStore } from '@/features/compounding/store/compoundingStore';

const props = defineProps({
  headKeys: {
    type: Array,
    required: true
  },
  rowData: {
    type: Array,
    required: true
  },
  rowKeys: {
    type: Array,
    required: true
  },
  cells: Object
})
const emit = defineEmits(['row'])

const router = useRouter()
const compoundingStore = useCompoundingStore();

const rows = ref((props.rowData || []).map(el => ({ ...el, id: genId.next().value })))

const openedRows = ref('')
const opened = computed(() => {
  return (id) => {
    console.log('id', id, !!openedRows.value.find(el => el == id))
    return openedRows.value.includes(id)
  }
})

function toggleDropdown(id) {
  console.log(id)
  if (openedRows.value == id) {
    openedRows.value = ''
  } else {
    openedRows.value = id
  }
}

watch(openedRows, () => console.log(props.rowData), { immediate: true })
watch(props, () => {
  rows.value = (props.rowData || []).map(el => ({ ...el, id: genId.next().value }))
})

function setPatien(data) {
  compoundingStore.setData(data);

  router.push({
    path: '/paidOrder/compoundingdispense',
    name: 'Compounding_Dispense',
    params: {
      Uuid: data?.prescriptionUuid,
    },
  })
}
</script>
<template>
  <tbody>
    <template :key="row.id" v-for="(row, index) in rows">
      <tr @click="emit('row', row)"
        class="cursor-pointer hover:bg-gray-300 odd:bg-gray-200 bg-white border-x-[0.2px] border-b-[0.2px] border-t-[0.2px]">
        <td class="p-2">{{ index + 1 }}</td>
        <td class="p-2 max-w-40" :key="key" v-for="key in rowKeys">
          <span v-if="!Object.keys(cells || {}) || !cells?.[key]">
            {{
              key.split(".").reduce((all, el) => {
                return all?.[el];
              }, row)
            }}
          </span>
          <component v-else-if="Object.keys(cells || {}) && cells[key].__hmrId" :row="row" :is="cells[key]" />
          <span v-else-if="typeof cells[key] == 'function'">
            {{ cells[key](row?.[key], row) }}
          </span>
        </td>
        <td class="p-3 flex items-center gap-3">
          <!-- <Button @click="() => {
            paperPrescriptionStore.setData(row)
            toggleDropdown(row.id)
          }" size="xs" class="text-sm text-white bg-error ">
            D2D
          </Button> -->
          <Button @click.stop="setPatien(row)" class="text-sm bg-dark text-white" size="xs">
            Dispense
          </Button>
        </td>
      </tr>
      <tr v-if="openedRows == row.id">
        <td :colspan="headKeys.length + 1">
          <div class="bg-white p-4 max-w-full">
            <D2DInteractionPage
              :id="row?.drugPrescriptions ? row?.drugPrescriptions.map(el => el.drugName?.trim()) : ''" />
          </div>
        </td>
      </tr>
    </template>
  </tbody>
</template>