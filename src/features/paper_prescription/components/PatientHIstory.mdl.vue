<script setup>
import { closeModal } from "@customizer/modal-x";
import Icon from "@components/Icon.vue";
import Table from "@components/Table.vue";
import { getPatientHistory } from "../api/patientApi";
import { useApiRequest } from "@/composables/useApiRequest";
import { ref, watch } from "vue";
import { formatCurrency, secondDateFormat } from '@/utils/utils'
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import Button from '@components/Button.vue'

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const req = useApiRequest();

req.send(
  () => getPatientHistory(props.data?.patientUuid),
  (res) => { }
);

const drugPrescriptions = ref([]);
const vitlas = ref([]);

watch(req.response, () => {
  console.log(req.response.value)
  if (req.response.value) {
    drugPrescriptions.value = req.response.value?.prescriptions?.map(el => {
      const dp = el.drugPrescriptions?.[0] || {}
      const vis = el.visits || {}
      delete el.drugPrescriptions
      delete el.visits
      return {
        ...el,
        ...dp,
        ...vis
      }
    });
    vitlas.value = req.response.value?.vitals || []
    console.log(drugPrescriptions.value)
  }
});
</script>
<template>
  <div class="min-h-full grid p-4 backdrop-blur-sm place-items-center">
    <div class="min-h-[10rem] h-full bg-base-clr-2 rounded-md shadow-lg w-[70rem]">
      <div class="flex justify-between border-b p-2 m-2">
        <p class="font-bold text-lg">Patient History</p>
        <button @click="closeModal()" class="w-10">
          <Icon name="solar:close-circle-outline" />
        </button>
      </div>
      <div class="flex flex-col overflow-auto show-scrollbar">
        <p class="p-2 font-bold border-t">Prescription History</p>
        <Table :pending="req.pending.value" :headers="{
          head: [
            'Encounter Number',
            'Vist Date',
            'Medicine Name',
            'Dosage form',
            'Duration',
            'Price',
            'Quantity',
            'Status',
          ],
          row: [
            'encounterNumber',
            'visitDate',
            'drugName',
            'dosageForm',
            'duration',
            'list_price',
            'totalQuantity',
            'status',
          ],
        }" :rows="drugPrescriptions" :cells="{
          visitDate: (date, row) => {
            console.log(date)
            return secondDateFormat(date)
          },
          list_price: (price) => {
            return formatCurrency(price);
          },
          drugName: (_, row) => {
            return row.drugPrescriptions?.map(drug => drug.drugName).join(', ') || 'N/A';
          }
        }" :Fallback="TableRowSkeleton" />
        <p class="p-2 font-bold">Vitals</p>
        <Table :pending="req.pending.value" :headers="{
          head: [
            'Temprature',
            'Blood pressure',
            'Blood glucose level',
            'Height (cm)',
            'Weight (kg)',
            'BMI',
            'Pulse rate',
          ],
          row: ['temperature', 'bloodPressure', 'bloodGlucoseLevel', 'height', 'weight', 'bmi', 'pulseRate']
        }" :Fallback="TableRowSkeleton" :rows="vitlas || []" />
      </div>
    </div>
  </div>
</template>
