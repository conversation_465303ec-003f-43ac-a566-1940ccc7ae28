<script setup>
import Button from '@/components/Button.vue';
import PatientVitalsForm from '../form/PatientVitalsForm.vue';
import { openModal } from '@customizer/modal-x';
import { usePaperPrescriptionStore } from '@paper_prescription/store/paperPrescriptionStore'
import { useApiRequest } from '@/composables/useApiRequest'
import { removeUndefined, toasted } from '@/utils/utils';
import { useForm } from '@/new_form_builder/useForm'
import { updatePatient } from '@paper_prescription/api/patientApi'

const paperPrescriptionStore = usePaperPrescriptionStore()
const props = defineProps({
  sendToPayment: Function
})

function send() {
  openModal('Confirmation', {
    title: 'Send To Payment',
    message: 'Are You Sure?'
  }, res => {
    if(res) {
      props.sendToPayment()
    }
  })
}

const vitalsReq = useApiRequest()
function addVitals({values}) {
  if(!Object.values(values).some(el => el) || vitalsReq.pending.value) return

  const data = {
    ...paperPrescriptionStore.selectedPatient,
    vitals: [values],
    userUuid: undefined
  }

  vitalsReq.send(
    () => updatePatient(paperPrescriptionStore.selectedPatient?.userUuid, removeUndefined(data)),
    res => {
      toasted(res.success, 'Succesfully Added Vitals', res.error)
    }
  )
}

const {submit} = useForm('vitals')
</script>
<template>
  <div class="bg-white p-2">
    <p class="p-2 font-bold">
      Screening (POC) Test's 
    </p>
    <PatientVitalsForm
      :addVitals="addVitals"
      :patient="paperPrescriptionStore.selectedPatient"
    >
      <template #default>
        <div class="flex border-t items-center justify-end p-2 gap-2">
          <Button :pending="vitalsReq.pending.value" @click.prevent="submit(addVitals)" type="primary">
            Edit Vitals
          </Button>
          <Button :disabled="vitalsReq.pending.value" @click.prevent="send" type="secondary">
            Send to Payment
          </Button>
        </div>
      </template>
    </PatientVitalsForm>
  </div>
</template>