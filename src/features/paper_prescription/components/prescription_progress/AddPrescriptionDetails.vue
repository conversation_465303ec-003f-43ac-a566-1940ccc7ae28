<script setup>
import { nextTick, ref, watch } from "vue";
import PrescriptionDetailForm from "../form/PrescriptionDetailForm.vue";
import { usePaperPrescriptionStore } from "@paper_prescription/store/paperPrescriptionStore";
import Button from "@/components/Button.vue";
import router from "@/router";
import { useRouter } from "vue-router";

const route = useRouter();
const props = defineProps({
  goToVitals: Function,
});

const paperPrescriptionStore = usePaperPrescriptionStore();

const active = ref(0);

function submit(data, reset) {
  paperPrescriptionStore.setPrescriptionDetail(data);
  if (active.value + 1 <= paperPrescriptionStore.selectedDrugList?.length - 1) {
    reset();
    nextTick(() => {
      paperPrescriptionStore.setActiveDrug(
        paperPrescriptionStore.selectedDrugList?.[++active.value]
      );
    });
  }

  nextTick(() => {
    if (paperPrescriptionStore.prescriptionFilled) {
      props.goToVitals();
    }
  })
}

watch(
  () => paperPrescriptionStore.activeDrug,
  () => {
    const idx = paperPrescriptionStore.selectedDrugList.findIndex(
      (el) => el.product_id == paperPrescriptionStore.activeDrug?.product_id
    );
    if (idx > -1) {
      active.value = idx;
    }
  }
);

watch(
  () => paperPrescriptionStore.selectedDrugList,
  (newVal, oldVal) => {
    if (newVal?.length > oldVal?.length && oldVal) return;

    if (paperPrescriptionStore.selectedDrugList?.length === 0) return;

    if (active.value >= paperPrescriptionStore.selectedDrugList?.length) {
      active.value = paperPrescriptionStore.selectedDrugList?.length - 1;
      paperPrescriptionStore.setActiveDrug(
        paperPrescriptionStore.selectedDrugList?.[active.value]
      );
      return;
    }

    paperPrescriptionStore.setActiveDrug(
      paperPrescriptionStore.selectedDrugList?.[active.value]
    );
  },
  { immediate: true, deep: true }
);

watch(
  () => paperPrescriptionStore.selectedDrugList,
  (val) => {
    if (val?.length == 0) {
      route.replace("/paper_prescription");
    }
  },
  { immediate: true, deep: true }
);
</script>
<template>
  <div class="bg-white">
    <p class="p-2">
      Prescribed to
      <strong>{{
        `${paperPrescriptionStore?.selectedPatient?.firstName} ${paperPrescriptionStore?.selectedPatient?.fatherName}`
      }}</strong>
    </p>
    <p class="px-2 text-sm italic">
      prescription detail for
      <strong class="underline"
        >{{ paperPrescriptionStore.activeDrug?.drugName }}
        {{ paperPrescriptionStore.activeDrug?.dosageForm }}</strong
      >
    </p>
    <PrescriptionDetailForm
      :prescription="paperPrescriptionStore.getPrescriptionDetail?.drug || {}"
      :onSubmit="submit"
    >
      <!-- <template v-if="paperPrescriptionStore.prescriptionFilled" #more-actions>
        <Button @click.prevent="goToVitals" type="secondary"> Continue </Button>
      </template> -->
    </PrescriptionDetailForm>
  </div>
</template>
