<script setup>
import { useApiRequest } from "@/composables/useApiRequest";
import { searchForPatient } from "../../api/patientApi";
import { usePaperPrescriptionStore } from "../../store/paperPrescriptionStore";
import { ref } from "vue";
import Table from "@/components/Table.vue";
import Button from "@/components/Button.vue";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import { formatAgeToFraction, secondDateFormat } from "@/utils/utils";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { mdiPencil } from "@mdi/js";
import { openModal } from "@customizer/modal-x";

const paperPrescriptionStore = usePaperPrescriptionStore();

const props = defineProps({
  setPatient: Function,
  setActive: Function,
});

const patientReq = useApiRequest();
const search = ref("");

function getPatient(ev) {
  patientReq.send(
    () => searchForPatient({ search: search.value }),
    (res) => {
      console.log(res);
    },
    true
  );
}

function patientSelected(patient) {
  props.setPatient(patient);
}
</script>
<template>
  <div class="bg-base-clr-2 px-7 py-8 flex flex-col gap-2 flex-1">
    <div
      class="flex justify-between gap-2 items-center px-2 rounded border-primary border-2 min-h-drug-search-height"
    >
      <input
        v-focus
        v-model="search"
        @input="getPatient"
        class="px-2 bg-transparent flex-1 h-12"
        placeholder="Search Patient (MRN, PHONE, NAME, FAYDA ID, INSURANCE ID)"
      />
      <Button @click="getPatient" size="md" type="primary"> Search </Button>
    </div>
    <div
      v-if="
        !patientReq.pending.value &&
        !patientReq.response.value?.length &&
        !paperPrescriptionStore.selectedPatient
      "
      class="flex-1 p-4 bg-base-clr-1 flex flex-col gap-4 items-center"
    >
      <img class="size-52" src="/src/assets/img/search_med.svg" />
      <div class="flex flex-col gap-4 items-center">
        <p class="font-medium text-md">Please search for a Patient</p>
      </div>
    </div>
    <div v-else>
      <Table
        @row="patientSelected"
        :pending="patientReq.pending.value"
        :Fallback="TableRowSkeleton"
        :headers="{
          head: [
            'MRN',
            'Full Name',
            'Phone number',
            'Age',
            'Gender',
            'Actions',
          ],
          row: ['cardNumber', 'fullname', 'mobilePhone', 'age', 'gender'],
        }"
        :rows="
          patientReq.response.value ||
          (paperPrescriptionStore.selectedPatient
            ? [paperPrescriptionStore.selectedPatient]
            : [])
        "
        :cells="{
          age: formatAgeToFraction,
          fullname: (_, row) => {
            return `${row?.title ?? ''} ${row?.firstName ?? ''} ${
              row?.fatherName ?? ''
            } ${row?.grandFatherName ?? ''}`;
          },
          birthDate: (date) => {
            return secondDateFormat(date);
          },
        }"
      >
        <template #actions="{ row }">
          <!-- event.stopPropagation() is equivalent to @click.stop -->
          <div class="flex gap-2 items-center">
            <Button
              size="xs"
              @click.stop.prevent="openModal('EditPatient', row)"
              class="!w-auto !h-auto !p-0 !px-2 text-xs text-white rounded bg-red-500 flex items-center"
            >
              <BaseIcon size="10" :path="mdiPencil" class="text-white" />
              Edit
            </Button>
            <Button @click.stop.prevent="patientSelected(row)" type="secondary" size="xs">
              Select
            </Button>
          </div>
        </template>
      </Table>
    </div>
  </div>
</template>
