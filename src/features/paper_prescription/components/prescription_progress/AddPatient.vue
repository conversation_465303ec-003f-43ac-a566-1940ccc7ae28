<script setup>
import PatientForm from "@/features/patients/components/form/PatientForm.vue";
import SearchPatients from "./SearchPatients.vue";
import { computed, ref, shallowRef, watch } from "vue";
import Button from "@/components/Button.vue";
import Icon from "@/components/Icon.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { addPatient as add } from "@paper_prescription/api/patientApi";
import { toasted } from "@utils/utils";

const props = defineProps({
  active: {
    type: String,
    default: "Search Patients",
  },
  setPatient: Function,
});

const components = [
  {
    name: "Search Patients",
    component: SearchPatients,
  },
  {
    name: "Add Patient",
    component: PatientForm,
  },
];

const activeComponent = shallowRef(
  components.find((el) => el.name == props.active)?.component
);

function setActive(name) {
  activeComponent.value = components.find((el) => el.name == name)?.component;
}

const patientReq = useApiRequest();

function addPatient(values) {
  if (patientReq.pending.value) return;
  console.log(values);
  patientReq.send(
    () => add({ ...values, vitals: [] }),
    (res) => {
      if (res.success) {
        props.setPatient({ ...values, ...res.data });
      }
      toasted(res.success, "Succesfully Added a Patient", res.error);
    }
  );
}

watch(
  () => props.active,
  () => {
    activeComponent.value = components.find(
      (el) => el.name == props.active
    )?.component;
  }
);
</script>
<template>
  <div class="flex-1 flex flex-col">
    <div v-if="activeComponent == components[1]?.component" class="p-2">
      <Button
        @click.stop="setActive('Search Patients')"
        class="flex items-center gap-2 pl-2"
        size="xs"
        type="secondary"
      >
        <Icon
          color="rgb(var(--base-clr-2))"
          class="rotate-90"
          name="solar:alt-arrow-down-outline"
        />
        Back to search
      </Button>
    </div>
    <div class="p-2 bg-white">
      <component
        btnText="Add Patient"
        :setActive="setActive"
        :setPatient="setPatient"
        :addPatient="addPatient"
        :pending="patientReq.pending.value"
        :is="activeComponent"
      />
      <div v-if="activeComponent != PatientForm" class="flex items-center gap-3 justify-center">
        <p>Add New Patient</p>
        <button
          @click.stop="setActive('Add Patient')"
          class="italic text-sm text-primary"
        >
          Add
        </button>
      </div>
    </div>
  </div>
</template>
