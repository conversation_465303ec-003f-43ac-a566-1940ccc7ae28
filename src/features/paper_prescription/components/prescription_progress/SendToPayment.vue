<script setup>
import Button from "@/components/Button.vue";
import { usePaperPrescriptionStore } from "@paper_prescription/store/paperPrescriptionStore";
import { useRouter } from "vue-router";
import { useApiRequest } from "@/composables/useApiRequest";
import {
  addPrescription,
  changePrescription,
  changePrescriptionBystatus,
  updatePrescription,
} from "@paper_prescription/api/paperPrescriptionApi";
import { toasted } from "@/utils/utils";

const router = useRouter();
const paperPrescriptionStore = usePaperPrescriptionStore();
const prescriptions = useApiRequest();

function reset() {
  paperPrescriptionStore.reset();
  router.replace("/paper_prescription");
}

const filteredDrugs =
  paperPrescriptionStore.paper_prescription.prescriptionDetail.filter(
    (drug) => drug?.drug?.totalQuantity < drug?.drug?.totalAmount
  );
const filteredDrugDetails =
  paperPrescriptionStore.priscripprescriptionDetail.filter((detail) =>
    filteredDrugs.some(
      (drug) => drug.inventoryUuid === detail.drug.inventoryUuid
    )
  );
console.log(filteredDrugDetails);

function send() {
  if (
    !prescriptions.dirty.value &&
    !paperPrescriptionStore.done &&
    prescriptions.pending.value
  )
    return;

  paperPrescriptionStore.setDone(true);
  // paperPrescriptionStore.priscripprescriptionDetail
  prescriptions.send(
    () =>
      addPrescription({
        patientUuid: paperPrescriptionStore.selectedPatient.userUuid,
        drugPrescriptions:
          paperPrescriptionStore.priscripprescriptionDetail.map(
            ({ drug: d }) => {
              const {
                frequency,
                drug,
                dosage_form,
                location_id,
                location_name,
                quantity,
                ...rest
              } = d;
              console.log("here ", rest);

              return rest;
            }
          ),
        status: "REQUESTED",
      }),
    (res) => {
      toasted(res.success, "Invoice created successfully", res.error);
      if (!res.success) {
        paperPrescriptionStore.setDone(false);
      }
    }
  );
}

function update() {
  if (
    !prescriptions.dirty.value &&
    !paperPrescriptionStore.done &&
    prescriptions.pending.value
  )
    return;

  console.log("here");

  paperPrescriptionStore.setDone(true);

  // prescriptions.send(
  //   () =>
  //     changePrescription(
  //       paperPrescriptionStore.paper_prescription.prescriptionUuid,
  //       {
  //         patientUuid: paperPrescriptionStore.selectedPatient.userUuid,
  //         drugPrescriptions: paperPrescriptionStore.priscripprescriptionDetail.map(({ drug: d }) => {
  //           const {
  //             frequency,
  //             drug,
  //             dosage_form,
  //             location_id,
  //             location_name,
  //             quantity,
  //             ...rest
  //           } = d;
  //         console.log('hallo', rest);

  //           return rest;
  //         }),

  //         status: "REQUESTED",
  //       }
  //     ),
  //   (res) => {
  //     toasted(res.success, "Invoice created successfully", res.error);
  //     if (!res.success) {
  //       paperPrescriptionStore.setDone(false);
  //     }
  //   }
  // );

  prescriptions.send(
    () =>
      updatePrescription(
        {
          prescriptionUuid:
            paperPrescriptionStore.paper_prescription.prescriptionUuid,
        },
        {
          paymentType: null,
          fsNumber: null,
          status: "REQUESTED",
        }
      ),
    (res) => {
      toasted(res.success, "Invoice created successfully", res.error);
      if (!res.success) {
        paperPrescriptionStore.setDone(false);
      }
    }
  );
}

function updateByStatus() {
  if (
    !prescriptions.dirty.value &&
    !paperPrescriptionStore.done &&
    prescriptions.pending.value
  )
    return;

  paperPrescriptionStore.setDone(true);

  prescriptions.send(
    () =>
      changePrescriptionBystatus(
        {
          prescriptionUuid:
            paperPrescriptionStore.paper_prescription.prescriptionUuid,
        },
        {
          status: "REQUESTED",
          paymentType: "Cash",
        }
      ),
    (res) => {
      toasted(res.success, "Invoice created successfully", res.error);
      if (!res.success) {
        paperPrescriptionStore.setDone(false);
      }
    }
  );
}

function check() {
  console.log(paperPrescriptionStore.ePrescription);

  if (paperPrescriptionStore.ePrescription) {
    updateByStatus();
  } else if (paperPrescriptionStore.edit) {
    update();
  } else {
    send();
  }
}

check();
</script>
<template>
  <div
    class="relative flex-1 p-4 bg-white flex flex-col gap-6 justify-center items-center"
  >
    <div
      v-if="prescriptions.pending.value"
      class="grid place-content-center absolute inset-0 z-20 backdrop-blur-sm"
    >
      <p class="bg-white p-2 rounded shadow-md">... Creating Invoice</p>
    </div>
    <img src="/src/assets/img/payment.svg" />
    <div
      v-if="!prescriptions.error.value"
      class="flex items-center gap-4 justify-center flex-col"
    >
      <p class="font-bold text-md">Order Sent to Payment Successfully!</p>
      <p>Click on “Another Prescription” to process another Prescription</p>
      <Button @click="reset" type="secondary"> Another Prescription </Button>
    </div>
    <div v-else class="text-center flex flex-col gap-2s">
      <p class="text-error p-2">{{ prescriptions.error.value }}</p>
      <Button
        :pending="prescriptions.pending.value"
        type="primary"
        @click="check"
        >Retry</Button
      >
    </div>
  </div>
</template>
