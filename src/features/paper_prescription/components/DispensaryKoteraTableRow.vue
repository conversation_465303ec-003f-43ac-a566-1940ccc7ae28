<script setup>
import { ref, watch, computed, onMounted, inject } from "vue";
import Button from "@components/Button.vue";
import { genId, toasted } from "@/utils/utils";
import { usePaperPrescriptionStore } from "@paper_prescription/store/paperPrescriptionStore";
import { useRouter } from "vue-router";
import CompoundingD2DInteractionPage from "@/features/compounding/components/CompoundingD2DInteractionPage.vue";
import D2DInteractionPage from "@/features/paper_prescription/components/D2DInteractionPage.vue";
import { mdiArrowDown } from "@mdi/js";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { openModal } from "@customizer/modal-x";
import { useApiRequest } from "@/composables/useApiRequest";
import { useKoteraStore } from "@/features/StoreKeeper/store/koteraStore";
import ActualQuantityInput from "@/features/StoreKeeper/components/ActualQuantityInput.vue";
import Form from "@/new_form_builder/Form.vue";
import { createStoreNewKotera } from "@/features/StoreKeeper/api/InventoryApi";

const props = defineProps({
  modelValue: {
    required: false,
  },
  headKeys: {
    type: Array,
    required: true,
  },
  rowData: {
    type: Array,
    required: true,
  },
  rowKeys: {
    type: Array,
    required: true,
  },
  cells: Object,
});

const rounds = ref(props.modelValue || []);
const emit = defineEmits(["row", "update:modelValue"]);
const router = useRouter();
const paperPrescriptionStore = usePaperPrescriptionStore();
const koteraStore = useKoteraStore();
const req = useApiRequest();
const pending = ref([]);

const rows = ref(
  (props.rowData || []).map((el) => ({
    ...el,
    id: genId.next().value,
    result: null,
    isSubmitted: !!el.countedStoreAmount,
    countedStoreAmount: el.countedStoreAmount || null,
  }))
);

watch(
  rows,
  () => {
    console.log(rows.value);
  },
  { deep: true }
);

// watch(() => props.rowData, () => {
// 	console.log(props.rowData)
// 	props.rowData.forEach(row => {
// 		console.log('hahu')
// 		updateRowResult(row)
// 	})
// 	rows.value = (props.rowData || []).map(el => ({ ...el, id: genId.next().value, result: null, isSubmitted: false }))
// })

watch(
  () => props.rowData,
  (newData) => {
    rows.value = (newData || []).map((el) => {
      const existingRow =
        rows.value.find((r) => r.storeUuid === el.storeUuid) || {};
      return {
        ...el,
        id: existingRow.id || genId.next().value,
        countedStoreAmount:
          el.countedStoreAmount || existingRow.countedStoreAmount || null,
        isSubmitted: !!el.countedStoreAmount || existingRow.isSubmitted,
        result: existingRow.result || null,
      };
    });

    // Update results for all rows with data
    rows.value.forEach((row) => {
      if (
        row.totalAmount !== undefined &&
        row.countedStoreAmount !== undefined
      ) {
        updateRowResult.value(row);
      }
    });
  },
  { deep: true }
);

watch(
  rounds,
  () => {
    console.log(rounds.value, "here");
    emit("update:modelValue", rounds.value);
  },
  { deep: true }
);

watch(
  () => props.modelValue,
  () => {
    rounds.value = props.modelValue;
  }
);

const koteraType = inject('koteraType', 'Monthly')


function submitKoteraCheck(row) {
  if (row.countedStoreAmount === null) return;

  console.log('here', koteraType.value);

  pending.value.push(row.storeUuid);
  req.send(
    () =>
      createStoreNewKotera(row.storeUuid, {
        koteraType: koteraType.value,
        countedQuantity: row.countedStoreAmount,
      }),
    (res) => {
      if (res.success) {
        koteraStore.updateAmount(row.storeUuid, {
          ...row,
          countedQuantity: row.countedStoreAmount,
          round: row.round,
          counted: true,
          dispensaryKoteraUuid: row?.dispensaryKoteraUuid,
        });
        row.isSubmitted = true;
        updateRowResult.value(row);
      }
      pending.value = pending.value.filter((id) => id !== row.storeUuid);
      toasted(res.success, "Successfully Kotera Counted", res.error);
    }
  );
}

const updateRowResult = computed(() => {
  return (row) => {
    if (
      !row.counted ||
      row.totalAmount == null ||
      row.countedStoreAmount == null
    ) {
			row.counted = true
      return;
    }

    if (row.totalAmount == row.countedStoreAmount) {
      return { text: "Correct", isNegative: false };
    } else {
      const difference = row.countedStoreAmount - row.totalAmount;
      console.log(difference);

      return {
        text: difference > 0 ? `+${difference}` : `${difference}`,
        isNegative: difference < 0,
      };
    }
  };
});

const thisValue = computed(() => {
  return (id) => koteraStore.kotera.find(el.inventory);
});
</script>
<template>
  <tbody>
    <template :key="row.id" v-for="(row, index) in rows">
      <tr
        class="cursor-pointer hover:bg-gray-300 border-x-[0.2px] border-b-[0.2px] border-t-[0.2px]"
      >
        <td class="p-2">{{ index + 1 }}</td>
        <td class="p-2 max-w-40" :key="key" v-for="key in rowKeys">
          <span v-if="!Object.keys(cells || {}) || !cells?.[key]">
            {{
              key.split(".").reduce((all, el) => {
                return all?.[el];
              }, row)
            }}
          </span>
          <component
            v-else-if="Object.keys(cells || {}) && cells[key].__hmrId"
            :row="row"
            :is="cells[key]"
          />
          <span v-else-if="typeof cells[key] == 'function'">
            {{ cells[key](row?.[key], row) }}
          </span>
        </td>
        <td class="p-3 flex items-center gap-3">
          <Form id="kotera-form">
            <div
              class="w-32 h-9 flex justify-between md:gap-2 items-center px-1 py-1 rounded border-[#C2C2C2] border"
            >
              <ActualQuantityInput
                :name="row.drugName"
                v-model="row.countedStoreAmount"
                validation="required"
                :attributes="{}"
              />
              <button
                @click.prevent="submitKoteraCheck(row)"
                class="h-7 w-12 rounded-sm px-2 py-1 bg-primary text-white disabled:bg-gray-400 disabled:cursor-not-allowed"
                :disabled="pending.includes(row.storeUuid)"
              >
                <span v-if="pending.includes(row.storeUuid)">...</span>
                <span v-else>Done</span>
              </button>
            </div>
          </Form>
        </td>
        <td
          class="text-center font-bold text-lg"
          :class="{
            'text-red-500': updateRowResult(row)?.isNegative,
            'text-primary': !updateRowResult(row)?.isNegative,
          }"
        >
          {{
            updateRowResult(row)?.text ||
            (row.totalAmount && row.countedStoreAmount ? "—" : "")
          }}
        </td>
      </tr>
    </template>
  </tbody>
</template>
