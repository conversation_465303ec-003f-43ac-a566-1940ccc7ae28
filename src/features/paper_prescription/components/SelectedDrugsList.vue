<script setup>
import Button from "@/components/Button.vue";
import Icon from "@/components/Icon.vue";
import { usePaperPrescriptionStore } from "@paper_prescription/store/paperPrescriptionStore";
import { computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import DrugCard from "./DrugCard.vue";
import { formatCurrency } from "@/utils/utils";

const props = defineProps({
  active: {
    type: [String, Number],
  },
  showDetail: Boolean,
  remove: {
    type: Boolean,
    default: true,
  },
  actions: {
    type: Boolean,
    default: true,
  },
  onSearch: {
    type: Function,
    default: (f) => f,
  },
  onDrugSelected: {
    type: Function,
    default: (f) => f,
  },
  onD2D: {
    type: Function,
    default: (f) => f,
  },
});
const emit = defineEmits(["drug:selected"]);
const router = useRouter();
const paperPrescriptionStore = usePaperPrescriptionStore();

function goToDetail() {
  if (paperPrescriptionStore.selectedDrugList.length) {
    router.push("/paper_prescription/detail");
  }
}

const detail = computed(
  () => (id) =>
    paperPrescriptionStore.priscripprescriptionDetail.find(
      (el) => el?.inventoryUuid == id
    )
);

const sum = computed(() => {
  const filteredDrugs =
    paperPrescriptionStore.paper_prescription.prescriptionDetail.filter(
      (drug) =>
        drug?.drug?.totalQuantity < drug?.drug?.totalAmount ||
        drug?.drug?.totalQuantity < drug?.drug?.inventoryQuantity
    );

  const res = filteredDrugs.reduce((sum, drug) => {
    return (sum +=
      (drug?.drug?.sellingUnitPrice || drug?.drug?.list_price) *
      drug?.drug?.totalQuantity);
  }, 0);
  return paperPrescriptionStore.paper_prescription?.totalPrice || paperPrescriptionStore.selectedDrugList.reduce((sum, el) => {
    return (sum +=
      (el?.sellingUnitPrice || el?.list_price || 0) *
      (detail.value(el?.inventoryUuid)?.totalQuantity || 0));
  }, 0) ||  res;
});
</script>
<template>
  <div class="bg-base-clr-2 w-full h-full flex flex-col gap-6 scroll-y">
    <div
      class="text-base-clr-2 flex justify-between px-7 items-center min-h-drug-search-height bg-primary"
    >
      <p class="font-medium">Prescription List</p>
      <div class="flex items-center gap-4">
        <button class="bg-primary px-2 py-1 rounded" @click.stop="onD2D">
          D2D
        </button>
        <button @click.stop="onSearch">
          <Icon color="rgb(var(--base-clr-2))" name="mynaui:search" />
        </button>
      </div>
    </div>
    <div
      v-if="!paperPrescriptionStore.selectedDrugList.length"
      class="flex-1 flex flex-col text-center items-center gap-4"
    >
      <img src="/src/assets/img/med_list.svg" />
      <div class="p-4 px-14 flex flex-col gap-4">
        <p class="font-bold text-md">Your list is empty.</p>
        <p class="text-xs">
          Note: you can search and add drugs to the list from the search
          section.
        </p>
      </div>
    </div>
    <div
      v-else
      :style="{
        height: 'calc(100% - 4rem - 2rem)',
      }"
      class="flex h-full text-sm flex-col gap-4"
    >
      <div
        class="show-scrollbar h-full pt-2 flex flex-col gap-4 px-4 overflow-y-auto"
      >
        <TransitionGroup name="list">
          <DrugCard
            :remove="remove"
            @click="onDrugSelected(drug)"
            :num="idx + 1"
            :drug="drug"
            :detail="detail(drug.inventoryUuid)"
            :active="active"
            :on-remove="paperPrescriptionStore.removeDrug"
            :key="drug.inventoryUuid"
            v-for="(drug, idx) in paperPrescriptionStore.selectedDrugList"
          />
          <!-- <Button
            class="flex flex-shrink-0 border-b !p-2 transition-all border duration-75 justify-start gap-3 cursor-pointer"
            :key="drug.inventoryUuid"
            v-for="(drug, idx) in paperPrescriptionStore.selectedDrugList"
          >
            <div class="">
              {{ idx + 1 }}
            </div>
            <div class="w-full flex justify-between">
              <div class="flex-1 flex text-left flex-col items-start">
                <p>{{ ` ${drug.product_name}` }}</p>
                <p class="text-xs text-error">
                  {{ formatCurrency(drug.list_price) }}
                </p>
                <div
                  v-if="!detail(drug.inventoryUuid)"
                  class="italic text-xs mt-2 font-normal flex items-center"
                >
                  <span>.</span> No Details Added
                </div>
                <div v-else class="px-4 mt-2">
                  <ul class="list-disc text-xs flex flex-col gap-1">
                    <li>{{ detail(drug.inventoryUuid)?.drug?.route }}</li>
                    <li>{{ detail(drug.inventoryUuid)?.drug?.dose }}</li>
                    <li>{{ detail(drug.inventoryUuid)?.drug?.frequency }}</li>
                    <li>{{ detail(drug.inventoryUuid)?.drug?.totalQuantity }}</li>
                  </ul>
                </div>
              </div>
              <div class="">
                <button
                  v-if="remove"
                  @click.stop="
                    paperPrescriptionStore.removeDrug(drug.inventoryUuid)
                  "
                >
                  <Icon
                    :color="'rgb(var(--error))'"
                    name="solar:trash-bin-trash-outline"
                  />
                </button>
              </div>
            </div>
          </Button> -->
        </TransitionGroup>
        <div v-if="sum" class="mt-5">
          <Button class="flex cursor-pointer font-bold text-xl">
            <div class="">
              <p class="">Total Price: {{ formatCurrency(sum) }}</p>
            </div>
          </Button>
        </div>
      </div>
      <div
        v-if="actions"
        class="min-h-12 px-4 *:flex-1 gap-4 flex items-center"
      >
        <slot>
          <Button
            @click="paperPrescriptionStore.clearDrugList"
            class="text-error border-error border"
          >
            Clear
          </Button>
          <Button @click="goToDetail" type="secondary"> Continue </Button>
        </slot>
      </div>
    </div>
  </div>
</template>

<style>
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.edit {
  text-shadow: 2px 2px 5px rgb(54, 53, 53);
}
</style>
