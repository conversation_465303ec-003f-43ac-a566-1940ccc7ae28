<script setup>
import { Form, Input, Select } from "@components/new_form_elements";
import { ref, watch, watchEffect } from "vue";

const props = defineProps({
  addVitals: Function,
  patient: Object,
});

const bmi = ref(0);
const vitals = ref(props.patient?.vitals?.[0])

const bmiinput = ref({
  weight: vitals.value?.weight ?? 0,
  height: vitals.value?.height ?? 0,
});

watchEffect(() => {
  const meter = parseFloat(bmiinput.value.height) / 100
  bmi.value = Math.round(parseFloat(bmiinput.value.weight) / Math.pow(meter, 2))
});

watch(() => props.patient, () => {
  bmiinput.value ={
    weight: vitals.value?.weight ?? 0,
    height: vitals.value?.height ?? 0,
  }
})
</script>
<template>
  <Form :inner="false" id="vitals" class="flex flex-col gap-4">
    <div class="grid grid-cols-3 gap-4">
      <Input
        name="temperature"
        label="Temperature (C)"
        :attributes="{
          placeholder: 'Eg. 37',
        }"
        validation="num"
        :value="vitals?.temperature || ''"
      />
      <Input
        name="bloodPressure"
        label="Blood pressure"
        :attributes="{
          placeholder: 'Eg. 97',
          
        }"
        validation="alpha2"
        :value="vitals?.bloodPressure || ''"
      />
      <Input
        name="bloodGlucoseLevel"
        label="Blood glucose level"
        :attributes="{
          placeholder: 'Eg. 200',
          
        }"
        validation="num"
        :value="vitals?.bloodGlucoseLevel || ''"
      />
      <Input
        name="height"
        label="Height (Centimeters)"
        :attributes="{
          placeholder: 'Eg (181)',
          
        }"
        validation="num"
        v-model="bmiinput.height"
      />
      <Input
        name="weight"
        label="Weight (Kilograms)"
        :attributes="{
          placeholder: 'Eg. 65',
          
        }"
        validation="num"
        v-model="bmiinput.weight"
      />
      <Input
        name="bmi"
        label="BMI"
        :attributes="{
          placeholder: 'BMI',
          disabled: true,
        }"
        validation="num"
        v-model="bmi"
      />
    </div>
    <div class="grid grid-cols-2 gap-4">
      <Input
        name="pulseRate"
        label="Pulse rate"
        :attributes="{
          placeholder: 'Eg. 72',
        }"
        validation="num"
        :value="vitals?.pulseRate || ''"
      />
      <Input
        name="allergies"
        label="Allergies and/or ADRs"
        :attributes="{
          placeholder: 'Allergies and/or ADRs',
          
        }"
        :value="vitals?.allergies || ''"
      />
      <Input
        name="adherence"
        label="Adherence and medication history"
        :attributes="{
          placeholder: 'Adherence and medication history',
          
        }"
        :value="vitals?.adherence || ''"
      />
      <Input
        name="immunization"
        label="Immunization"
        :attributes="{
          placeholder: 'Immunization'
        }"
        :value="vitals?.immunization || ''"
      />
    </div>
    <slot></slot>
  </Form>
</template>
