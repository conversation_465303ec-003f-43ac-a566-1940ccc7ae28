<script setup>
import Button from "@/components/Button.vue";
import {
  addDayToDate,
  formatDateToYYMMDD,
  formatNumber,
  FREQUENCY,
  getRoutes,
  instructions,
  routes,
  routesAndDosageForms,
  toasted,
} from "@/utils/utils";
import { Input, Form, Select, Textarea } from "@components/new_form_elements";
import { computed, ref, watch, watchEffect } from "vue";
import { usePaperPrescriptionStore } from "@paper_prescription/store/paperPrescriptionStore";
import { openModal } from "@customizer/modal-x";
import { useSearcedDrugs } from "../../store/selectedStore";
import { useApiRequest } from "@/composables/useApiRequest";
import { getNextBatch } from "../../api/paperPrescriptionApi";

const paperPrescriptionStore = usePaperPrescriptionStore();
const searchesDrug = useSearcedDrugs();

const props = defineProps({
  prescription: Object,
  onSubmit: Function,
});

const prescription = ref(props.prescription || {});
function submitForm({ values, reset }) {
  props.onSubmit(values, reset);
}

const totalQuantity = ref({
  frequency: props.prescription?.frequency || "1 Day/s",
  dose: props.prescription?.dose || 0,
  duration: props.prescription?.duration || 0,
});

const calculatable = computed(() => {
  return ["tablet", "capsule"].includes(
    paperPrescriptionStore.activeDrug.dosageForm.toLowerCase()
  );
});

const quantity = ref(0);
watchEffect(() => {
  quantity.value = !calculatable.value
    ? 1
    : Math.floor(
        parseFloat(totalQuantity.value.frequency.split(" ")?.[0] || "0") *
          totalQuantity.value.dose *
          totalQuantity.value.duration
      );
});

watch(
  () => props.prescription,
  () => {
    totalQuantity.value = {
      frequency: props.prescription?.frequency || "1 Day/s",
      dose: props.prescription?.dose || 0,
      duration: props.prescription?.duration || 0,
    };
  }
);

const refill = ref();
const activeFor = ref(30);
const endDate = ref("");

watch(
  () => paperPrescriptionStore.activeDrug,
  () => {
    const detail = paperPrescriptionStore.priscripprescriptionDetail.find(
      (item) =>
        item.inventoryUuid == paperPrescriptionStore.activeDrug.inventoryUuid
    );
    refill.value = detail?.drug?.activeFor > 0;
    activeFor.value = detail?.drug?.activeFor || 30;
  },
  { immediate: true }
);

watch(
  () => activeFor.value,
  () => {
    const res = addDayToDate(new Date(), parseInt(activeFor.value || ""));
    endDate.value = formatDateToYYMMDD(res);
  }
);

// watch(activeFor, () => {
//   console.log(endDate.value)
//   const res = addDayToDate(new Date(), parseInt(activeFor.value))
//   console.log(res, activeFor.value)
//   endDate.value = formatDateToYYMMDD(res)
// }, { deep: true })

const priscriptionDetail = ref({
  route: props.prescription?.route || "Oral",
  instruction: props.prescription?.instruction || "",
});

watch(
  () => paperPrescriptionStore.activeDrug,
  () => {
    console.log(paperPrescriptionStore.getPrescriptionDetail, "here");
    prescription.value = paperPrescriptionStore.getPrescriptionDetail?.drug;
    priscriptionDetail.value.route = prescription.value?.route || "Oral";
    priscriptionDetail.value.instruction =
      prescription.value?.instruction || "";
  },
  { deep: true }
);

const nextBatchApi = useApiRequest();
function getAmountFromTheNextBatch(id, quantity) {
  if (nextBatchApi.pending.value) return;

  const idx = paperPrescriptionStore.paper_prescription.list.findIndex(
    (el) =>
      el.drugName == id.split(" ")?.[0] && el.strength == id.split(" ")?.[1]
  );
  nextBatchApi.send(
    () => getNextBatch(id),
    (res) => {
      if (res.success) {
        if (idx > -1) {
          paperPrescriptionStore.addToList(res.data, true, idx);
        } else {
          paperPrescriptionStore.addToList(res.data, true);
        }
      } else {
        toasted(false, "", res.error);
        paperPrescriptionStore.paper_prescription.forPaper.push({
          ...paperPrescriptionStore.paper_prescription.list[idx],
          overAmount: quantity,
        });
      }
    }
  );
}
</script>
<template>
  <div
    :key="paperPrescriptionStore.activeDrug?.inventoryUuid"
    class="px-4 py-8 bg-base-clr-2"
  >
    <Form v-slot="{ submit }" id="prescription-detail">
      <div class="grid grid-cols-2 gap-4">
        <!-- {{ priscriptionDetail.route }} -->
        <Select
          v-model="priscriptionDetail.route"
          validation="required"
          label="Routes"
          :attributes="{ placeholder: 'Routes', type: 'text' }"
          :options="getRoutes(paperPrescriptionStore.activeDrug?.dosageForm)"
          name="route"
        />
        <Input
          v-model="totalQuantity.dose"
          label="dose"
          :attributes="{ placeholder: 'Dose' }"
          name="dose"
          validation="required|num"
        />
        <Select
          v-model="totalQuantity.frequency"
          :obj="true"
          label="Frequency"
          name="frequency"
          validation="required"
          :options="
            Object.values(FREQUENCY).map((el) => ({
              label: `${el.label}`,
              value: `${el.values}`,
            }))
          "
          :attributes="{
            placeholder: 'Frequency',
            type: 'text',
          }"
        />
        <Input
          v-model="totalQuantity.duration"
          label="Duration(Days)"
          name="duration"
          validation="required|num"
          :attributes="{
            placeholder: 'Duration',
            type: 'text',
          }"
        />
        <Input
          :autoValidate="true"
          v-model="quantity"
          name="totalQuantity"
          :label="`Total Quantity - SOH (${
            paperPrescriptionStore.activeDrug?.inventoryQuantity ??
            paperPrescriptionStore.activeDrug?.totalAmount ??
            0
          })`"
          :validation="{
            num_max: {
              args:
                paperPrescriptionStore.activeDrug?.inventoryQuantity ??
                paperPrescriptionStore.activeDrug?.totalAmount ??
                0,
              message: '',
            },
          }"
          :attributes="{
            placeholder: 'Total Quantity',
            type: 'text'
          }"
        />
        <Select
          v-model="priscriptionDetail.instruction"
          label="Instruction"
          name="instruction"
          :options="instructions"
          :attributes="{
            placeholder: 'Instruction',
            type: 'text',
          }"
        />
        <div
          v-if="quantity > paperPrescriptionStore.activeDrug?.totalAmount"
          class="col-span-2 gap-2 grid grid-cols-2 leading-none text-sm -mt-2 px-2 rounded-xl italic py-2"
        >
          <p>
            <span class="text-red-500 col-span-2"
              >Stock limit exceeded - maximum available:
              {{
                formatNumber(paperPrescriptionStore.activeDrug?.totalAmount)
              }}</span
            >
          </p>
          <div class="flex items-center gap-2 col-span-2">
            would you like to add from the next batch?
            <Button
              @click.prevent="
                getAmountFromTheNextBatch(
                  `${paperPrescriptionStore.activeDrug?.drugName} ${paperPrescriptionStore.activeDrug?.strength}`,
                  paperPrescriptionStore.activeDrug?.totalAmount - quantity
                )
              "
              size="xs"
              :pending="nextBatchApi.pending.value"
              class="px-4 py-2 bg-secondary rounded-md text-white"
            >
              Add
            </Button>
          </div>
        </div>
        <div class="col-span-2 flex flex-col gap-4 border-b py-2">
          <div class="flex items-center gap-2">
            <input
              :checked="refill"
              @change="(ev) => (refill = ev.target.checked)"
              type="checkbox"
              class="size-5"
            />
            <p>Refill</p>
          </div>
          <div v-if="refill" class="grid grid-cols-2 gap-4">
            <Select
              v-model="activeFor"
              :obj="true"
              label="Active For"
              name="activeFor"
              :attributes="{
                type: 'text',
                placeholder: 'Select Days',
              }"
              :options="[
                {
                  label: '30 Days',
                  value: 30,
                },
                {
                  label: '60 Days',
                  value: 60,
                },
                {
                  label: '90 Days',
                  value: 90,
                },
              ]"
              validation="required"
            />
            <div>
              <Input
                label="Refill End Date"
                name="refillEndDate"
                :attributes="{
                  type: 'date',
                  disabled: true,
                }"
                v-model="endDate"
              />
            </div>
          </div>
        </div>
        <Textarea
          class="col-span-2"
          :value="prescription?.diagnosisDetail || ''"
          name="diagnosisDetail"
          label="Diagnosis detail"
          :attributes="{
            placeholder: 'Diagnosis detail',
          }"
        />
        <Textarea
          class="col-span-2"
          :value="prescription?.chiefComplaint || ''"
          name="chiefComplaint"
          label="Chief complaint"
          :attributes="{
            placeholder: 'Chief complaint',
          }"
        />
      </div>
      <div class="py-3 border-t mt-2 flex items-center gap-2 justify-end">
        <Button @click.prevent="submit(submitForm)" type="primary">
          Continue
        </Button>
        <Button
          @click.prevent="
            () => {
              // Filter drugs where quantity is less than inventoryQuantity
              const filteredDrugs =
                paperPrescriptionStore.paper_prescription.prescriptionDetail.filter(
                  (drug) => drug?.drug?.totalQuantity > drug?.drug?.totalAmount
                );

              console.log(filteredDrugs);
              const filteredDrugDetails =
                paperPrescriptionStore.priscripprescriptionDetail.filter(
                  (detail) =>
                    filteredDrugs.some(
                      (drug) => drug.inventoryUuid === detail.drug.inventoryUuid
                    )
                );
              console.log(filteredDrugDetails);

              paperPrescriptionStore.setPrescriptionDetail({
                ...priscriptionDetail,
                ...totalQuantity,
                totalQuantity: quantity,
              });
              openModal('PaperPrescription', {
                drugs: filteredDrugs,
                patient: paperPrescriptionStore.selectedPatient,
                drugDetail: filteredDrugDetails,
              });
            }
          "
          v-if="
            paperPrescriptionStore.paper_prescription.list.length ==
              paperPrescriptionStore.paper_prescription.prescriptionDetail
                .length &&
            paperPrescriptionStore.paper_prescription.forPaper?.length
          "
          type="secondary"
        >
          Paper Prescription
        </Button>
        <slot name="more-actions" />
      </div>
    </Form>
  </div>
</template>
