import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const api = new ApiService();

const path = "/patients";

export function searchForPatient(query = {}, config) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path}/getPatients${qr}`, config);
}

export function addPatient(data) {
  return api.addAuthenticationHeader().post(`${path}/addPatient`, data);
}

export function updatePatient(id, data) {
  return api.addAuthenticationHeader().put(`${path}/updatePatient/${id}`, data);
}

export function getPatientHistory(id) {
  return api.addAuthenticationHeader().get(`${path}/patientHistory/${id}`);
}
