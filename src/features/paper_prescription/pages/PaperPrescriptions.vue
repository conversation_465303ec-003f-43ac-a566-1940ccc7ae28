<script setup>
import Button from "@/components/Button.vue";
import { useApiRequest } from "@/composables/useApiRequest";
// import SelectedDrugsList from "../components/SelectedDrugsList.vue";
import {
  checkDrugAvailability,
  checkMedicineAvailability,
} from "../api/paperPrescriptionApi";
import { usePagination } from "@/composables/usePagination";
import Table from "@/components/Table.vue";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import PaperPrescriptionStatusCell from "@paper_prescription/components/PaperPrescriptionStatusCell.vue";
import { formatCurrency, routesAndDosageForms } from "@/utils/utils";
import { usePaperPrescriptionStore } from "@paper_prescription/store/paperPrescriptionStore";
import { useAuth } from "@/stores/auth";
import { onMounted, ref } from "vue";
import LayeredPages from "@components/LayeredPages.vue";
import SearchSelectedDrugs from "../components/SearchSelectedDrugs.vue";
import SelectedDrugListPage from "../components/SelectedDrugListPage.vue";
import D2DInteractionPage from "../components/D2DInteractionPage.vue";
import { useSearcedDrugs } from "../store/selectedStore";
import { usePaginationTemp } from "@/composables/usePaginaionTemp";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
// import Form from '@/component/new_form_elements/Form.vue'
// import { Input } from '@/component/new_form_elements'

const auth = useAuth();
const searchesDrug = useSearcedDrugs();

const paperPrescriptionStore = usePaperPrescriptionStore();

const pagination = usePaginationcopy({
  auto: false,
  store: searchesDrug,
  cb: checkDrugAvailability,
});

const search = ref("");
const showLayeredPages = ref(false);

let to;
function fetchDrugs(ev) {
  if (to) clearTimeout(to);
  to = setTimeout(() => {
    pagination.search.value = search.value;
  }, 300);
}

function addMed(order) {
  delete order.Contraindication;
  delete order.description;

  paperPrescriptionStore.addToList(order);
}

onMounted(() => {
  if (paperPrescriptionStore.done) {
    paperPrescriptionStore.reset();
  }
});
</script>
<template>
  <div class="flex flex-1 h-full gap-5 overflow-hidden">
    <div class="flex-1 flex justify-center h-full overflow-hidden">
      <div class="flex flex-col gap-4 w-[90%] h-full">
        <div
          class="flex justify-between md:gap-2 items-center px-2 rounded border-primary border-2 min-h-drug-search-height"
        >
          <input
            v-model="search"
            v-focus
            @input="fetchDrugs"
            class="px-2 bg-transparent flex-1 h-12"
            placeholder="Search Drug"
          />
          <!-- <select class="text-xs h-10 px-3 rounded-md min-w-[5rem] max-h-[5rem]" >
            <option :key="option" v-for="option in [...routesAndDosageForms.reduce((state, el) => {
              el.dosageForm.forEach((el) => {
                state.add(el);
              })
              return state
            } , new Set([]))]" >{{option}}</option>
          </select> -->
          <div></div>
          <Button @click="fetchDrugs" size="md" type="primary"> Search </Button>
        </div>
        <div
          v-if="!pagination.pending.value && !searchesDrug.drugs?.length"
          class="flex-1 flex flex-col gap-12 items-center"
        >
          <!-- <Form id="age" >
            <Input
              name="age"
            />
          </Form> -->
          <img src="/src/assets/img/search_med.svg" />
          <div class="flex flex-col gap-4 items-center">
            <p class="font-medium text-md">
              Please search medicine availability and price.
            </p>
            <p class="text-xs px-20">
              Note: you can search a medicine using medicine or generic name.
            </p>
          </div>
        </div>
        <div v-else class="overflow-auto show-scrollbar">
          <Table
            @row="
              (row) => {
                row.totalAmount > 0 && addMed(row);
              }
            "
            :pending="pagination.pending.value"
            :headers="{
              head: [
                'Medicine Name',
                'Unit',
                'Drug Brand',
                'Selling Price',
                'Quantity',
                'totalAmount',
                'actions',
              ],
              row: [
                'drugName',
                'unit',
                'drugBrandName',
                // 'unitPrice',
                'sellingUnitPrice',
                // 'quantity',
                'totalAmount',
                'totalAmount',

              ],
            }"
            :rows="searchesDrug.drugs.length ? searchesDrug.drugs : []"
            :cells="{
              drugBrandName: (name) => {
                return name == 'NA' ? '' : name;
              },
              unitPrice: (_, row) => {
                const price = row.sellingUnitPrice;
                return formatCurrency(price - (price * 0.2).toFixed(5));
              },
              // status: PaperPrescriptionStatusCell,
              sellingUnitPrice: (price) => {
                return `ብር ${price}`;
              },
            }"
          >
            <template #actions="{ row }">
              <Button
                v-if="row.totalAmount > 0"
                @click.stop="addMed(row)"
                class="text-sm"
                size="xs"
                :type="row.totalAmount > 0 ? 'secondary' : 'primary'"
              >
                Select
              </Button>
            </template>
          </Table>
        </div>
      </div>
    </div>
    <!-- Right Section (LayeredPages) -->
    <div class="">
      <!-- Icon for Small Screens -->
      <div class="md:hidden fixed bottom-4 left-4 z-50">
        <Button
          @click="showLayeredPages = !showLayeredPages"
          size="sm"
          type="secondary"
        >
          <i class="bi bi-list"></i>
          <!-- Replace with your preferred icon -->
        </Button>
      </div>

      <!-- LayeredPages -->
      <div
        :class="[
          'w-[22rem] h-full bg-base-clr-2 transition-transform duration-300 ease-in-out',
          { 'hidden md:block': !showLayeredPages, block: showLayeredPages },
        ]"
      >
        <LayeredPages
          transitionName="search"
          :baseAnimation="false"
          :pages="[
            {
              name: 'list',
              component: SelectedDrugListPage,
              props: {
                class: 'rounded-tl',
              },
            },
            {
              name: 'search',
              component: SearchSelectedDrugs,
            },
            {
              name: 'd2d',
              component: D2DInteractionPage,
            },
          ]"
        />
      </div>
    </div>

    <!-- <div class="w-[22rem] h-full bg-base-clr-2">
      <LayeredPages transitionName="search" :baseAnimation="false" :pages="[
        {
          name: 'list',
          component: SelectedDrugListPage,
          props: {
            class: 'rounded-tl',
          }
        },
        {
          name: 'search',
          component: SearchSelectedDrugs,
        },
        {
          name: 'd2d',
          component: D2DInteractionPage,
        }
      ]" />
    </div> -->
  </div>
</template>
<style>
.search-enter-active,
.search-leave-active {
  transition: transform, opacity 0.2s ease;
}

.search-enter-from {
  transform: translate(100%, 0);
}

.search-leave-to {
  opacity: 0;
}
</style>
