<script setup>
import BaseIcon from "@/components/base/BaseIcon.vue";
import Button from "@/components/Button.vue";
import Input from "@/components/new_form_elements/Input.vue";
import Select from "@/components/new_form_elements/Select.vue";
import Table from "@/components/Table.vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import { formatCurrency, secondDateFormat, openNewTab } from "@/utils/utils";
import { mdiMagnify, mdiFilter } from "@mdi/js";
import { computed, ref, watch } from "vue";
import Form from "@/new_form_builder/Form.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { useRouter } from "vue-router";

// Create a store for the kotera history
import { useKoteraHistoryStore } from "@/features/StoreKeeper/store/KoteraHistoryStore";
import { dispensaryKoteraHistory } from "@/features/StoreKeeper/api/dispensaryKoteraApi";

const router = useRouter();
const koteraHistoryStore = useKoteraHistoryStore();
const search = ref("");
const selectedMonth = ref(new Date().getMonth() + 1);
const selectedYear = ref(new Date().getFullYear());
const req = useApiRequest();
const baseURL = import.meta.env?.VITE_BASE_URL || "";

// Generate months for dropdown
const months = [
  { value: 1, label: "January" },
  { value: 2, label: "February" },
  { value: 3, label: "March" },
  { value: 4, label: "April" },
  { value: 5, label: "May" },
  { value: 6, label: "June" },
  { value: 7, label: "July" },
  { value: 8, label: "August" },
  { value: 9, label: "September" },
  { value: 10, label: "October" },
  { value: 11, label: "November" },
  { value: 12, label: "December" },
];

// Generate years for dropdown (current year down to 2000)
const years = computed(() => {
  const currentYear = new Date().getFullYear();
  const yearList = [];
  for (let year = currentYear; year >= 2000; year--) {
    yearList.push(year);
  }
  return yearList;
});

// Set up pagination with the getKoteraHistory API
const pagination = usePaginationcopy({
  store: koteraHistoryStore,
  cb: (params) =>
    dispensaryKoteraHistory({
      ...params,
      search: search.value,
    }),
});

// Watch for changes in filters and refresh data
watch([selectedMonth, selectedYear], () => {
  fetchHistory();
});

// Function to fetch history data
function fetchHistory() {
  pagination.send();
}

// Initial data load
fetchHistory();
</script>

<template>
  <div class="h-full w-full flex flex-col gap-4 p-6">
    <!-- Header with search and filters -->
    <div class="flex justify-between items-center py-4">
      <h1 class="text-2xl font-semibold">Dispensary Kotera History</h1>
    </div>

    <!-- Search and filters -->
    <div class="flex flex-wrap gap-4 mb-4">
      <Form id="koteraHistorySearch" class="flex flex-1 gap-4">
        <div class="flex-1">
          <Input
            name="search"
            v-model="search"
            :attributes="{
              placeholder: 'Search by drug name or batch number',
            }"
          />
        </div>

        <div class="w-48">
          <Select
            name="month"
            v-model="selectedMonth"
            :options="months"
            :obj="true"
            :attributes="{
              placeholder: 'All Months',
            }"
          />
        </div>

        <div class="w-48">
          <Select
            name="year"
            v-model="selectedYear"
            :options="years"
            :attributes="{
              placeholder: 'Select Year',
            }"
          />
        </div>

        <Button
          size="sm"
          @click.prevent="fetchHistory"
          type="primary"
          class="self-end"
        >
          <div v-if="req.pending.value">
            <i class="animate-spin">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
              </svg>
            </i>
          </div>
          <div v-else class="flex items-center gap-2">
            <BaseIcon :path="mdiFilter" :size="20" />
            Filter
          </div>
        </Button>
      </Form>
    </div>

    <Table
      :pending="pagination.pending.value"
      :headers="{
        head: ['Batch Name', 'Status', 'Actions'],
        row: ['batchName', 'batchStatus'],
      }"
      :rows="koteraHistoryStore.history || []"
      :cells="{
        countDate: secondDateFormat,
        status: (status) => {
          return status;
        },
      }"
      :Fallback="TableRowSkeleton"
    >
      <template #actions="{ row }">
        <Button
          @click="
            openNewTab(`${baseURL}/dispensary-kotera-history/${row?.batchUuid}`)
          "
          size="xs"
          type="edge"
          >PDF</Button
        >
      </template>
    </Table>
  </div>
</template>
