<script setup>
import Input from "@/components/new_form_elements/Input.vue";
import Select from "@/components/new_form_elements/Select.vue";
import Table from "@/components/Table.vue";
import { usePagination } from "@/composables/usePagination";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import icons from "@/utils/icons";
import { computed, provide, ref, watch } from "vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import { useApiRequest } from "@/composables/useApiRequest";
import { formatCurrency, removeUndefined } from "@/utils/utils";
import DispensaryKoteraTableRow from "../components/DispensaryKoteraTableRow.vue";
import { useKoteraStore } from "@/features/StoreKeeper/store/koteraStore";
import { getAllDispensaryKotera, getAllStoreDrugs } from "@/features/StoreKeeper/api/InventoryApi";
import Button from "@/components/Button.vue";
import Form from "@/new_form_builder/Form.vue";
import { useRouter } from "vue-router";

const router = useRouter();
const koteraStore = useKoteraStore();
const type = ref('')

provide('koteraType', type)
const pagination = usePaginationcopy({
  watch: [type],
  store: koteraStore,
  cb: (data) =>
    getAllStoreDrugs({
      ...data,
    }),
});

const req = useApiRequest();
function fetchDrugs() {
  pagination.send();
}
</script>

<template>
  <div class="h-full w-full flex flex-col bg-white gap-9 px-[124px] mt-7 py-6">
    <Form id="store-kotera" class="grid grid-cols-5 gap-9">
      <div class="col-span-3">
        <Input
          name="search"
          v-model="pagination.search.value"
          label="Search"
          focus
          :attributes="{
            placeholder: 'Serach Drug',
          }"
        />
      </div>
      <!-- <Select
        class="w-full"
        v-model="type"
        label="Type"
        name="type"
        :attributes="{
          type: 'text',
          placeholder: 'Select Type',
        }"search
        :options="['Quarter', 'Perpetual']"
        validation="required"
      /> -->
      <Button
        class="mt-auto"
        size="sm"
        @click.prevent="fetchDrugs"
        type="primary"
      >
        <div v-if="req.pending.value">
          <i v-html="icons.spinner" />
        </div>
        <div v-if="!req.pending.value">Search Drug</div>
      </Button>
      <div class="mt-auto pl-4 border-l">
        <Button
          type="edge"
          size="sm"
          @click="router.push('/store-kotera-history')"
        >
          History
        </Button>
      </div>
    </Form>
    <hr />
    <div class="rounded-lg p-4 gap-4 flex flex-1 flex-col bg-[#F1F8F7]">
      <div class="flex justify-between items-center">
        <p class="font-bold text-sm opacity-65">Added Items</p>
        <div
          class="w-[22rem] p-4 rounded border border-[#A4A4A4] bg-white flex items-center gap-4"
        >
          <i class="opacity-65" v-html="icons.search" />
          <input
            class="placeholder:opacity-65"
            placeholder="Search Added Items "
          />
        </div>
      </div>
      <div class="bg-white">
        <Table
          :lastCol="true"
          :pending="pagination.pending.value"
          :headers="{
            head: [
              'Drug Code',
              'Drug Name',
              'Brand Name',
              'Dosage Form',
              'Strength',
              'Drug Unit',
              'Price',
              'Expiry Date',
              'Quantity',

              'Actual Count',
            ],
            row: [
              'drugCode',
              'drugName',
              'drugBrandName',
              'dosageForm',
              'strength',
              'bulkUnit',
              'unitPrice',
              'expDate',
              'totalAmount',
            ],
          }"
          :rows="koteraStore.kotera || []"
          :cells="{
            strength: (_, row) => {
              return row?.strength
                ? `${row.strength} ${row.unit}`
                : `${row.strength || 'No Strength'}`;
            },
            unitPrice: (_, row) => {
              return formatCurrency(row?.unitPrice);
            },
          }"
          :Fallback="TableRowSkeleton"
          :rowCom="DispensaryKoteraTableRow"
        >
          <template #headerLast>
            <p class="text-center">Kotera Check</p>
          </template>
          <template #lastCol="{ row }"> </template>
        </Table>
      </div>
    </div>
    <div class="flex gap-4">
      <Button type="primary" class="flex-1">
        Generate and Report Kotera
      </Button>
    </div>
  </div>
</template>
