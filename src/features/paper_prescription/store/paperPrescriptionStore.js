import { defineStore } from "pinia";
import { computed, ref, watch } from "vue";

export const usePaperPrescriptionStore = defineStore(
  "paperPrescriptionStore",
  () => {
    const paper_prescription = ref({
      prescriptionUuid: "",
      totalPrice: 0,
      fsNumber: 0,
      list: [],
      patient: null,
      prescriptionDetail: [],
      forPaper: [],
      done: false,
    });

    const edit = ref(false);
    const ePrescription = ref(false);
    function setDone(val) {
      paper_prescription.value.done = !!val;
    }

    const activeDrug = ref();

    function setActiveDrug(drug) {
      activeDrug.value = drug;
    }

    const prescriptionFilled = computed(() => {
      return (
        paper_prescription.value.list?.length ==
        paper_prescription.value.prescriptionDetail?.length
      );
    });

    function setPrescriptionDetail(data, id) {
      const idx = paper_prescription.value.prescriptionDetail.findIndex(
        (el) => el.inventoryUuid == (id || activeDrug.value?.inventoryUuid)
      );

      console.log(idx, data);

      if (idx > -1) {
        const detail = paper_prescription.value.prescriptionDetail[idx];

        console.log(detail);
        
        const frequency = detail.drug?.frequency?.split(" ");
        paper_prescription.value.prescriptionDetail.splice(idx, 1, {
          inventoryUuid: activeDrug.value?.inventoryUuid || id,
          drug: {
            ...paper_prescription.value.prescriptionDetail[idx],
            ...data,
            ...(activeDrug.value || {}),
            dosageForm: "Accurate Dose",
            inventoryUuid: `${activeDrug.value?.inventoryUuid || id}`,
            frequencyUnit: `${frequency[1]}`,
            frequencyRate: `${parseFloat(frequency[0])}`,
          },
        });
      } else {
        console.log(data);

        const frequency = data.frequency.split(" ");
        paper_prescription.value.prescriptionDetail.push({
          inventoryUuid: id || activeDrug.value?.inventoryUuid,
          drug: {
            ...data,
            ...(activeDrug.value || {}),
            dosageForm: "Accurate Dose",
            inventoryUuid: `${id || activeDrug.value?.inventoryUuid}`,
            frequencyUnit: `${frequency[1]}`,
            frequencyRate: `${parseFloat(frequency[0])}`,
          },
        });
      }
    }
    const getPrescriptionDetail = computed(() => {
      return paper_prescription.value?.prescriptionDetail.find(
        (el) => el.inventoryUuid == activeDrug.value?.inventoryUuid
      );
    });

    function getAll() {
      return paper_prescription.value;
    }

    function set(data) {
      console.log(data);
      paper_prescription.value = data;
    }

    function reset() {
      paper_prescription.value = {
        list: [],
        patient: null,
        prescriptionDetail: [],
        done: false,
      };
      edit.value = false;
    }

    const selectedDrugList = computed(() => paper_prescription.value.list);
    const selectedPatient = computed(() => paper_prescription.value.patient);
    const priscripprescriptionDetail = computed(
      () => paper_prescription.value.prescriptionDetail
    );
    const done = computed(() => paper_prescription.value.done);
    const credit = computed(() => {
      return !!selectedPatient.value?.institutionName;
    });

    function addToList(order, push = false, splicIdx = -1) {
      const idx = paper_prescription.value.list.findIndex(
        (el) => el.inventoryUuid == order.inventoryUuid
      );

      if (idx > -1) {
        paper_prescription.value.list.splice(idx, 1, order);
      } else {
        if(splicIdx > -1) {
          const data = paper_prescription.value.list[splicIdx]
          console.log(splicIdx)
          paper_prescription.value.list.splice(splicIdx, 1, data, order)
        } else {
          paper_prescription.value.list[push ? 'push' : 'unshift'](order);
        }
      }
    }

    function setPatient(data) {
      paper_prescription.value.patient = data;
    }
    function update(id, data) {
      console.log(id, data);

      const idx = paper_prescription.value.patient.findIndex(
        (el) => el.userUuid == id
      );
      if (idx == -1) return;

      paper_prescription.value.patient.splice(idx, 1, data);
    }

    function clearDrugList() {
      paper_prescription.value.list = [];
    }

    function removeDrug(id) {
      const idx = paper_prescription.value?.list.findIndex(
        (el) => el.inventoryUuid == id
      );
      if (idx > -1) {
        paper_prescription.value?.list.splice(idx, 1);
      }
    }

    function setData(data, change = false, ePrescrip = false) {
      paper_prescription.value.prescriptionUuid = data?.prescriptionUuid;
      paper_prescription.value.totalPrice = data.totalPrice;
      paper_prescription.value.fsNumber = data?.fsNumber
      paper_prescription.value.list = data.drugPrescriptions.map((el) => ({
        ...el,
        frequency: `${el?.frequency?.frequencyRate} ${el?.frequency?.frequencyUnit}`,
      }));
      paper_prescription.value.patient = data.patient;
      paper_prescription.value.list.forEach((el) => {
        setPrescriptionDetail(el, el?.inventoryUuid);
      });

      if (edit) {
        edit.value = change;
      }

      if (ePrescrip) {
        ePrescription.value = ePrescrip;
      }
    }

    watch(
      selectedDrugList,
      () => {
        if (paper_prescription.value.prescriptionDetail?.length) {
          const idxs = paper_prescription.value.list.map(
            (el) => el.inventoryUuid
          );
          paper_prescription.value.prescriptionDetail =
            paper_prescription.value.prescriptionDetail.filter((el) =>
              idxs.includes(el.inventoryUuid)
            );
        }
      },
      { deep: true }
    );

    return {
      getAll,
      set,
      paper_prescription,
      addToList,
      selectedDrugList,
      removeDrug,
      clearDrugList,
      selectedPatient,
      setPatient,
      activeDrug,
      setActiveDrug,
      getPrescriptionDetail,
      setPrescriptionDetail,
      prescriptionFilled,
      priscripprescriptionDetail,
      reset,
      setDone,
      done,
      setData,
      credit,
      edit,
      ePrescription,
      update,
    };
  }
);
