<script setup>
import Icon from '@components/Icon.vue'
import { ref, watch, computed } from 'vue'
import { usePaperPrescriptionStore } from '@paper_prescription/store/paperPrescriptionStore'
import DrugCard from './DrugCard.vue';

const paperPrescriptionStore = usePaperPrescriptionStore()

const props = defineProps({
  goBack: {
    type: Function,
    required: true
  },
})

const search = ref('')

const filter = computed(() => {
  return paperPrescriptionStore.selectedDrugList.filter(el => el.drugName.toLowerCase().includes(search.value.toLowerCase()))
})

</script>
<template>
  <div class="w-full h-full bg-base-clr-2 flex flex-col">
    <div class="h-drug-search-height flex items-center border-b">
      <button @click="goBack()" class="size-12 grid place-items-center">
        <Icon name="solar:round-arrow-left-outline" />
      </button>
      <div class="flex-1 flex h-full">
        <input v-model="search" v-focus class="h-full flex-1 pr-2" placeholder="Search Form Prescription List" />
      </div>
    </div>
    <div class="show-scrollbar h-full pt-2 flex flex-col gap-2 px-4 overflow-hidden overflow-y-auto">
      <DrugCard :show-detail="false" :drug="drug" :on-remove="paperPrescriptionStore.removeDrug" :key="drug.product_id"
        v-for="drug in filter" />
    </div>
  </div>
</template>