<script setup>
import { usePaperPrescriptionStore } from "@paper_prescription/store/paperPrescriptionStore";
import Button from '@components/Button.vue'
import { getAgeFormDate } from '@/utils/utils'

const props = defineProps({
  setActive: Function,
  actions: {
    type: Boolean,
    default: true
  }
})

const paperPrescriptionStore = usePaperPrescriptionStore();

const selectedUserInfo = [
  {
    label: "phone Number",
    value: "mobilePhone",
  },
  {
    label: "patient Full Name",
    value: "fullName",
    cb(values) {
      return `${values?.firstName ?? ''} ${values?.fatherName ?? ''} ${values?.grandFatherName ?? ''}`;
    },
  },
  {
    label: "Patient Age",
    cb(values) {
      return formatAgeToFraction(values?.age)
    },
  },
  {
    label: "Gender",
    value: "gender",
  },
  {
    label: "Woreda",
    value: "woreda",
  },
  {
    label: "Zone",
    value: "zone",
  },
  {
    label: "Region",
    value: "region",
  },
];
</script>
<template>
  <div class="bg-base-clr-2 border-r h-full flex flex-col gap-4">
    <div class="min-h-drug-search-height font-bold px-6 bg-secondary text-white flex items-center">
      <p>Patient Detail</p>
    </div>
    <div class="flex-1 flex flex-col gap-2 p-2 overflow-auto show-scrollbar">
      <div class="p-3 bg-base-clr-3 rounded flex flex-col gap-1" :key="info.label" v-for="info in selectedUserInfo">
        <p class="capitalize text-dark/70 text-sm">{{ info.label }}</p>
        <p>
          {{
            info.cb
              ? info.cb(paperPrescriptionStore.selectedPatient)
              : paperPrescriptionStore.selectedPatient[info.value]
          }}
        </p>
      </div>
      <template v-if="paperPrescriptionStore.credit">
        <div class="p-3 bg-accent rounded flex flex-col gap-1">
          <p class="capitalize text-dark/70 text-sm">Institution</p>
          <p>
            {{ paperPrescriptionStore.selectedPatient?.institutionName }}
          </p>
        </div>
        <div class="p-3 bg-accent rounded flex flex-col gap-1">
          <p class="capitalize text-dark/70 text-sm">Credit</p>
          <p class="text-primary">Active</p>
        </div>
      </template>
    </div>
    <div v-if="!paperPrescriptionStore.done && actions" class="mt-auto flex items-center gap-2 justify-center p-2">
      <slot></slot>
    </div>
  </div>
</template>
