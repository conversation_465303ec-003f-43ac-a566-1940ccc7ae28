<script setup>
import SelectedDrugsList from "./SelectedDrugsList.vue";
import { usePaperPrescriptionStore } from "@paper_prescription/store/paperPrescriptionStore";
import Button from "@/components/Button.vue";
import { watch } from "vue";

const props = defineProps({
  detail: {
    type: Boolean,
    default: false,
  },
  goTo: Function,
});

function go() {
  props.goTo("search");
}

function goD2D() {
  props.goTo("d2d");
}
const paperPrescriptionStore = usePaperPrescriptionStore();
</script>
<template>
  <SelectedDrugsList :onDrugSelected="paperPrescriptionStore.setActiveDrug"
    :active="paperPrescriptionStore.activeDrug?.inventoryUuid" :on-search="go" :onD2D="goD2D">
    <template v-if="detail" #default>
      <div v-if="!paperPrescriptionStore.done" class="flex items-center gap-2 mt-auto">
        <Button @click="() => {
          paperPrescriptionStore.clearDrugList();
          $router.replace('/paper_prescription');
        }
        " class="border truncate border-error text-error">
          Clear
        </Button>
        <Button @click="$router.replace('/doctor')" class="flex-1 border truncate border-dark">
          Add Drugs
        </Button>
      </div>
      <div v-else></div>
    </template>
  </SelectedDrugsList>
</template>
