<script setup>
  import { ref } from 'vue'
  import { closeModal } from '@customizer/modal-x'
  import Icon from '@components/Icon.vue'
  import Button from '@components/Button.vue'

const type = ref('Cash')
</script>
<template>
  <div class="bg-black/50 h-full grid place-items-center">
    <div class=" rounded bg-white flex flex-col w-[20rem] shadow-md">
      <div class="flex border-b items-center justify-between">
        <p class="p-2 font-bold text-lg m-2">Payment Type</p>
        <button @click="closeModal()" class="w-10">
          <Icon name="solar:close-circle-outline" />
        </button>
      </div>
      <div class="p-2">
        <div class="flex items-center px-4 py-2 gap-2">
          <input @change="type = $event.target.value" :checked="type == 'Cash'" type="checkbox" value="Cash" name="type" />
          <p>Cash</p>
        </div>
        <div class="flex items-center px-4 py-2  gap-2">
          <input @change="type = $event.target.value" :checked="type == 'OnLine'" type="checkbox" value="OnLine" name="type" />
          <p>Online</p>
        </div>
      </div>
      <div class="px-4  p-2 flex justify-end border-t">
        <Button @click.stop="closeModal(type)" type="primary">
          Select
        </Button>
      </div>
    </div>
  </div>
</template>