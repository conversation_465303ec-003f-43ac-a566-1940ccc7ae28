<script setup>
import Button from "@components/Button.vue";
import Icon from "@/components/Icon.vue";
import { formatCurrency } from "@/utils/utils";
import { computed } from "vue";
import { usePaperPrescriptionStore } from "../store/paperPrescriptionStore";
const props = defineProps({
  num: Number,
  drug: Object,
  detail: Object,
  active: {
    type: [String, Boolean, Number],
    default: false,
  },
  remove: {
    type: Boolean,
    default: true,
  },
  onRemove: {
    type: Function,
    default: f => f
  },
  showDetail: {
    type: Boolean,
    default: true
  }
});
console.log(props.drug);

console.log(props.detail);

const paperPrescriptionStore = usePaperPrescriptionStore();

const totalPrice = computed(() => {
  return parseFloat((props?.drug?.sellingUnitPrice || props?.drug?.list_price || 0)) * (props?.detail?.drug?.frequencyRate || 0) * (props?.detail?.drug?.dose || 0) * (props?.detail?.drug?.duration || 0)
})


</script>
<template>
  <div class="flex flex-col gap-3">
    <Button :class="[
      active == drug.inventoryUuid
        ? 'bg-base-clr-3 border-primary'
        : 'border-white/0 ',
    ]" class="flex flex-shrink-0 border-b !p-2 transition-all border duration-75 justify-start gap-3 cursor-pointer">
      <div class="flex flex-col gap-5 w-full">
        <div class="flex gap-4">
          <div class="text-left">
            {{ num || '' }}
          </div>
          <div class="w-full flex justify-between items-center">
            <div class="flex text-left flex-col flex-grow">
              <p>{{ drug.drugName }}</p>
              <p class="text-xs text-error">
                {{ formatCurrency(drug?.sellingUnitPrice || drug?.list_price) }}
              </p>
              <div v-if="!detail && showDetail" class="italic text-xs mt-2 font-normal flex items-center">
                <span>.</span> No Details Added
              </div>
              <div v-else-if="showDetail" class="px-4 mt-2">
                <ul class="list-disc text-xs flex flex-col gap-1">
                  <li>{{ detail?.drug?.route }}</li>
                  <li>{{ detail?.drug?.dose }}</li>
                  <li>{{ detail?.drug?.frequency }}</li>
                  <li>{{ (props?.detail?.drug?.frequencyRate || 0) * (props?.detail?.drug?.dose || 0) *
                    (props?.detail?.drug?.duration || 0) }}</li>
                </ul>
              </div>
            </div>
            <div class="">
              <button v-if="remove" @click.stop="onRemove(drug.inventoryUuid)">
                <Icon :color="'rgb(var(--error))'" name="solar:trash-bin-trash-outline" />
              </button>
            </div>
          </div>
        </div>

        <div v-if="detail">
          <Button
            class="flex flex-col flex-shrink-0 border-b !p-2 transition-all border duration-75 justify-start gap-3 cursor-pointer font-bold">
            <div v-if="showDetail">
              <p>Price: <span class="text-red-500">
                  {{ formatCurrency(totalPrice) }}
                </span></p>
            </div>
          </Button>
        </div>
      </div>
    </Button>
  </div>

</template>
