<script setup>
import Button from "@/components/Button.vue";
import { useApiRequest } from "@/composables/useApiRequest";
// import SelectedDrugsList from "../components/SelectedDrugsList.vue";
import { checkMedicineAvailability } from "../api/paperPrescriptionApi";
import { usePagination } from "@/composables/usePagination";
import Table from "@/components/Table.vue";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import PaperPrescriptionStatusCell from "@paper_prescription/components/PaperPrescriptionStatusCell.vue";
import { formatCurrency } from "@/utils/utils";
import { usePaperPrescriptionStore } from "@paper_prescription/store/paperPrescriptionStore";
import { useAuth } from "@/stores/auth";
import { onMounted, ref } from 'vue'
import LayeredPages from '@components/LayeredPages.vue'
import SearchSelectedDrugs from "../components/SearchSelectedDrugs.vue";
import SelectedDrugListPage from "../components/SelectedDrugListPage.vue";

const auth = useAuth();

const paperPrescriptionStore = usePaperPrescriptionStore();

const pagination = usePagination({
  auto: false,
  cb: (data) =>
    checkMedicineAvailability({
      drugName: data.search,
      stockLocationName: auth?.auth?.user?.stockLocationName,
    }),
});

const search = ref('')
function fetchDrugs(ev) {
  pagination.search.value = search.value;
}

function addMed(order) {
  delete order.Contraindication;
  delete order.description;

  paperPrescriptionStore.addToList(order);
}

onMounted(() => {
  if(paperPrescriptionStore.done) {
    paperPrescriptionStore.reset()
  }
})
</script>
<template>
  <div class="flex flex-1 h-full overflow-hidden">
    <div class="flex-1 flex justify-center h-full overflow-hidden">
      <div class="flex flex-col gap-4 w-[90%] h-full">
        <div
          class="flex justify-between gap-2 items-center px-2 rounded border-primary border-2 min-h-drug-search-height"
        >
          <input
            v-model="search"
            v-focus
            @keydown.enter="fetchDrugs"
            class="px-2 bg-transparent flex-1 h-12"
            placeholder="Search Drug"
          />
          <Button @click="fetchDrugs" size="md" type="primary"> Search </Button>
        </div>
        <div
          v-if="!pagination.pending.value && !pagination.data.value?.length"
          class="flex-1 flex flex-col gap-12 items-center"
        >
          <img src="/src/assets/img/search_med.svg" />
          <div class="flex flex-col gap-4 items-center">
            <p class="font-medium text-md">
              Please search medicine availability and price.
            </p>
            <p class="text-xs px-20">
              Note: you can search a medicine using medicine or generic name.
            </p>
          </div>
        </div>
        <div v-else class="overflow-auto show-scrollbar">
          <Table
            :pending="pagination.pending.value"
            :headers="{
              head: [
                'Medicine Name',
                'Dosage form',
                'Strength',
                'Price',
                'Quantity',
                'Status',
                'actions',
              ],
              row: [
                'product_name',
                'dosageForm',
                'strength',
                'list_price',
                'quantity',
                'status',
              ],
            }"
            :rows="
              (pagination.data.value || []).map((el) => {
                return {
                  dosageForm: `Accurate dose`,
                  ...el,
                };
              })
            "
            :cells="{
              status: PaperPrescriptionStatusCell,
              list_price: (price) => {
                return formatCurrency(price);
              },
            }"
            :Fallback="TableRowSkeleton"
          >
            <template #actions="{ row }">
              <Button
                v-if="row.quantity > 0"
                @click.stop="addMed(row)"
                class="text-sm"
                size="xs"
                :type="row.quantity > 0 ? 'secondary' : 'primary'"
              >
                Add
              </Button>
            </template>
          </Table>
        </div>
      </div>
    </div>
    <div class="w-[22rem] h-full bg-base-clr-2">
      <LayeredPages
        transitionName="search"
        :baseAnimation="false"
        :pages="[
          {
            name: 'list',
            component: SelectedDrugListPage,
            props: {
              class: 'rounded-tl',
            }
          },
          {
            name: 'search',
            component: SearchSelectedDrugs,
          }
        ]"
      />
    </div>
  </div>
</template>
<style>
  .search-enter-active,
  .search-leave-active {
    transition: transform, opacity .2s ease;
  }

  .search-enter-from {
    transform: translate(100%, 0);
  }

  .search-leave-to {
    opacity: 0;
  }
</style>