<script setup>
import Button from "@/components/Button.vue";
import { useApiRequest } from "@/composables/useApiRequest";
// import SelectedDrugsList from "../components/SelectedDrugsList.vue";
import { checkMedicineAvailability } from "../api/paperPrescriptionApi";
import { usePagination } from "@/composables/usePagination";
import Table from "@/components/Table.vue";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import PaperPrescriptionStatusCell from "@paper_prescription/components/PaperPrescriptionStatusCell.vue";
import { formatCurrency } from "@/utils/utils";
import { usePaperPrescriptionStore } from "@paper_prescription/store/paperPrescriptionStore";
import { useAuth } from "@/stores/auth";
import { onMounted, ref } from 'vue'
import LayeredPages from '@components/LayeredPages.vue'
import SearchSelectedDrugs from "../components/SearchSelectedDrugs.vue";
import SelectedDrugListPage from "../components/SelectedDrugListPage.vue";
import D2DInteractionPage from "../components/D2DInteractionPage.vue";

const auth = useAuth();

const paperPrescriptionStore = usePaperPrescriptionStore();

const pagination = usePagination({
  auto: false,
  cb: checkMedicineAvailability,
});

const search = ref('')
const showLayeredPages = ref(false);

function fetchDrugs(ev) {
  pagination.search.value = search.value;
}

function addMed(order) {
  delete order.Contraindication;
  delete order.description;

  paperPrescriptionStore.addToList(order);
}

onMounted(() => {
  if (paperPrescriptionStore.done) {
    paperPrescriptionStore.reset()
  }
})
</script>
<template>
  <div class="flex flex-1 h-full overflow-hidden">
    <div class="flex-1 flex justify-center h-full overflow-hidden">
      <div class="flex flex-col gap-4 w-[90%] h-full">
        <div
          class="flex justify-between gap-2 items-center px-2 rounded border-primary border-2 min-h-drug-search-height">
          <input v-model="search" v-focus @input="fetchDrugs" class="px-2 bg-transparent flex-1 h-12"
            placeholder="Search Drug" />
          <Button @click="fetchDrugs" size="md" type="primary"> Search </Button>
        </div>
        <div v-if="!pagination.pending.value && !pagination.data.value"
          class="flex-1 flex flex-col gap-12 items-center">
          <img src="/src/assets/img/search_med.svg" />
          <div class="flex flex-col gap-4 items-center">
            <p class="font-medium text-md">
              Please search medicine availability and price.
            </p>
            <p class="text-xs px-20">
              Note: you can search a medicine using medicine or generic name.
            </p>
          </div>
        </div>
        <div v-else class="overflow-auto show-scrollbar">
          <Table :pending="pagination.pending.value" :headers="{
            head: [
              'Medicine Name',
              'Pharmacy Branch Name',
              'Dosage form',
              'Strength',
              'Selling Price',
              'Quantity',
              'Status',
              'actions',
            ],
            row: [
              'drugName',
              'pharmacyBranchName',
              'dosageForm',
              'strength',
              'sellingUnitPrice',
              'totalAmount',
              'status',
            ],
          }" :rows="(pagination.data.value?.content || []).map((el) => {
            return {
              dosageForm: `Accurate dose`,
              ...el,
            };
          })
            " :cells="{
              status: PaperPrescriptionStatusCell,
              list_price: (price) => {
                return formatCurrency(price);
              },
            }" :Fallback="TableRowSkeleton">
            <template #actions="{ row }">
              <Button v-if="row.totalAmount > 0" @click.stop="addMed(row)" class="text-sm" size="xs"
                :type="row.totalAmount > 0 ? 'secondary' : 'primary'">
                Select
              </Button>
            </template>
          </Table>
        </div>
      </div>
    </div>
    <div>
      <div class="md:hidden fixed bottom-4 left-4 z-50">
        <Button @click="showLayeredPages = !showLayeredPages" size="sm" type="secondary">
          <i class="bi bi-list"></i> <!-- Replace with your preferred icon -->
        </Button>
      </div>
      <div
        :class="['w-[22rem] h-full bg-base-clr-2 transition-transform duration-300 ease-in-out', { 'hidden md:block': !showLayeredPages, 'block': showLayeredPages }]">
        <LayeredPages transitionName="search" :baseAnimation="false" :pages="[
          {
            name: 'list',
            component: SelectedDrugListPage,
            props: {
              class: 'rounded-tl',
            }
          },
          {
            name: 'search',
            component: SearchSelectedDrugs,
          },
          {
            name: 'd2d',
            component: D2DInteractionPage,
          }
        ]" />
      </div>

    </div>
    <!-- Icon for Small Screens -->
  </div>
</template>
<style>
.search-enter-active,
.search-leave-active {
  transition: transform, opacity .2s ease;
}

.search-enter-from {
  transform: translate(100%, 0);
}

.search-leave-to {
  opacity: 0;
}
</style>