import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const api = new ApiService();
const path = '/batch';

export function getAllPendingBatches(query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path}/all${qr}`);
}

export function getBatchById(id) {
  return api.addAuthenticationHeader().get(`${path}/${id}`);
}

export function approveBatch(id, data) {
  return api.addAuthenticationHeader().put(`${path}/${id}/approve`, data);
}

export function rejectBatch(id, reason) {
  return api.addAuthenticationHeader().put(`${path}/${id}/reject`, { reason });
}
