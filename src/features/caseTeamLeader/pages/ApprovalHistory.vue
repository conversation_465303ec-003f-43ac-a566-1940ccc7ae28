<script setup>
import Table from "@/components/Table.vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import Button from "@/components/Button.vue";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { mdiArrowLeft } from "@mdi/js";
import { useRouter } from "vue-router";
import {
  BatchStatus,
  openNewTab,
  RequestOptions,
  secondDateFormat,
  toasted,
} from "@/utils/utils";
import { getAllPendingBatches } from "../api/batchApprovalApi";
import { useBatchApprovalStore } from "../store/batchApprovalStore";
import { ref, computed } from "vue";
import { usePagination } from "@/composables/usePagination";
import { openModal } from "@customizer/modal-x";
import { useApiRequest } from "@/composables/useApiRequest";
import {
  getRequestByBatch,
  updateRequestedBatch,
} from "@/features/stock/api/regularRequestApi";

const batchApprovalStore = useBatchApprovalStore();
const router = useRouter();

const pagination = usePagination({
  store: batchApprovalStore,
  cb: (params) =>
    getAllPendingBatches({
      ...params,
      status: BatchStatus.PROCESSED,
    }),
});

const selected = ref([]);
const statusFilter = ref("all");

function goBack() {
  router.push("/case-team-leader");
}

function getStatusClass(status) {
  if (status === BatchStatus.APPROVED) {
    return "bg-green-100 text-green-800";
  } else if (status === BatchStatus.REJECTED) {
    return "bg-red-100 text-red-800";
  }
  return "";
}

const baseUrl = import.meta.env?.v_BASE_URL;

const approveReq = useApiRequest();
function approveBatch(row) {
  if (approveReq.pending.value) return;

  openModal(
    "Confirmation",
    {
      title: "Approve Batch",
      message: "Are you sure you want to approve this batch?",
    },
    (res) => {
      if (res) {
        approveReq.send(
          () =>
            updateRequestedBatch(
              row?.batchUuid,
              row?.inventoryResponses?.map?.((el) => ({
                approvedAmount: el?.approvedAmount,
                toInventoryUuid: el.toInventoryUuid,
                remark: el?.remark || "",
              })),
              { status: BatchStatus.APPROVED }
            ),
          (res) => {
            toasted(res.success, "Batch approved successfully", res.error);
            if (res.success) {
              batchApprovalStore.remove(row?.batchUuid, BatchStatus.APPROVED);
            }
          }
        );
      }
    }
  );
}

const bacthReq = useApiRequest();
function viewBatchDetail(row) {
  if (bacthReq.pending.value) return;

  bacthReq.send(
    () => getRequestByBatch(row?.batchUuid),
    (res) => {
      console.log(res.data.res?.inventoryResponses);

      if (res.success) {
        openModal("ApprovalRequest", {
          batchUuid: row?.batchUuid,
          drugs: res.data?.inventoryResponses,
          editing: false,
        });
      }
    }
  );
}

const active = ref(RequestOptions.Emergency);
function selectActive(name) {
  active.value = name;
}
</script>

<template>
  <div class="h-full w-full flex flex-col gap-4 pt-10 px-24">
    <div class="flex justify-between items-center">
      <div class="flex items-center gap-2">
        <button @click="goBack" class="p-2 rounded-full hover:bg-gray-100">
          <BaseIcon :path="mdiArrowLeft" :size="24" />
        </button>
        <h1 class="text-2xl font-semibold">Approval History</h1>
      </div>
    </div>
    <div class="">
      <Table
        v-model="selected"
        :pending="pagination.pending.value"
        :headers="{
          head: ['Request Date', 'Request Type', 'Status', 'Actions'],
          row: ['sentDate', 'reportType', 'status'],
        }"
        :rows="batchApprovalStore.approvedBatches"
        :cells="{
          sentDate: secondDateFormat,
        }"
      >
        <template #status="{ row }">
          <span
            :class="getStatusClass(row.status)"
            class="px-2 py-1 rounded-full text-xs font-medium"
          >
            {{ row.status }}
          </span>
        </template>
        <template #actions="{ row }">
          <div class="flex gap-2">
            <Button
              :pending="bacthReq.pending.value"
              @click.stop="() => viewBatchDetail(row)"
              type="edge"
              size="xs"
            >
              View Details
            </Button>
            <Button
              @click="openNewTab(`${baseUrl}/model22/${row?.batchUuid}`)"
              size="xs"
              type="secondary"
            >
              Model22
            </Button>
            <Button @click="approveBatch(row)" type="primary" size="xs">
              Approve
            </Button>
          </div>
        </template>
      </Table>
    </div>
  </div>
</template>
