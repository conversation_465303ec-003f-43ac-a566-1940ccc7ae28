<script setup>
import Table from "@/components/Table.vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import Button from "@/components/Button.vue";
import { openModal } from "@customizer/modal-x";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { mdiHistory } from "@mdi/js";
import { useRouter } from "vue-router";
import { BatchStatus, secondDateFormat } from "@/utils/utils";
// import { getAllPendingBatches } from "../api/batchApprovalApi";
import { useBatchApprovalStore } from "../store/batchApprovalStore";
import { ref } from "vue";

const batchApprovalStore = useBatchApprovalStore();
const router = useRouter();

const pagination = usePaginationcopy({
  store: batchApprovalStore,
  storeKey: "pendingBatches",
  cb: (params) => getAllPendingBatches({
    ...params,
    status: BatchStatus.REQUESTED
  }),
});

const selected = ref([]);
const tabs = ref([
  { name: "Pending Approvals", active: true },
  { name: "Approval History", active: false }
]);

function setActiveTab(index) {
  tabs.value = tabs.value.map((tab, i) => ({
    ...tab,
    active: i === index
  }));
  
  if (index === 1) {
    router.push('/approval-history');
  }
}
</script>

<template>
  <div class="h-full w-full flex flex-col gap-4 pt-10 px-24">
    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-semibold">Case Team Leader Dashboard</h1>
    </div>
    
    <div class="flex border-b mb-4">
      <div 
        v-for="(tab, index) in tabs" 
        :key="tab.name"
        @click="setActiveTab(index)"
        class="px-4 py-2 cursor-pointer"
        :class="tab.active ? 'border-b-2 border-blue-500 font-medium' : 'text-gray-500'"
      >
        {{ tab.name }}
      </div>
    </div>
    
    <div class="">
      <Table
        v-model="selected"
        :pending="pagination.pending.value"
        :headers="{
          head: ['Request Date', 'Requested By', 'Status', 'Actions'],
          row: ['sentDate', 'requestedBy', 'status'],
        }"
        :rows="batchApprovalStore.pendingBatches"
        :cells="{
          sentDate: secondDateFormat,
        }"
      >
        <template #actions="{ row }">
          <div class="flex gap-2">
            <Button 
              @click.stop="openModal('BatchApproval', {
                batchUuid: row?.batchUuid,  
                drugs: row?.inventoryResponses
              })" 
              type="secondary" 
              size="xs"
            >
              Review & Approve
            </Button>
          </div>
        </template>
      </Table>
    </div>
  </div>
</template>
