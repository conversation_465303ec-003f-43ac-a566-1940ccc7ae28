<script setup>
import BaseIcon from '@/components/base/BaseIcon.vue';
import Button from '@/components/Button.vue';
import Table from '@/components/Table.vue';
import { usePagination } from '@/composables/usePagination';
import { useApiRequest } from '@/composables/useApiRequest';
import { mdiMagnify, mdiCheck, mdiClose } from '@mdi/js';
import { onMounted } from 'vue';
import { getAllLossAdjustments, approveLossAdjustment, rejectLossAdjustment } from '@/features/stock/api/lossAdjustmentApi';
import { useLossAdjustmentStore } from '@/features/stock/store/lossAdjustmentStore';
import { formatCurrency, LOSS_ADJEstment_STATUS, secondDateFormat, toasted } from '@/utils/utils';
import { openModal } from '@customizer/modal-x';
import TableRowSkeleton from '@/skeletons/TableRowSkeleton.vue';
import { getLossAdjestment } from '@/features/StoreKeeper/api/InventoryApi';

const lossAdjustmentStore = useLossAdjustmentStore();
const approveRequest = useApiRequest();
const rejectRequest = useApiRequest();

// Set up pagination with the getAllLossAdjustments API
const pagination = usePagination({
	store: lossAdjustmentStore,
  cb: (params) => getLossAdjestment({
    ...params,
    status: LOSS_ADJEstment_STATUS.REQUESTED_LOSS // Filter to only show requested losses
  }),
});

// Function to approve a loss adjustment
function approveAdjustment(row) {
  openModal('Confirmation', {
    title: 'Approve Loss Adjustment',
    message: 'Are you sure you want to approve this loss adjustment?'
  }, (res) => {
    if (res) {
      approveRequest.send(
        () => approveLossAdjustment(row?.inventoryUuid, {
					...row,
					amount: row?.quantity
				}),
        (res) => {
          if (res.success) {
            lossAdjustmentStore.remove(row?.inventoryUuid, row);
          }
          toasted(res.success, "Loss adjustment approved successfully", res.error);
        }
      );
    }
  });
}

// Function to reject a loss adjustment
function rejectAdjustment(row) {
  openModal('Confirmation', {
    title: 'Reject Loss Adjustment',
    message: 'Are you sure you want to reject this loss adjustment?'
  }, (res) => {
    if (res) {
      rejectRequest.send(
        () => rejectLossAdjustment(row?.inventoryUuid, row),
        (res) => {
          toasted(res.success, "Loss adjustment rejected", res.error);
        }
      );
    }
  });
}

</script>

<template>
  <div class="h-full w-full flex flex-col gap-4 p-6">
    <!-- Header with search and add button -->
    <div class="flex justify-between items-center py-4">
      <h1 class="text-2xl font-semibold">Loss Adjustment Requests</h1>

      <div class="flex gap-4">
        <!-- Search input -->
        <div class="flex border rounded px-2 h-12 items-center">
          <input
            v-model="pagination.search.value"
            class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80"
            placeholder="Search by medicine name or batch number"
          />
          <BaseIcon :path="mdiMagnify" :size="20" />
        </div>
      </div>
    </div>

    <!-- Table of loss adjustments -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <Table
        :pending="pagination.pending.value"
        :headers="{
          head: [
            'Medicine Name',
            'Adjustment Amount',
            'Remark',
            'Actions'
          ],
          row: [
            'drugName',
            'quantity',
            'remark'
          ]
        }"
        :rows="lossAdjustmentStore.adjustments || []"
        :cells="{
          amount: (amount) => {
            return formatCurrency(amount);
          },
          requestDate: secondDateFormat
        }"
        :Fallback="TableRowSkeleton"
      >
        <template #actions="{ row }">
          <div class="flex gap-2">
            <Button
              @click="approveAdjustment(row)"
              :pending="approveRequest.pending.value"
              size="xs"
              type="primary"
              class="flex items-center gap-1"
            >
              <BaseIcon :path="mdiCheck" :size="16" />
              Approve
            </Button>
          </div>
        </template>
      </Table>
    </div>
  </div>
</template>