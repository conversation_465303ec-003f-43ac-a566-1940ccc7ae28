import { ref } from "vue";
import { defineStore } from "pinia";

export const useBatchApprovalStore = defineStore("batchApprovalStore", () => {
  const approvedBatches = ref([]);

  function getAll() {
    return approvedBatches.value
  }

  function set(data) {
    approvedBatches.value = data
  }

  function setApprovedBatches(data) {
    approvedBatches.value = data;
  }

  function updateBatchStatus(id, status, data = {}) {
    const idx = approvedBatches.value.findIndex((batch) => batch.batchUuid === id);
    if (idx === -1) return;

    const batch = { ...pendingBatches.value[idx], status, ...data };
    
    pendingBatches.value.splice(idx, 1);
    
    approvedBatches.value.push(batch);
  }

  function remove(batchUuid) {
    approvedBatches.value = approvedBatches.value.filter((batch) => batch.batchUuid !== batchUuid);
  }

  return {
    getAll,
    set,
    approvedBatches,
    setApprovedBatches,
    updateBatchStatus,
    remove
  };
});
