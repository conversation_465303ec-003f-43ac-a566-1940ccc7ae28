import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const URL = import.meta.env?.v_API_URL;
const api = new ApiService(URL);

const path = "/prescriptions";

export function searchInvoice(query = {}, config = {}) {
  const qr = getQueryFormObject(query);
  return api
    .addAuthenticationHeader()
    .get(`${path}/getPrescriptions${qr}`, config);
}

export function getPatientPrescription(query = {}, config = {}) {
  const qr = getQueryFormObject(query);
  return api
    .addAuthenticationHeader()
    .get(`${path}/getPrescriptionByPrescriptionUuid${qr}`, config);
}
export function updatePrescription(query = {}, data, config = {}) {
  const qr = getQueryFormObject(query);
  return api
    .addAuthenticationHeader()
    .put(`${path}/updatePrescriptionStatus${qr}`, data);
}

export function cancelPrescription(id, data) {
  return api
    .addAuthenticationHeader()
    .put(`${path}/CancelPrescription?prescriptionUuid=${id}`, data);
}
export function cancelPrescriptionToDoctor(id, data) {
  return api
    .addAuthenticationHeader()
    .put(`${path}/returnToDoctor?prescriptionUuid=${id}`, data);
}
export function getCashierReport(query = {}) {
  const qr = getQueryFormObject(query);
  return api
    .addAuthenticationHeader()
    .get(`${path}/cashierPrescriptionReport${qr}`);
}
