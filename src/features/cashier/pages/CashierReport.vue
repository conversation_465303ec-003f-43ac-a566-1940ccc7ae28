<script setup>
import Table from "@/components/Table.vue";
import { usePagination } from "@/composables/usePagination";
import { computed, ref } from "vue";
import { getCashierReport } from "../api/getInvoice";
import { useApiRequest } from "@/composables/useApiRequest";
import { Icon } from "@iconify/vue";
import icons from "@/utils/icons";
import { formatCurrency, formatNumber } from "@/utils/utils";
import Select from "@/components/new_form_elements/Select.vue";
import Input from "@/components/new_form_elements/Input.vue";
import Form from "@/new_form_builder/Form.vue";
import Button from "@/components/Button.vue";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import MultipleSelect from "@/components/new_form_elements/MultipleSelect.vue";

const startDateTime = ref("");
const endDateTime = ref("");
const search = ref("");
const paymentOptions = ["Cash", "Credit", "Online", "Insurance"];

const reportParams = ref({
  fromDate: new Date().toISOString().split("T")[0],
  toDate: new Date().toISOString().split("T")[0],
  paymentType: [],
});

const req = useApiRequest();
// const pagination = usePagination({
// 	cb: (data) => getCashierReport({ ...data, fromDate: startDateTime.value, toDate: endDateTime.value, page: 1, limit: 25 }),
// })

const isValidRange = computed(() => {
  if (!startDateTime.value || !endDateTime.value) return false;
  return new Date(startDateTime.value) <= new Date(endDateTime.value);
});

function validateRange() {
  if (startDateTime.value && !endDateTime.value) {
    endDateTime.value = startDateTime.value;
  }
}

function fetchData({ values }) {
  req.send(
    () =>
      getCashierReport({
        page: 1,
        limit: 25,
        search: search.value,
        fromDate: reportParams.value.fromDate,
        toDate: reportParams.value.toDate,
        paymentType: reportParams.value.paymentType,
      }),
    (res) => {
      if (res.success) {
        console.log(res.data, "Success");
      }
    }
  );
}

function validateDates(fromDate, toDate) {
  if (new Date(toDate) < new Date(fromDate)) {
    alert("End date cannot be before start date");
    return false;
  }
  return true;
}

const reportReq = useApiRequest();

function genReport() {
  reportReq.send(
    () =>
      getExportSalesReport({
        status: "PAID",
        fromDate: props.fromDate,
        toDate: props.toDate,
        paymentType: props.paymentType,
        export: true,
      }),
    (res) => {
      if (res.success) {
        const url = URL.createObjectURL(res.data);
        excel.value.href = url;
        excel.value.setAttribute(
          "download",
          `report_from_${secondDateFormat(
            props.fromDate
          )}_to_${secondDateFormat(props.toDate)}.xlsx`
        );
        excel.value.click();
      }
    }
  );
}
</script>
<template>
  <div
    class="relative min-h-full w-full flex flex-col bg-white gap-9 px-[124px] mt-7 py-6"
  >
    <Form
      class="grid grid-cols-4 gap-6"
      v-slot="{ submit }"
      id="cashierreportform"
    >
      <MultipleSelect
        name="paymentType"
        label="Select Payment Type"
        v-model="reportParams.paymentType"
        :options="paymentOptions"
        :attributes="{
          type: 'text',
          placeholder: 'Select Payment TYpe',
        }"
      />
      <Input
        @clcik="validateDates(reportParams.fromDate, reportParams.toDate)"
        name="fromDate"
        label="From"
        v-model="reportParams.fromDate"
        :attributes="{
          type: 'datetime-local',
          placeholder: 'Select Date',
        }"
      />
      <Input
        @click="validateDates(reportParams.fromDate, reportParams.toDate)"
        name="toDate"
        label="To"
        v-model="reportParams.toDate"
        :attributes="{
          type: 'datetime-local',
          placeholder: 'Select Date',
        }"
      />
      <Button
        type="primary"
        @click.prevent="submit(fetchData)"
        class="self-end"
      >
        <div
          v-if="req.pending.value"
          class="flex justify-center items-center h-full"
        >
          <Icon icon="svg-spinners:3-dots-scale" class="text-2xl" />
        </div>
        <div v-else class="flex justify-center items-center h-full">
          Get Report
        </div>
      </Button>
      <hr class="col-span-4" />
    </Form>
    <div class="flex flex-col gap-4 bg-[#F1F8F7] p-4">
      <div class="flex justify-between items-center mt-4 mb-4">
        <div
          class="w-full flex justify-between items-center opacity-65 font-bold gap-4 text-lg"
        >
          Report Preview
          <div class="flex gap-4 text-sm text-gray-600">
            <div class="" v-if="reportParams.fromDate && reportParams.toDate">
              Showing report from
              <span class="font-bold">{{ reportParams.fromDate }}</span> to
              <span class="font-bold">{{ reportParams.toDate }}</span>
            </div>
            <div class="" v-if="reportParams.paymentType">
              Payment Type:
              <span class="font-bold">{{
                reportParams.paymentType.join()
              }}</span>
            </div>
            <div class="flex items-centerfont-bold">
              Total | {{ formatCurrency(req.response.value?.totalAmount) }}
            </div>
          </div>
        </div>
      </div>
      <div
        class="w-50 h-12 p-3 rounded border border-[#A4A4A4] bg-white flex items-center gap-4"
      >
        <i class="opacity-65" v-html="icons.search" />
        <input
          v-model="search"
          class="placeholder:opacity-65"
          placeholder="Search Added Item "
        />
      </div>
      <Table
        class="bg-white"
        :pending="req.pending.value"
        :Fallback="TableRowSkeleton"
        :headers="{
          head: [
            'Cashier Name',
            'Serial Number',
            'Patient Name',
            'Drug Name',
            'Brand Name',
            'Quantity',
            'Price',
          ],
          row: [
            'updatedBy',
            'fsNumber',
            'fullname',
            'drugName',
            'drugBrandName',
            'totalQuantity',
            'drugPrice',
          ],
        }"
        :rows="
          (req.response.value?.prescriptionGetResponses || []).reduce(
            (state, pres) => {
              const prescription = pres.drugPrescriptions || [];
              prescription.forEach((el) => {
                state.push({
                  ...el,
                  ...pres,
                });
              });

              return state;
            },
            []
          )
        "
        :cells="{
          fullname: (_, row) => {
            return `${row?.patient?.firstName ?? ''} ${row?.patient?.fatherName ?? ''}`;
          },
          totalQuantity: (_, row) => {
            return `${row?.totalQuantity ?? ''} ${row?.unit ?? ''}`;
          },
          drugPrice: (price) => {
            return formatCurrency(price);
          },
        }"
      >
        <template #actions="{ row }"> </template>
      </Table>
      <div class="grid grid-cols-3 w-full gap-4 items-center">
				<Button type="edge" size="md" >
					Print Report
				</Button>
        <Button
					size="md"
          type="primary"
          @click="genReport"
          class="col-span-2 justify-center hover:bg-blue-600 transition flex gap-2 items-center"
          :disabled="!req.pending.value?.prescriptionReport?.length"
        >
          <i class="text-white" v-html="icons.export" />
          Download Report
        </Button>
      </div>
    </div>
  </div>
</template>
