<script setup>
import Button from "@components/Button.vue";
import Icon from "@components/Icon.vue";
import { closeModal, useModal } from "@customizer/modal-x";
import { cancelPrescription } from "../api/getInvoice";
import { useApiRequest } from "@/composables/useApiRequest";
import { ref } from "vue";
import { toasted } from "@utils/utils";
import Form from "@/new_form_builder/Form.vue";
import Textarea from "@/components/new_form_elements/Textarea.vue";

const { getModal } = useModal();

const data = getModal("CancelOrder");

const props = defineProps({
  prescriptionUuid: {
    type: String,
  },
});

const remark = ref("");
const req = useApiRequest();
function submitReason({ values }) {
  req.send(
    () => cancelPrescription(data.data?.prescriptionUuid, values),
    (res) => {
      toasted(res.success, "Caceled", res.error);
      if (res.success) {
        closeModal();
      }
    }
  );
}
</script>
<template>
  <div class="grid h-full place-items-center">
    <Form
      v-slot="{ submit }"
      id="cancle-order"
      class="shadow-lg backdrop-blur-sm bg-base-clr-2 rounded min-h-[10rem] w-[20rem]"
    >
      <div class="m-2 p-2 border-b flex justify-between items-center gap-2">
        <div class="flex flex-col gap-2">
          <p class="font-bold text-lg">Cancel Order</p>
          <span class="text-xs">{{ data?.data?.patientName }}</span>
          <span class="text-xs">{{ data?.data?.drugName }}</span>
        </div>
        <button @click="closeModal()">
          <Icon name="solar:close-circle-outline" />
        </button>
      </div>
      <div class="p-2 flex flex-col gap-4">
        <Textarea
          name="remark"
          validation="required"
          placeholder="Remark"
          class="bg-accent rounded w-full resize-none"
        />
        <Button
          :pending="req.pending.value"
          @click.prevent="submit(submitReason)"
          type="primary"
        >
          Submit
        </Button>
      </div>
    </Form>
  </div>
</template>
