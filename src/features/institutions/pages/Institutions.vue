<script setup>
import BaseIcon from "@/components/base/BaseIcon.vue";
import { mdiMagnify, mdiPlus, mdiChevronDown } from "@mdi/js";
import Button from "@/components/Button.vue";
import { ref } from "vue";
import { openModal } from "@customizer/modal-x";
import NewFormModal from "@/components/NewFormModal.vue";
import Dropdown from "@/components/Dropdown.vue";
import { usePagination } from "@/composables/usePagination";
import { getInstitutions } from "../api/InstitutionApi";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import { removeUndefined } from "@/utils/utils";
import Table from "@/components/Table.vue";
import { useRouter } from "vue-router";
import DefaultPage from "@/components/DefaultPage.vue";
const router = useRouter();

const pagination = usePagination({
  cb: (data) =>
    getInstitutions(
      removeUndefined({
        search: data.search,
      })
    ),
});
function fetchInstitution(ev) {
  pagination.search.value = ev.target.value;
}
</script>
<template>
  <DefaultPage>
    <div class="flex justify-between h-14">
      <div class="flex border rounded px-2 items-center">
        <input class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80"
          placeholder="Search Institutions " @keydown.enter="fetchInstitution" />
        <BaseIcon :path="mdiMagnify" :size="20" />
      </div>
      <button @click.prevent="openModal('Institution')"
        class="flex gap-4 bg-accent px-4 border rounded h-14 items-center">
        <BaseIcon :path="mdiPlus" :size="20" />
        <p class="opacity-80">Add Institution</p>
      </button>
    </div>
    <Table :headers="{
      head: [
        ' iD',
        'Institution Name',
        'Contact',
        'City',
        'Woreda',
        'actions',
      ],
      row: ['id', 'institutionName', 'contactPersonPhone', 'city', 'woreda'],
    }" :pending="pagination.pending.value" :Fallback="TableRowSkeleton" :rows="pagination.data.value || []">
      <template #actions="{ row }">
        <Dropdown v-slot="{ setRef, toggleDropdown }">
          <button @click="toggleDropdown" class="rounded-full py-1 flex justify-center items-center px-2 text-primary">
            <div>More</div>
            <BaseIcon :path="mdiChevronDown" :size="24" />
          </button>
          <!-- $router.push("/edit_privilege/" + row?.privilegeUuid) -->
          <div class="flex shadow-lg px-4 border py-2 rounded flex-col gap-2 bg-white" :ref="setRef">
            <button @click=" 
              router.push({
                name: 'Members',
                params: {
                  Uuid: row.institutionUuid,
                },
              })
              " title="View Members"
              class="flex w-full font-bold p-2 items-center gap-2 rounded-lg hover:shadow-xl duration-200">
              <div class="flex flex-row gap-4 items-center">
                <img class="w-5 h-5" src="@/assets/img/dropDown/HamburgerMenu.png" />
                <span class="whitespace-nowrap pr-4">View Members </span>
              </div>
            </button>
            <button @click="$router.push('/institutions/claims/' + row?.institutionUuid)" title="RequestClaim"
              class="rounded-lg hover:shadow-xl duration-200">
              <div class="flex flex-row rounded-lg font-bold p-2 gap-4 items-center">
                <img class="w-5 h-5" src="@/assets/img/dropDown/PenNewSquare.png" />
                <span class="whitespace-nowrap pr-4">Request claim</span>
              </div>
            </button>
            <button title="Deativate" class="text-error whitespace-nowrap rounded-lg hover:shadow-xl duration-200">
              <div class="flex items-center gap-4 rounded-lg font-bold p-2">
                <img class="w-5 h-5" src="@/assets/img/dropDown/ForbiddenCircle.png" />

                <span class="whitespace-nowrap pr-4">Deactivate</span>
              </div>
            </button>
          </div>
        </Dropdown>
      </template>
    </Table>
  </DefaultPage>
</template>
