<script setup>
import FormSubmitButton from "@/components/FormSubmitButton.vue";
import MemberInputs from "@/features/institutions/components/MemberInputs.vue";
import {
  Form,
  Input,
  Select,
  Textarea,
  InputFile,
} from "@components/new_form_elements";
import { useForm } from "@/new_form_builder/useForm";
import { useMembersForm } from '@/features/institutions/store/membresFormStore'
import { Gender } from '@utils/utils'

const formData = useMembersForm()

const props = defineProps({
  dependant: {
    type: Object,
  },
  onSubmit: {
    type: Function,
    required: true
  }
});

function institutionAdd({ values, reset }) {
  props.addDependency(values);
  reset();
}

function submitForm({values, reset}) {
  props.onSubmit({
    ...props.dependant,
    ...values
  });
  reset()
}
</script>
<template>
  <Form class="grid grid-cols-2 gap-4" id="dependentForm" v-slot="{ submit }">
    <Select
      class="items-center"
      label="Gender"
      :value="dependant?.gender || Gender.FEMALE"
      :attributes="{
        placeholder: 'Select Gender',
        type: 'text',
      }"
      name="gender"
      :options="Object.values(Gender)"
      validation="required"
    />
    <Input
      :value="dependant?.dependentName || ''"
      label="Dependent First Name"
      name="dependentName"
      :attributes="{
        placeholder: 'Dependent First Name',
      }"
      validation="required"
    />
    <Input
      :value="dependant?.dependentFatherName || ''"
      label="Dependent Father’s Name"
      name="dependentFatherName"
      :attributes="{
        placeholder: 'Father’s Name',
      }"
      validation="required"
    />
    <Input
    :value="dependant?.dependentGrandFatherName || ''"
      
      label="Dependent Grand Father's Name"
      name="dependentGrandFatherName"
      :attributes="{
        placeholder: 'Grand Father\'s Name',
      }"
      validation="required"
    />
    <Input
    :value="dependant?.occupation || ''"
      
      label="Occupation"
      name="occupation"
      :attributes="{
        placeholder: 'Type Occupation',
      }"
    />
    <Input
    :value="dependant?.birthDate || ''"
      
      label="Date of Birth" 
      validation="required"
      name="birthDate"
      :attributes="{
        type: 'date',
      }"
    />
    <Select
    :value="dependant?.relationship || ''"

      :options="['Spouse', 'Child']"
      validation="required"
      label="Relation ship"
      name="relationship"
      :attributes="{
        placeholder: 'Relation ship',
        type: 'text'
      }"
    />
    <Input
    :value="dependant?.dependentId || ''"
      
      label="Dependent ID"
      name="dependentId"
      :attributes="{
        placeholder: 'Type Dependent ID',
      }"
    />
    <Input
      label="Woreda"
      name="woreda"
      :value="dependant?.woreda || ''"
      :attributes="{
        placeholder: 'Type Woreda',
      }"
    />
    <button
      @click.prevent="submit(submitForm)"
      class="col-span-2 border rounded border-secondary flex justify-center py-2"
    >
      <p class="font-bold">Add Dependent</p>
    </button>
  </Form>
</template>
