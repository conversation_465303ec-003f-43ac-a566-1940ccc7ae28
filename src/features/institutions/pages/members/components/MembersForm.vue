<script setup>
import { Form } from "@components/new_form_elements";
import { useForm } from "@/new_form_builder/useForm";
import { ref } from "vue";
import Button from '@components/Button.vue'
import MemberInputs from "@/features/institutions/components/MemberInputs.vue";

const props = defineProps({
  pending: {
    type: Boolean,
    default: false
  },
  member: {
    type: Object,
  },
  onAddMember: {
    type: Function,
    required: true,
  },
  onSubmit: {
    type: Function,
    required: true,
  }
});

const dependant = ref([]);

function addDependency(values) {
  dependant.value.unshift(values);
}

const { submit } = useForm("membersForm");

function addMember({ values, reset }) {
  props.onAddMember({
    ...props.member,
    ...values,
  });
  reset();
}
</script>
<template>
  <Form :inner="false" class="grid grid-cols-2 gap-4" id="membersForm">
    <MemberInputs :member="member" />
    <div class="col-span-2 flex items-center gap-2">
      <slot />
      <button
        :disabled="pending"
        @click.prevent="submit(addMember)"
        class="flex-1 border rounded border-secondary flex justify-center py-2"
      >
        <p class="font-bold">Add Member</p>
      </button>
      <Button
        :pending="pending"
        type="primary"
        size="xs"
        @click.prevent="onSubmit"
        class="border-error border px-4 py-2 rounded text-error"
      >
        Submit
      </Button>
    </div>
  </Form>
</template>
