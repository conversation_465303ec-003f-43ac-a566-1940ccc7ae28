import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const URL = import.meta.env?.v_API_URL;
const api = new ApiService(URL);

const path = "/insuredPerson";

export function getInsred() {
  return api.addAuthenticationHeader().get(`${path}/all`)
}
export function importMember(query = {}, data) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().post(`${path}/import${qr}`, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
