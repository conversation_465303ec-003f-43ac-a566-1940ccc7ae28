<script setup>
import { ModalParent, closeModal } from "@customizer/modal-x";
import InstitutionForm from "./InstitutionForm.vue";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { mdiClose } from "@mdi/js";
import NewFormModal from "@/components/NewFormModal.vue";
</script>
<template>
  <NewFormModal name="Institution" class="flex justify-center min-h-full w-full overflow-scroll inset-0">
    <div class="p-8 gap-10 bg-white rounded-lg flex flex-col">
      <div class="flex w-full justify-between">
        <h1 class="font-bold text-[1rem] leading-[1.5rem]">
          Add new Institution
        </h1>
        <button @click="closeModal()"
          class="border rounded-full w-4 h-4 border-text-clr p-2 items-center justify-center flex">
          <BaseIcon :path="mdiClose" :size="10" />
        </button>
      </div>
      <InstitutionForm />
    </div>
  </NewFormModal>
</template>
