<script setup>
import { Form, Input, Select, Textarea } from "@components/new_form_elements";
import FormSubmitButton from "@components/FormSubmitButton.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { toasted } from "@/utils/utils";
import Institutions from "../pages/Institutions.vue";
import { addInstitution } from "../api/InstitutionApi";
import { useRouter } from "vue-router";
import { closeModal } from "@customizer/modal-x";
const institutions = useApiRequest();
const router = useRouter();
function institutionAdd({ values }) {
  delete values.institutionType;
  institutions.send(
    () => addInstitution(values),
    (res) => {
      toasted(res.success, "Institution submitted successfully", res.error);
      closeModal();
      router.push({
        name: "Members",
        params: {
          Uuid: res.data.institutionUuid,
        },
      });
    }
  );
}
</script>
<template>
  <Form class="grid grid-cols-3 w-full gap-8" id="InstitutionForm" v-slot="{ submit }">
    <Input class="col-span-2 items-center" label="Institution Name" :attributes="{
      placeholder: 'Institution Name',
      type: 'text',
    }" name="institutionName" validation="required" />
    <Input label=" City" name="city" :attributes="{
      placeholder: ' City',
    }" validation="required" />

    <Input label=" Woreda *" name="woreda" :attributes="{
      placeholder: ' Woreda',
    }" validation="required" />
    <Select class="items-center" label="Institution Type *" :attributes="{
      placeholder: 'Institution Type',
      type: 'text',
    }" name="institutionType" :options="['Bole', 'Saris']" validation="required" />
    <Input label="Email  *" name="email" :attributes="{
      placeholder: 'Email',
    }" validation="required|email" />

    <Input label="Phone Number *" name="contactPersonPhone" :attributes="{
      placeholder: 'Eg: 0911010203',
    }" validation="required|phone" />
    <Input label=" TIN *" name="tinNumber" :attributes="{
      placeholder: 'Eg: 0911010203',
    }" validation="required" />
    <div class="col-span-2">
      <Input label="Remark" name="description" :attributes="{
        placeholder: 'Write Remark ',
      }" />
    </div>
    <div class="w-full flex pt-10 col-span-3 justify-end">
      <div class="flex w-[8rem] justify-end">
        <FormSubmitButton @click.prevent="submit(institutionAdd)" class="col-span-2 font-bold rounded bg-secondary"
          btn-text="Continue" />
      </div>
    </div>
  </Form>
</template>
