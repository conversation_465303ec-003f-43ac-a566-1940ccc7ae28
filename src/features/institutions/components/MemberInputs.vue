<script setup>
import { Input, Select } from "@components/new_form_elements";
import { Gender } from "@utils/utils";

const props = defineProps({
  member: Object,
});
</script>
<template>
  <Select label="Gender" :value="member?.gender || ''" :attributes="{
    placeholder: 'Select Gender',
    type: 'text',
  }" name="gender" :options="Object.values(Gender)" validation="required" />
  <Input label=" Name" name="firstName" :value="member?.firstName || ''" :attributes="{
    placeholder: ' Member\'s Name',
  }" validation="required" />
  <Input label="Father's Name" name="fatherName" :value="member?.fatherName || ''" :attributes="{
    placeholder: 'Father\'s Name',
  }" validation="required" />
  <Input label="Grand Father's Name" name="grandFatherName" :value="member?.grandFatherName || ''" :attributes="{
    placeholder: 'Grand Father\'s Name',
  }" validation="required" />
  <Input label="CBHI ID" name="cbhiId" :value="member?.cbhiId || ''" :attributes="{
    placeholder: 'Enter CBHI ID',
  }" validation="required" />
  <Input label="Woreda" name="woreda" :value="member?.woreda || ''" :attributes="{
    placeholder: 'Type Woreda',
  }" />
  <Input label="Occupation" name="occupation" :value="member?.occupation || ''" :attributes="{
    placeholder: 'Type Occupation',
  }" />
  <Input label="Phone Number" name="mobilePhone" :value="member?.mobilePhone || ''" :attributes="{
    placeholder: 'Phone Number',
  }" validation="required|phone" />
  <Input label="Email" name="email" validation="email" :value="member?.email || ''" :attributes="{
    placeholder: 'Email',
  }" />
  <Input label="Date of Birth" name="birthDate" :value="member?.birthDate || ''" :attributes="{
    type: 'date',
  }" />
  <!-- <Input
    label="Registration Date"
    validation="required"
    name="registrationDate"
    :value="member?.registrationDate || ''"
    :attributes="{
      type: 'date',
    }"
  /> -->
  <Input label="End date" name="endDate" :value="member?.endDate || ''" :attributes="{
    type: 'date',
  }" />
  <Input label="Member ID" name="memberId" :value="member?.memberId || ''" :attributes="{
    placeholder: 'Type Member ID',
  }" />
</template>
