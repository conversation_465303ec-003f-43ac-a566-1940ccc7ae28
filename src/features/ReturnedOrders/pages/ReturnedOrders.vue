<script setup>
import Button from "@/components/Button.vue";
import Table from "@/components/Table.vue";
import { usePagination } from "@/composables/usePagination";
import { checkMedicineAvailability } from "@/features/paper_prescription/api/paperPrescriptionApi";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import { allRequest, formatAgeToFraction, formatCurrency, removeUndefined } from "@/utils/utils";
import { searchInvoice } from "@/features/cashier/api/getInvoice";
import { usePaperPrescriptionStore } from '@paper_prescription/store/paperPrescriptionStore'
import { useRouter } from 'vue-router'
import { useApiRequest } from "@/composables/useApiRequest";
import { getMedicineByInventryId } from "@/features/StoreKeeper/api/InventoryApi";

const router = useRouter()

const paperPrescriptionStore = usePaperPrescriptionStore()

const pagination = usePagination({
  cb: (data) =>
    searchInvoice(
      removeUndefined({
        search: data.search,
        status: "RETURNED",
      })
    ),
});

function fetchDrugs(ev) {
  pagination.search.value = ev.target.value;
}


const getDataReq = useApiRequest()
function editPrescription(data) {
  //   paperPrescriptionStore.setData({
  //     ...data,
  //     drugPrescriptions: data.drugPrescriptions.map((el, idx) => ({
  //       ...el,
  //     }))
  //   }, true)
  //   router.push('/paper_prescription/detail')
  // }

  const drugs = data.drugPrescriptions.reduce((req, drug) => {
    req[drug.inventoryUuid] = getMedicineByInventryId(drug.inventoryUuid)
    return req
  }, {})


  getDataReq.send(
    () => allRequest(drugs),
    res => {
      if (res.success) {
        paperPrescriptionStore.setData({
          ...data,
          drugPrescriptions: Object.values(res.data)
            .map((el, idx) => {
              console.log(idx, data.drugPrescriptions[idx]);

              return { ...el, ...data.drugPrescriptions[idx] }
            })
        }, true)
        router.push('/paper_prescription/detail')
      }
    }
  )
}

</script>
<template>
  <div class="flex h-full pt-8">
    <div class="flex justify-center flex-1 overflow-auto show-scrollbar">
      <div class="flex flex-col gap-4 w-[90%] h-full">
        <div class="flex justify-end">
          <Button type="primary" v-if="router.currentRoute.value.path !== '/compoundingReturnedOrders'"
            @click="router.push('/compoundingReturnedOrders')" class="text-sm bg-dark text-white py-3" size="md">
            Compounding ReturnedOrder
          </Button>
        </div>
        <div
          class="flex justify-between gap-2 items-center px-2 rounded border-primary border-2 min-h-drug-search-height">
          <input @input="fetchDrugs" autofocus class="px-2 bg-transparent flex-1 h-12"
            placeholder="Search by Phone Number or Patient ID" />
          <Button size="md" type="primary"> Search </Button>
        </div>
        <Table :pending="pagination.pending.value" :Fallback="TableRowSkeleton" :headers="{
          head: [
            'Patient Name',
            'Phone Number',
            'Birth Date',
            'Gender',
            'Total Order',
            'Status',
            'Drug Price',
            'Declined Reason',
            'actions',
          ],
          row: [
            'fullName',
            'patient.mobilePhone',
            'birthDate',
            'patient.gender',
            'totalOrder',
            'status',
            'drugPrice',
            'remark',
          ],
        }" :rows="pagination.data.value || []" :cells="{
          birthDate: (_, row) => formatAgeToFraction(row?.patient?.age),
          fullName: (_, row) => {
            return `${row?.patient?.title ?? ''} ${row?.patient?.firstName ?? ''} ${row?.patient?.fatherName ?? ''} ${row?.patient?.grandFatherName ?? ''}`;
          },
          drugPrescriptions: (drugPrescriptions) => {
            return drugPrescriptions.map((el) => el?.product_name).join(',');
          },
          drugPrice: (_, row) => {
            return row.drugPrescriptions.reduce(
              (accumulator, currentValue) =>
                accumulator + currentValue.list_price,
              0
            );
          },
        }">
          <template #actions="{ row }">
            <div class="flex gap-4">
              <Button @click.stop="editPrescription(row)" class="text-sm bg-dark text-white" size="xs">
                Edit
              </Button>
            </div>
          </template>
        </Table>
        <div v-if="getDataReq.pending.value" class="loading-overlay">
          <div class="loading-message">Loading...</div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(109, 108, 108, 0.8);
  /* Semi-transparent background */
  /* backdrop-filter: blur(5px); */
  /* Blur effect */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  /* Ensure it is on top of other elements */
}

.loading-message {
  font-size: 2rem;
  /* Adjust size as needed */
  color: #333;
  /* Adjust color as needed */
}
</style>