// Test conversion logic
function getConversionFactor(symbol, type) {
  if (!symbol || !type) return null;
  
  const lowerSymbol = symbol.toLowerCase();
  
  if (type === 'WEIGHT') {
    const weightConversions = {
      'mg': 0.001,
      'mcg': 0.000001,
      'µg': 0.000001,
      'g': 1,
      'kg': 1000,
      'grain': 0.0648
    };
    return weightConversions[lowerSymbol] || null;
  }
  
  if (type === 'VOLUME') {
    const volumeConversions = {
      'ml': 0.001,
      'µl': 0.000001,
      'l': 1,
      'drop': 0.00005,
      'tsp': 0.005,
      'tbsp': 0.015
    };
    return volumeConversions[lowerSymbol] || null;
  }
  
  return null;
}

// Test cases
console.log('Testing conversion factors:');
console.log('mg + WEIGHT:', getConversionFactor('mg', 'WEIGHT')); // Should be 0.001
console.log('g + WEIGHT:', getConversionFactor('g', 'WEIGHT')); // Should be 1
console.log('kg + WEIGHT:', getConversionFactor('kg', 'WEIGHT')); // Should be 1000
console.log('ml + VOLUME:', getConversionFactor('ml', 'VOLUME')); // Should be 0.001
console.log('l + VOLUME:', getConversionFactor('l', 'VOLUME')); // Should be 1
console.log('tablet + COUNT:', getConversionFactor('tablet', 'COUNT')); // Should be null (not implemented)
console.log('unknown + WEIGHT:', getConversionFactor('unknown', 'WEIGHT')); // Should be null
