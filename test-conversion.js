// Test conversion logic
function getConversionFactor(symbol, type) {
  if (!symbol || !type) return null;

  const lowerSymbol = symbol.toLowerCase();

  if (type === 'WEIGHT') {
    const weightConversions = {
      'mg': 0.001,
      'mcg': 0.000001,
      'µg': 0.000001,
      'g': 1,
      'kg': 1000,
      'grain': 0.0648
    };
    return weightConversions[lowerSymbol] || null;
  }

  if (type === 'VOLUME') {
    const volumeConversions = {
      'ml': 0.001,
      'µl': 0.000001,
      'l': 1,
      'drop': 0.00005,
      'tsp': 0.005,
      'tbsp': 0.015
    };
    return volumeConversions[lowerSymbol] || null;
  }

  if (type === 'COUNT') {
    const countConversions = {
      'tablet': 1,
      'capsule': 1,
      'pill': 1,
      'piece': 1,
      'vial': 1,
      'ampoule': 1,
      'bottle': 1,
      'box': 10,
      'pack': 10,
      'strip': 10,
      'blister': 10,
      'sachet': 1,
      'tube': 1,
      'jar': 1,
      'syringe': 1
    };
    return countConversions[lowerSymbol] || null;
  }

  if (type === 'DOSAGE') {
    const dosageConversions = {
      'iu': 1,
      'unit': 1,
      'dose': 1,
      'spray': 1,
      'puff': 1,
      'application': 1,
      'patch': 1
    };
    return dosageConversions[lowerSymbol] || null;
  }

  if (type === 'CONCENTRATION') {
    const concentrationConversions = {
      'mg/ml': 1,
      'g/l': 1000,
      '%': 10000,
      'ppm': 0.001,
      'mg/g': 1,
      'iu/ml': 1,
      'mcg/ml': 0.001
    };
    return concentrationConversions[lowerSymbol] || null;
  }

  return null;
}

// Test cases
console.log('Testing conversion factors:');
console.log('\n=== WEIGHT ===');
console.log('mg + WEIGHT:', getConversionFactor('mg', 'WEIGHT')); // Should be 0.001
console.log('g + WEIGHT:', getConversionFactor('g', 'WEIGHT')); // Should be 1
console.log('kg + WEIGHT:', getConversionFactor('kg', 'WEIGHT')); // Should be 1000

console.log('\n=== VOLUME ===');
console.log('ml + VOLUME:', getConversionFactor('ml', 'VOLUME')); // Should be 0.001
console.log('l + VOLUME:', getConversionFactor('l', 'VOLUME')); // Should be 1

console.log('\n=== COUNT ===');
console.log('tablet + COUNT:', getConversionFactor('tablet', 'COUNT')); // Should be 1
console.log('box + COUNT:', getConversionFactor('box', 'COUNT')); // Should be 10
console.log('strip + COUNT:', getConversionFactor('strip', 'COUNT')); // Should be 10

console.log('\n=== DOSAGE ===');
console.log('iu + DOSAGE:', getConversionFactor('iu', 'DOSAGE')); // Should be 1
console.log('unit + DOSAGE:', getConversionFactor('unit', 'DOSAGE')); // Should be 1
console.log('spray + DOSAGE:', getConversionFactor('spray', 'DOSAGE')); // Should be 1

console.log('\n=== CONCENTRATION ===');
console.log('mg/ml + CONCENTRATION:', getConversionFactor('mg/ml', 'CONCENTRATION')); // Should be 1
console.log('g/l + CONCENTRATION:', getConversionFactor('g/l', 'CONCENTRATION')); // Should be 1000
console.log('% + CONCENTRATION:', getConversionFactor('%', 'CONCENTRATION')); // Should be 10000

console.log('\n=== UNKNOWN ===');
console.log('unknown + WEIGHT:', getConversionFactor('unknown', 'WEIGHT')); // Should be null
